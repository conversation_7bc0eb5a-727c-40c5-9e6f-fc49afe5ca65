# Image Processing Service

## Requirements

* Java 17
    * Add the installation location to a `JAVA_HOME` envvar
    * Append the new `JAV<PERSON>_HOME\bin` var to the `PATH`
* Maven 3.8.6
* Docker
* Skaffold
* jib

## Building the Service

Run the following command in the base directory:

```shell
mvn clean install
```

## Running the Service

Run the following command:

```shell
./mvnw spring-boot:run
```

## Swagger

Go
to [http://localhost:9010/api/image-processing/swagger-ui/index.html](http://localhost:9010/api/image-processing/swagger-ui/index.htm)
to access the
Swagger page.

The default user is `user` and the password is a development one generated on startup. It can be found in the console.

## Additional Information

### Reference Documentation

For further reference, please consider the following sections:

* [Official Apache Maven documentation](https://maven.apache.org/guides/index.html)
* [Spring Boot Maven Plugin Reference Guide](https://docs.spring.io/spring-boot/docs/2.7.5/maven-plugin/reference/html/)
* [Create an OCI image](https://docs.spring.io/spring-boot/docs/2.7.5/maven-plugin/reference/html/#build-image)
* [OAuth2 Client](https://docs.spring.io/spring-boot/docs/2.7.5/reference/htmlsingle/#web.security.oauth2.client)
* [Spring for RabbitMQ](https://docs.spring.io/spring-boot/docs/2.7.5/reference/htmlsingle/#messaging.amqp)
* [Validation](https://docs.spring.io/spring-boot/docs/2.7.5/reference/htmlsingle/#io.validation)
* [Jersey](https://docs.spring.io/spring-boot/docs/2.7.5/reference/htmlsingle/#web.servlet.jersey)
* [Spring Boot DevTools](https://docs.spring.io/spring-boot/docs/2.7.5/reference/htmlsingle/#using.devtools)
* [Spring Data MongoDB](https://docs.spring.io/spring-boot/docs/2.7.5/reference/htmlsingle/#data.nosql.mongodb)

### Guides

The following guides illustrate how to use some features concretely:

* [Messaging with RabbitMQ](https://spring.io/guides/gs/messaging-rabbitmq/)
* [Validation](https://spring.io/guides/gs/validating-form-input/)
* [Accessing Data with MongoDB](https://spring.io/guides/gs/accessing-data-mongodb/)

# Permissions

https://scubeenterprise.atlassian.net/wiki/spaces/SD/pages/71073793/Adding+Keycloak+Permission