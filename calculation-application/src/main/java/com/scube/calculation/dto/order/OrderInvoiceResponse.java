package com.scube.calculation.dto.order;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.calculation.dto.cart.AdditionalFeeDto;
import com.scube.calculation.dto.cart.AdditionalItemFeeDto;
import com.scube.calculation.dto.fee.FeeDto;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;

@Getter
public class OrderInvoiceResponse implements Serializable {
    private final UUID orderId;
    private final String userId;
    private String status;
    private final Long createdDate;
    private final List<OrderInvoiceItem> items = new ArrayList<>();
    private final String orderNumber;
    private final Instant orderPaidDate;

    @Getter(AccessLevel.NONE)
    @JsonIgnore
    private List<OrderInvoiceItemFee> fees;
    private final List<AdditionalFeeDto> orderAdditionalFees;

    @JsonIgnore
    public List<OrderInvoiceItemFee> getFees() {
        if (fees != null && !ObjectUtils.isArray(fees)) return fees;

        // group fees by label and sum amounts
        fees = new ArrayList<>();
        var itemFees = items.stream().filter(x -> !"ADDITIONAL_FEE".equals(x.getItemType())).flatMap(i -> i.getFees().stream()).toList();
        for (var fee : itemFees) {
            var existingFee = fees.stream().filter(f -> f.getLabel().equals(fee.getLabel())).findFirst();
            if (existingFee.isPresent()) {
                existingFee.get().setAmount(existingFee.get().getAmount().add(fee.getAmount()));
            } else {
                fees.add(fee);
            }
        }
        return fees;
    }

    @JsonProperty("summary")
    public List<Object> getSummary() {
        BigDecimal discount = getDiscountedItems().stream()
                .map(OrderInvoiceItemFee::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<Object> summary = new ArrayList<>();
        orderAdditionalFees.forEach(f -> summary.add(Map.of("label", f.getLabel(), "amount", f.getPrice(), "items", new ArrayList<>())));
        summary.add(Map.of("label", "Discounts", "amount", discount, "items", getDiscountedItems()));
        return summary;
    }

    @JsonProperty("subtotal")
    public BigDecimal getSubtotal() {
        // The sum of all fees except for discounts
        return getFees().stream()
                .map(OrderInvoiceItemFee::getAmount)
                .filter(Objects::nonNull)
                .filter(f -> f.compareTo(BigDecimal.ZERO) > 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonProperty("total")
    public BigDecimal getTotal() {
        return getFees().stream()
                .map(OrderInvoiceItemFee::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add).add(orderAdditionalFees.stream().map(AdditionalFeeDto::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    @JsonIgnore
    public List<OrderInvoiceItemFee> getDiscountedItems() {
        return getFees().stream()
                .filter(x -> !ObjectUtils.isEmpty(x.getAmount()))
                .filter(f -> f.getAmount().compareTo(BigDecimal.ZERO) < 0)
                .toList();
    }

    public OrderInvoiceResponse(OrderDto order) {
        orderNumber = order.getOrderNumber();
        orderPaidDate = order.getOrderPaidDate();
        orderId = order.getOrderId();
        createdDate = order.getCreatedDate().toEpochMilli();
        if (order.getStatus() != null)
            status = order.getStatus().getKey();
        userId = order.getUserId();

        for (var item : order.getOrderItems()) {
            items.add(new OrderInvoiceItem(item));
        }
        orderAdditionalFees = order.getOrderAdditionalFees();
        for (var item : orderAdditionalFees) {
            items.add(new OrderInvoiceItem(item));
        }
    }


    @Data
    public static class OrderInvoiceItem {
        private Long orderItemId;
        private UUID itemId;
        private UUID orderItemUuid;
        private String itemType;
        private String primaryDisplay;
        private String secondaryDisplay;
        private Map<String, Object> properties;

        private List<OrderInvoiceItemFee> fees = new ArrayList<>();

        @JsonProperty("total")
        public BigDecimal getTotal() {
            return getFees().stream()
                    .map(OrderInvoiceItemFee::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        @JsonProperty("subtotal")
        public BigDecimal getSubtotal() {
            return getFees().stream()
                    .map(OrderInvoiceItemFee::getAmount)
                    .filter(Objects::nonNull)
                    .filter(f -> f.compareTo(BigDecimal.ZERO) > 0)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        @JsonProperty("discountedItems")
        public List<OrderInvoiceItemFee> getDiscountedItems() {
            return getFees().stream()
                    .filter(x -> !ObjectUtils.isEmpty(x.getAmount()))
                    .filter(f -> f.getAmount().compareTo(BigDecimal.ZERO) < 0)
                    .toList();
        }

        @JsonProperty("discount")
        public BigDecimal getSummary() {
            return getDiscountedItems().stream()
                    .map(OrderInvoiceItemFee::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        public OrderInvoiceItem(AdditionalFeeDto additionalFee) {
            orderItemId = additionalFee.getId();
            itemId = additionalFee.getUniqueItemId();
            orderItemUuid = additionalFee.getOrderAdditionalFeeUuid();
            itemType = additionalFee.getItemTypeId();
            primaryDisplay = additionalFee.getName();
            secondaryDisplay = additionalFee.getDescription();
            properties = additionalFee.getProperties();
            additionalFee.getFees().forEach(additionalItemFeeDto -> {
                fees.add(new OrderInvoiceItemFee(additionalItemFeeDto));
            });
        }

        public OrderInvoiceItem(OrderItemDto item) {
            orderItemId = item.getId();
            itemId = item.getUniqueItemId();
            orderItemUuid = item.getOrderItemUuid();
            itemType = item.getItemTypeId();
            primaryDisplay = item.getName();
            secondaryDisplay = item.getDescription();
            properties = item.getProperties();

            for (OrderItemFeeDto fee : item.getFees()) {
                fees.add(new OrderInvoiceItemFee(fee));
            }
        }

        @JsonProperty("stateFees")
        public BigDecimal stateFees() {
            if (fees == null) return BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
            return fees.stream()
                    .filter(f -> f.getFeeCode().endsWith("-S-ALT") || f.getFeeCode().endsWith("-S-UNALT"))
                    .map(OrderInvoiceItemFee::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.CEILING);
        }

        @JsonProperty("cityFees")
        public BigDecimal cityFees() {
            if (fees == null) return BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
            return getTotal()
                    .subtract(stateFees())
                    .setScale(2, RoundingMode.CEILING);
        }
    }

    @Data
    public static class OrderInvoiceItemFee implements Serializable {
        private String feeCode;
        private String label;
        private BigDecimal amount;
        private UUID orderItemFeeUuid;

        @JsonIgnore
        private Map<String, Object> properties;

        @JsonAnyGetter
        public Map<String, Object> getAdditionalProperties() {
            return properties;
        }

        public OrderInvoiceItemFee(OrderItemFeeDto orderItemFeeDto) {
            FeeDto fee = orderItemFeeDto.getFee();
            this.feeCode = fee.getKey();
            this.label = fee.getFeeName();
            this.orderItemFeeUuid = orderItemFeeDto.getOrderItemFeeUuid();
            this.amount = orderItemFeeDto.getPrice();
            this.properties = new HashMap<>(orderItemFeeDto.getProperties());
        }

        public OrderInvoiceItemFee(AdditionalItemFeeDto fee) {
            this.feeCode = fee.getFee().getKey();
            this.label = fee.getFee().getFeeName();
            this.orderItemFeeUuid = fee.getOrderAdditionalItemFeeUuid();
            this.amount = fee.getPrice();
            this.properties = new HashMap<>();
        }
    }
}
