package com.scube.calculation.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum FeeType {
    FLAT("FLAT"),
    PERCENTAGE("PERCENTAGE"),
    MANUAL("MANUAL");

    private final String value;

    FeeType(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static FeeType fromValue(String input) {
        for (FeeType b : FeeType.values()) {
            if (b.value.equals(input)) {
                return b;
            }
        }
        return null;
    }
}
