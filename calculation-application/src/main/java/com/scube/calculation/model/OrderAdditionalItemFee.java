package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.*;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;


@Getter
@Setter
@Entity
@Table(name = OrderAdditionalItemFee.TABLE_NAME)
@NoArgsConstructor
@Audited
@AllArgsConstructor
public class OrderAdditionalItemFee  extends AuditableEntity {
    public static final String TABLE_NAME = "order_additional_item_fee";
    @ManyToOne
    @JoinColumn(name = OrderAdditionalFee.ADDITIONAL_FEE_ID, nullable = false)
    private OrderAdditionalFee orderAdditionalFee;

    @ManyToOne
    @JoinColumn(name = Fee.FEE_ID, nullable = false)
    private Fee fee;

    @Getter(AccessLevel.NONE)
    private BigDecimal price;


    public BigDecimal calculatePrice() {
        BigDecimal orderTotal = this.getOrderAdditionalFee().getOrder().getOrderItems().stream().flatMap(orderItem -> orderItem.getOrderItemFees().stream()).map(OrderItemFee::calculatePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal amount = this.price == null ? fee.getAmount() : this.price;
        Map<String, Object> properties = this.getProperties();
        BigDecimal minFee = new BigDecimal(String.valueOf(properties.getOrDefault("minFee",0)));
        if (fee.getOperation() == null) return amount;
        var res = switch (fee.getOperation()) {
            case FLAT, MANUAL -> amount;
            case PERCENTAGE -> amount.multiply(orderTotal).divide(BigDecimal.valueOf(100));
        };
        if (res.compareTo(BigDecimal.ZERO) > 0) {
            res = res.setScale(2, RoundingMode.CEILING)
                    .max(minFee.setScale(2, RoundingMode.CEILING));
        }
        return res;
    }
}
