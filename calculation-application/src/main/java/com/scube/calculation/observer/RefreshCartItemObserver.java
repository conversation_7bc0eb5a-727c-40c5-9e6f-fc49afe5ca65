package com.scube.calculation.observer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.calculation.model.CartItem;
import com.scube.calculation.repository.CartItemRepository;
import com.scube.calculation.service.CartService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Component("refresh")
@RequiredArgsConstructor
public class RefreshCartItemObserver implements ICalculationObserver {
    private final CartService cartService;
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;
    private final CartItemRepository cartItemRepository;
    private CartRefreshEvent input;

    @Override
    public void update() {
        var cartItems = cartItemRepository.findByUniqueItemIdInAndItemTypeIdIn(input.entityIds, CartService.NON_UNIQUE_ITEMS_TYPE_ID).stream().filter(ci -> ObjectUtils.notEqual(input.currentCartId, ci.getCart().getId())).toList();
        Map<UUID, List<UUID>> cartIdToItemIds = cartItems.stream()
                .collect(Collectors.groupingBy(
                        ci -> ci.getCart().getId(),
                        Collectors.mapping(CartItem::getUniqueItemId, Collectors.toList())
                ));
        cartIdToItemIds.forEach(cartService::removeItemByItemIds);
        cartItems.forEach(cartItem -> amqpGateway.publish(new AddItemToCartEvent(cartItem.getItemTypeId(), cartItem.getUniqueItemId(), cartItem.getCart().getId(), false)));
    }

    @Override
    public <T> void setInput(T input) {
        this.input = objectMapper.convertValue(input, CartRefreshEvent.class);
    }

    @Data
    @NoArgsConstructor
    public static class CartRefreshEvent {
        private List<UUID> entityIds;
        private UUID currentCartId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddItemToCartEvent implements IRabbitFanoutPublisher {
        private String itemType;
        private UUID itemId;
        private UUID cartId;
        private boolean isMe;
    }
}
