package com.scube.calculation.repository;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.calculation.model.CartItem;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface CartItemRepository extends AuditableEntityRepository<CartItem, Long> {
    void deleteByCartId(UUID cartId);

    List<CartItem> findByUniqueItemId(UUID uniqueItemId);

    void deleteByUniqueItemId(UUID uniqueItemId);

    List<CartItem> findByCartIdAndUniqueItemId(UUID cartId, UUID uniqueItemId);
    List<CartItem> findByUniqueItemIdInAndItemTypeIdIn(List<UUID> uniqueItemIds, List<String> itemTypeIds);
}