package com.scube.calculation.repository;

import com.scube.audit.auditable.repositories.AuditableBaseWithPropertiesRepository;
import com.scube.calculation.enums.CartStatus;
import com.scube.calculation.model.Cart;
import jakarta.validation.constraints.Size;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface CartRepository extends AuditableBaseWithPropertiesRepository<Cart, UUID> {

    boolean existsByIdAndUserId(UUID cartId, @Size(max = 255) String userId);

    @Query("select c from Cart c where (?1 is null or c.userId = ?1)  and (c.status in ?2 or :#{#statuses} is null)")
    public Page<Cart> findFilteredPaginatedCarts(@Size(max = 255) String userId, List<CartStatus> statuses, Pageable pageable);

    @Modifying
    @Query("UPDATE Cart c SET c.status = :newStatus WHERE c.userId = :userId AND c.status in :statuses")
    void updateStatusForCarts(@Size(max = 255) String userId, List<CartStatus> statuses, CartStatus newStatus);

    @Query(value = """
            SELECT c FROM Cart c
            INNER JOIN CartItem ci ON c.id = ci.cart.id
            WHERE ci.uniqueItemId = :uniqueItemId
            AND cast(c.status as String) LIKE 'CART_%'
            """)
    List<Cart> findCartsByUniqueItemId(UUID uniqueItemId);

    @Query("""
    SELECT c FROM Cart c
    INNER JOIN CartItem ci ON c.id = ci.cart.id
    WHERE ci.uniqueItemId IN :uniqueItemIds
    AND cast(c.status as string) LIKE 'CART_%'
    """)
    List<Cart> findCartsByUniqueItemIds(List<UUID> uniqueItemIds);

    @Query("""
            SELECT c FROM Cart c
            JOIN c.cartItems ci
            WHERE ( COALESCE(:status, '') = '' OR c.status = :status)
            AND ( COALESCE(:startDate, '') = '' OR DATE(c.lastModifiedDate) >= :startDate)
            AND ( COALESCE(:endDate, '') = '' OR DATE(c.lastModifiedDate) <= :endDate)
            AND ( COALESCE(:cartItemName, '') = '' OR ci.name LIKE :cartItemName%)
            AND ( COALESCE(:cartItemId, '') = '' OR ci.itemTypeId = :cartItemId )
            ORDER BY c.lastModifiedDate 
            """)
    List<Cart> findCartsByFilters(CartStatus status,
                                  LocalDate startDate,
                                  LocalDate endDate,
                                  @Size(max = 255) String cartItemName,
                                  @Size(max = 255) String cartItemId);

    List<Cart> findByStatusAndUserIdOrderByCreatedDateDesc(CartStatus status, @Size(max = 255) String userId);

    default Cart getActiveCartByUserId(@Size(max = 255) String userId) {
        List<Cart> carts = findByStatusAndUserIdOrderByCreatedDateDesc(CartStatus.CART_ACTIVE, userId);
        if (carts.isEmpty())
            return null;

        return carts.get(0);
    }


    default List<Cart> findCartsByFilters(Map<String, String> queryParams) {
        CartStatus status = queryParams.get("status") == null ? null : CartStatus.valueOf(queryParams.get("status"));
        LocalDate startDate = queryParams.get("startDate") == null ? null : LocalDate.parse(queryParams.get("startDate"));
        LocalDate endDate = queryParams.get("endDate") == null ? null : LocalDate.parse(queryParams.get("endDate"));
        String cartItemName = queryParams.get("cartItemName");
        String cartItemId = queryParams.get("cartItemId");

        return findCartsByFilters(status, startDate, endDate, cartItemName, cartItemId);
    }
}