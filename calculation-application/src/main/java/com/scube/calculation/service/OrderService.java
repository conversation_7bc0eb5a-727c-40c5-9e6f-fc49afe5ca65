package com.scube.calculation.service;

import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.calculation.dto.AddItemRequest;
import com.scube.calculation.dto.GetOrderIDResponse;
import com.scube.calculation.dto.PageDTO;
import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.dto.order.OrderSummaryResponse;
import com.scube.calculation.enums.CartStatus;
import com.scube.calculation.enums.OrderStatus;
import com.scube.calculation.mapper.OrderMapper;
import com.scube.calculation.model.Cart;
import com.scube.calculation.model.Order;
import com.scube.calculation.model.OrderItem;
import com.scube.calculation.model.OrderItemFee;
import com.scube.calculation.repository.CartRepository;
import com.scube.calculation.repository.FeeRepository;
import com.scube.calculation.repository.OrderRepository;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.util.*;

@Service
@RequiredArgsConstructor
public class OrderService {
    private final CartRepository cartRepository;
    private final OrderRepository orderRepository;
    private final OrderMapper orderMapper;
    private final FeeRepository feeRepository;
    private final EntityManager entityManager;

    @Transactional
    public OrderInvoiceResponse getOrderInvoice(UUID id) {
        Order order = getOrderById(id);
        if (!Set.of(OrderStatus.ORDER_OPEN, OrderStatus.ORDER_LOCKED, OrderStatus.ORDER_PAID).contains(order.getStatus())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "The specified order does not exist or has been canceled");
        }
        return new OrderInvoiceResponse(orderMapper.toDto(order));
    }

    @Transactional
    public OrderInvoiceResponse getRefundableOrderInvoice(UUID id) {
        Order order = getOrderById(id);
        if (!Set.of(OrderStatus.ORDER_OPEN, OrderStatus.ORDER_LOCKED, OrderStatus.ORDER_PAID, OrderStatus.ORDER_PARTIALLY_REFUNDED).contains(order.getStatus())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "The specified order does not exist or has been canceled or fully refunded");
        }
        return new OrderInvoiceResponse(orderMapper.toDto(order));
    }

    //This is the method where I am getting the error
    @Transactional
    public OrderInvoiceResponse makeOrderFromCartWithResponse(UUID cartId) {
        Order order = makeOrderFromCart(cartId);

        return orderMapper.toOrderInvoiceResponse(order);
    }

    @Transactional
    public Order makeOrderFromCart(UUID cartId) {
        Cart cart = getCartById(cartId);

        if (!cart.getStatus().equals(CartStatus.CART_ACTIVE))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "An order can only be placed using an active cart");

        if (cart.getCartItems().isEmpty())
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "An order can only be placed using a non-empty cart");

        Order order = new Order(cart);

        order = orderRepository.save(order);

        entityManager.refresh(order);

        return order;
    }

    @Transactional
    public Cart makeCartFromOrder(UUID orderId) {
        Order order = getOrderById(orderId);

        if (!order.getStatus().equals(OrderStatus.ORDER_OPEN))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "A cart can only be created from an open order");

        Cart cart = cartRepository.save(new Cart(order));

        order.setStatus(OrderStatus.ORDER_CANCELED);

        orderRepository.save(order);

        return cart;
    }

    @Transactional
    public void transferOrder(UUID orderId, String userId) {
        Order order = getOrderById(orderId);
        order.setUserId(userId);
        orderRepository.save(order);
    }

    @Transactional
    public void cancelOrder(UUID id) {
        Order order = getOrderById(id);
        if (!Set.of(OrderStatus.ORDER_OPEN, OrderStatus.ORDER_LOCKED).contains(order.getStatus()))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Only an open or locked order can be canceled");
        order.setStatus(OrderStatus.ORDER_CANCELED);
        orderRepository.save(order);
    }

    @Transactional
    public Order getOrderById(UUID id) {
        return orderRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "No such order with id " + id));
    }

    @Transactional
    public Cart getCartById(UUID id) {
        return cartRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "No such cart with id " + id));
    }

    @Transactional
    public GetOrderIDResponse getOrderId(UUID uniqueItemId) {
        var orders = orderRepository.findOrderIdByItemId(uniqueItemId);
        if (ObjectUtils.isEmpty(orders))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No order found");

        return new GetOrderIDResponse(orders.get(0).toString());
    }

    @Transactional
    public PageDTO<OrderSummaryResponse> listRefundableOrders(String userId, String orderNumber, LocalDate startDate, LocalDate endDate, int pageNumber, int pageSize) {
        pageNumber -= 1; // The pagination should be 1-indexed, but Spring is 0-indexed by default

        LocalDate defaultStartDate = (startDate == null) ? LocalDate.of(1900, 1, 1) : startDate;
        LocalDate defaultEndDate = (endDate == null) ? LocalDate.now().plusDays(1) : endDate;

        List<OrderStatus> statuses = new ArrayList<>(List.of(
                OrderStatus.ORDER_PAID,
                OrderStatus.ORDER_PARTIALLY_REFUNDED
        ));

        Page<Order> orderPage = orderRepository.findFilteredPaginatedRefundableOrders(userId, orderNumber, defaultStartDate, defaultEndDate, statuses, PageRequest.of(pageNumber, pageSize, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending()));
        List<OrderSummaryResponse> summaries = orderPage.stream().map(x -> new OrderSummaryResponse(orderMapper.toDto(x))).toList();
        PageDTO<OrderSummaryResponse> response = new PageDTO<>();
        response.setItems(summaries);
        response.setPageIndex(orderPage.getNumber() + 1);
        response.setPageSize(orderPage.getSize());
        response.setTotalCount(orderPage.getTotalElements());
        return response;
    }

    @Transactional
    public PageDTO<OrderSummaryResponse> listOrders(String userId, String statusString, int pageNumber, int pageSize) {
        pageNumber -= 1; // The pagination should be 1-indexed, but Spring is 0-indexed by default
        List<OrderStatus> statuses;
        if (statusString == null) {
            statuses = OrderStatus.getAllStatuses();
        } else {
            var status = OrderStatus.valueOfIgnoreCase(statusString);
            if (status == null)
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid order status: " + statusString);
            statuses = List.of(status);
        }
        Page<Order> orderPage = orderRepository.findFilteredPaginatedOrders(userId, statuses, PageRequest.of(pageNumber, pageSize, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending()));
        List<OrderSummaryResponse> summaries = orderPage.stream().map(x -> new OrderSummaryResponse(orderMapper.toDto(x))).toList();
        PageDTO<OrderSummaryResponse> response = new PageDTO<>();
        response.setItems(summaries);
        response.setPageIndex(orderPage.getNumber() + 1);
        response.setPageSize(orderPage.getSize());
        response.setTotalCount(orderPage.getTotalElements());
        return response;
    }

    public void rollbackOrderToCart(UUID cartId, UUID orderId) {
        Order order = getOrderById(orderId);
        Cart cart = getCartById(cartId);

        if (!order.getStatus().equals(OrderStatus.ORDER_OPEN))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Only an open order can be rolled back to a cart");

        cart.setCartFromOrder(order);
        cart.setActive();

        order.setStatus(OrderStatus.ORDER_CANCELED);

        orderRepository.save(order);
        cartRepository.save(cart);
    }

    @Transactional
    public OrderInvoiceResponse makeOrderFromItems(String userId, List<AddItemRequest> request) {
        Order order = new Order(userId, toOrderItems(request));

        order = orderRepository.save(order);

        entityManager.refresh(order);

        return orderMapper.toOrderInvoiceResponse(order);
    }

    public List<OrderItem> toOrderItems(List<AddItemRequest> request) {
        return request.stream().map(this::requestToItem).toList();
    }

    public OrderItem requestToItem(AddItemRequest item) {
        OrderItem orderItem = new OrderItem(item);
        if (item.getFees() == null)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Fee list cannot be null");

        var itemFees = new ArrayList<OrderItemFee>();
        for (var feeRequest : item.getFees()) {
            var fee = feeRepository.findByKey(feeRequest.getFeeCode())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No such fee: " + feeRequest));
            itemFees.add(new OrderItemFee(orderItem, fee, feeRequest.getAmount()));
        }

        orderItem.setOrderItemFees(itemFees);
        return orderItem;
    }

    public List<OrderInvoiceResponse> findOrdersByFilters(Map<String, String> queryParams) {
        if (queryParams.isEmpty())
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "At least one filter is required");

        return orderRepository.findOrdersByFilters(queryParams).stream().map(x -> new OrderInvoiceResponse(orderMapper.toDto(x))).toList();
    }
}