package com.scube.calculation.service;

import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.calculation.SharedTestConfig;
import com.scube.calculation.dto.*;
import com.scube.calculation.enums.CartStatus;
import com.scube.calculation.enums.FeeType;
import com.scube.calculation.model.Cart;
import com.scube.calculation.model.CartItem;
import com.scube.calculation.model.Fee;
import com.scube.calculation.repository.CartItemRepository;
import com.scube.calculation.repository.CartRepository;
import com.scube.calculation.repository.FeeRepository;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@SpringBootTest
@ActiveProfiles("test")
public class CartServiceTest extends SharedTestConfig {

    @MockBean
    private CartRepository cartRepository;

    @MockBean
    private FeeRepository feeRepository;

    @MockBean
    private CartItemRepository cartItemRepository;

    @Autowired
    private CartService service;

    @Test
    void createCart() {
        when(cartRepository.findFilteredPaginatedCarts(eq("userId"), argThat(l -> l.contains(CartStatus.CART_ACTIVE)), any()))
                .thenReturn(Page.empty());

        var savedCart = new Cart("userId");

        ArgumentCaptor<Cart> captor = ArgumentCaptor.forClass(Cart.class);
        when(cartRepository.save(captor.capture())).thenReturn(savedCart);
        when(cartRepository.findById(any())).thenReturn(Optional.of(savedCart));

        CartInvoiceResponse response = service.createCart("userId");
        assertEquals(captor.getValue().getId(), response.getCartId());
    }

    @Test
    void addItem() {
        UUID cartId = UUID.randomUUID();
        var feeKey = new FeeRequest("FEE-KEY");
        String itemName = "Item Name";
        String itemDescription = "Item Description";

        Fee optionalFee = new Fee();
        optionalFee.setKey(feeKey.getFeeCode());

        AddItemRequest request = new AddItemRequest();
        request.setFees(List.of(feeKey));
        request.setBasePrice(BigDecimal.valueOf(10L));
        request.setName(itemName);
        request.setDescription(itemDescription);

        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);
        cart.setCartItems(new ArrayList<>());
        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));
        when(feeRepository.findByKey(feeKey.getFeeCode())).thenReturn(Optional.of(optionalFee));
        when(cartItemRepository.save(any(CartItem.class))).thenAnswer(i -> i.getArgument(0));

        service.addItem(cartId, request);

        ArgumentCaptor<Cart> captor = ArgumentCaptor.forClass(Cart.class);
        Mockito.verify(cartRepository).save(captor.capture());
        Cart savedCart = captor.getValue();
        assertEquals(1, savedCart.getCartItems().size());
        assertEquals(BigDecimal.valueOf(10L), savedCart.getCartItems().get(0).getPrice());
        assertEquals(itemName, savedCart.getCartItems().get(0).getName());
        assertEquals(itemDescription, savedCart.getCartItems().get(0).getDescription());
    }

    @Test
    void addItemInvalidStatus() {
        UUID cartId = UUID.randomUUID();
        var feeKey = new FeeRequest("FEE-KEY");
        String itemName = "Item Name";
        String itemDescription = "Item Description";

        Fee optionalFee = new Fee();
        optionalFee.setKey(feeKey.getFeeCode());

        AddItemRequest request = new AddItemRequest();
        request.setFees(List.of(feeKey));
        request.setBasePrice(BigDecimal.valueOf(10l));
        request.setName(itemName);
        request.setDescription(itemDescription);

        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_SUSPENDED);
        cart.setId(cartId);
        cart.setCartItems(new ArrayList<>());
        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.addItem(cartId, request));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("Items can only be added to an active cart", exception.getReason());
    }

    @Test
    void addItemNotFound() {
        var feeKey = new FeeRequest("FEE-KEY");
        String itemName = "Item Name";
        String itemDescription = "Item Description";

        Fee optionalFee = new Fee();
        optionalFee.setKey(feeKey.getFeeCode());

        AddItemRequest request = new AddItemRequest();
        request.setFees(List.of(feeKey));
        request.setBasePrice(BigDecimal.valueOf(10l));
        request.setName(itemName);
        request.setDescription(itemDescription);

        when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.addItem(UUID.randomUUID(), request));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void addItemInvalidFee() {
        UUID cartId = UUID.randomUUID();
        var feeKey = new FeeRequest("SOMETHING-INVALID");
        String itemName = "Item Name";
        String itemDescription = "Item Description";

        AddItemRequest request = new AddItemRequest();
        request.setFees(List.of(feeKey));
        request.setBasePrice(BigDecimal.valueOf(10l));
        request.setName(itemName);
        request.setDescription(itemDescription);

        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);
        cart.setCartItems(new ArrayList<>());
        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));
        when(feeRepository.findByKey(feeKey.getFeeCode())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.addItem(cartId, request));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("No such fee: SOMETHING-INVALID", exception.getReason());
    }

    @Test
    void getCartInvoiceWithAllPossibleFees() {
        UUID cartId = UUID.randomUUID();

        Fee additionalFlatFee = new Fee();
        additionalFlatFee.setId(4L);
        additionalFlatFee.setFeeName("Additional Flat Fee");
        additionalFlatFee.setAmount(new BigDecimal(2));
        additionalFlatFee.setOperation(FeeType.FLAT);
        additionalFlatFee.setPayableId(3);

        Fee additionalPercentFee = new Fee();
        additionalPercentFee.setId(5L);
        additionalPercentFee.setFeeName("Additional Percentage Fee");
        additionalPercentFee.setAmount(new BigDecimal(20));
        additionalPercentFee.setOperation(FeeType.PERCENTAGE);
        additionalPercentFee.setPayableId(4);

        Fee includedFlatFee = new Fee();
        includedFlatFee.setId(6L);
        includedFlatFee.setFeeName("Included Flat Fee");
        includedFlatFee.setAmount(new BigDecimal(3));
        includedFlatFee.setOperation(FeeType.FLAT);
        includedFlatFee.setPayableId(5);

        Fee includedPercentFee = new Fee();
        includedPercentFee.setId(7L);
        includedPercentFee.setFeeName("Included Percentage Fee");
        includedPercentFee.setAmount(new BigDecimal(8));
        includedPercentFee.setOperation(FeeType.PERCENTAGE);
        includedPercentFee.setPayableId(6);

        CartItem item1 = new CartItem();
        item1.setName("Item Name");
        item1.setDescription("Item Description");
        item1.setFees(List.of(additionalFlatFee, includedPercentFee));

        CartItem item2 = new CartItem();
        item2.setName("Item Name 2");
        item2.setDescription("Item Description 2");
        item2.setFees(List.of(includedFlatFee, additionalPercentFee));

        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);
        cart.setCartItems(List.of(item1, item2));
        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        CartInvoiceResponse response = service.getCartInvoice(cartId);

        MatcherAssert.assertThat(response.getItems(), containsInAnyOrder(
                allOf(
                        hasProperty("primaryDisplay", equalTo("Item Name")),
                        hasProperty("secondaryDisplay", equalTo("Item Description"))
                ),
                allOf(
                        hasProperty("primaryDisplay", equalTo("Item Name 2")),
                        hasProperty("secondaryDisplay", equalTo("Item Description 2"))
                )
        ));

        // Included fees are included in the list price of the item.
        // They should be counted in what's sent to payment service, but not listed on a separate line.
        var fees = response.getFees();
        MatcherAssert.assertThat(fees, containsInAnyOrder(
                allOf(
                        hasProperty("label", equalTo("Additional Flat Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(2l)))
                ),
                allOf(
                        hasProperty("label", equalTo("Additional Percentage Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(0.6)))
                ),
                allOf(
                        hasProperty("label", equalTo("Included Flat Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(3l)))
                ),
                allOf(
                        hasProperty("label", equalTo("Included Percentage Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(0.16)))
                )
        ));
    }

    @Test
    void getCartInvoiceNotFound() {
        when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.getCartInvoice(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void getPreviewInvoice() {
        Fee additionalFlatFee = new Fee();
        additionalFlatFee.setId(4L);
        additionalFlatFee.setKey("FEE-FLAT-1");
        additionalFlatFee.setFeeName("Additional Flat Fee");
        additionalFlatFee.setAmount(new BigDecimal(2));
        additionalFlatFee.setOperation(FeeType.FLAT);
        additionalFlatFee.setPayableId(3);

        Fee additionalPercentFee = new Fee();
        additionalPercentFee.setId(5L);
        additionalPercentFee.setKey("FEE-PERCENT-1");
        additionalPercentFee.setFeeName("Additional Percentage Fee");
        additionalPercentFee.setAmount(new BigDecimal(20));
        additionalPercentFee.setOperation(FeeType.PERCENTAGE);
        additionalPercentFee.setPayableId(4);

        Fee includedFlatFee = new Fee();
        includedFlatFee.setId(6L);
        includedFlatFee.setKey("FEE-FLAT-2");
        includedFlatFee.setFeeName("Included Flat Fee");
        includedFlatFee.setAmount(new BigDecimal(3));
        includedFlatFee.setOperation(FeeType.FLAT);
        includedFlatFee.setPayableId(5);

        Fee includedPercentFee = new Fee();
        includedPercentFee.setId(7L);
        includedPercentFee.setKey("FEE-PERCENT-2");
        includedPercentFee.setFeeName("Included Percentage Fee");
        includedPercentFee.setAmount(new BigDecimal(8));
        includedPercentFee.setOperation(FeeType.PERCENTAGE);
        includedPercentFee.setPayableId(6);

        AddItemRequest item1 = new AddItemRequest();
        item1.putFeeCodes(List.of(additionalFlatFee.getKey(), includedPercentFee.getKey()));
        item1.setName("First Item Name");
        item1.setDescription("First Item Description");

        AddItemRequest item2 = new AddItemRequest();
        item2.putFeeCodes(List.of(includedFlatFee.getKey(), additionalPercentFee.getKey()));
        item2.setName("Second Item Name");
        item2.setDescription("Second Item Description");

        when(feeRepository.findByKey(additionalFlatFee.getKey())).thenReturn(Optional.of(additionalFlatFee));
        when(feeRepository.findByKey(includedPercentFee.getKey())).thenReturn(Optional.of(includedPercentFee));
        when(feeRepository.findByKey(additionalPercentFee.getKey())).thenReturn(Optional.of(additionalPercentFee));
        when(feeRepository.findByKey(includedFlatFee.getKey())).thenReturn(Optional.of(includedFlatFee));

        CartInvoiceResponse response = service.getPreviewInvoice(List.of(item1, item2));

        MatcherAssert.assertThat(response.getItems(), containsInAnyOrder(
                allOf(
                        hasProperty("primaryDisplay", equalTo("First Item Name")),
                        hasProperty("secondaryDisplay", equalTo("First Item Description"))
                ),
                allOf(
                        hasProperty("primaryDisplay", equalTo("Second Item Name")),
                        hasProperty("secondaryDisplay", equalTo("Second Item Description"))
                )
        ));

        // Included fees are included in the list price of the item.
        // They should be counted in what's sent to payment service, but not listed on a separate line.
        MatcherAssert.assertThat(response.getFees(), containsInAnyOrder(
                allOf(
                        hasProperty("label", equalTo("Additional Flat Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(2l)))
                ),
                allOf(
                        hasProperty("label", equalTo("Additional Percentage Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(0.6)))
                ),
                allOf(
                        hasProperty("label", equalTo("Included Flat Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(3l)))
                ),
                allOf(
                        hasProperty("label", equalTo("Included Percentage Fee")),
                        hasProperty("feeAmount", comparesEqualTo(BigDecimal.valueOf(0.16)))
                )
        ));
    }

    @Test
    void getCartSummaryWithAllPossibleFees() {
        UUID cartId = UUID.randomUUID();

        Fee additionalFlatFee = new Fee();
        additionalFlatFee.setAmount(new BigDecimal(2));
        additionalFlatFee.setOperation(FeeType.FLAT);
        additionalFlatFee.setPayableId(3);

        Fee additionalPercentFee = new Fee();
        additionalPercentFee.setAmount(new BigDecimal(20));
        additionalPercentFee.setOperation(FeeType.PERCENTAGE);
        additionalPercentFee.setPayableId(4);

        Fee includedFlatFee = new Fee();
        includedFlatFee.setAmount(new BigDecimal(3));
        includedFlatFee.setOperation(FeeType.FLAT);
        includedFlatFee.setPayableId(5);

        Fee includedPercentFee = new Fee();
        includedPercentFee.setAmount(new BigDecimal(8));
        includedPercentFee.setOperation(FeeType.PERCENTAGE);
        includedPercentFee.setPayableId(6);

        CartItem item = new CartItem();
        item.setPrice(new BigDecimal(50));
        item.setFees(List.of(additionalFlatFee, additionalPercentFee, includedFlatFee, includedPercentFee));

        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);
        cart.setCartItems(List.of(item));
        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        PayableSummaryResponse response = service.getCartSummary(cartId);

        var fees = response.getItems();
        MatcherAssert.assertThat(fees, containsInAnyOrder(
                allOf(
                        hasProperty("payableId", equalTo(3)),
                        hasProperty("amount", comparesEqualTo(new BigDecimal(2)))
                ),
                allOf(
                        hasProperty("payableId", equalTo(4)),
                        hasProperty("amount", comparesEqualTo(new BigDecimal("10.0")))
                ),
                allOf(
                        hasProperty("payableId", equalTo(5)),
                        hasProperty("amount", comparesEqualTo(new BigDecimal(3)))
                ),
                allOf(
                        hasProperty("payableId", equalTo(6)),
                        hasProperty("amount", comparesEqualTo(new BigDecimal(4)))
                ),
                allOf(
                        hasProperty("payableId", equalTo(0)),
                        hasProperty("amount", comparesEqualTo(new BigDecimal(50)))
                )
        ));
    }

    @Test
    void getCartSummaryNotFound() {
        when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.getCartSummary(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void listCarts() {
        String userId = "userId";

        CartItem item1 = new CartItem();
        item1.setName("First Item");
        CartItem item2 = new CartItem();
        item2.setName("Second Item");
        CartItem item3 = new CartItem();
        item3.setName("Third Item");
        CartItem item4 = new CartItem();
        item4.setName("Fourth Item");

        UUID cartId1 = UUID.randomUUID();
        Instant date1 = Instant.ofEpochMilli(0);
        Cart cart1 = new Cart();
        cart1.setId(cartId1);
        cart1.setUserId(userId);
        cart1.setStatus(CartStatus.CART_ACTIVE);
        cart1.setCreatedDate(date1);
        cart1.setCartItems(List.of(item1, item2));

        UUID cartId2 = UUID.randomUUID();
        Instant date2 = Instant.ofEpochMilli(1000 * 60 * 60 * 24);
        Cart cart2 = new Cart();
        cart2.setId(cartId2);
        cart2.setUserId(userId);
        cart2.setStatus(CartStatus.CART_ACTIVE);
        cart2.setCreatedDate(date2);
        cart2.setCartItems(List.of(item3, item4));

        when(cartRepository.findFilteredPaginatedCarts(userId, List.of(CartStatus.CART_ACTIVE), PageRequest.of(0, 2, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending())))
                .thenReturn(
                        PageableExecutionUtils.getPage(List.of(cart1, cart2), PageRequest.of(0, 2, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending()), () -> 2)
                );

        PageDTO<CartSummaryResponse> response = service.listCarts(userId, List.of("cartActive"), 1, 2);
        assertEquals(1, response.getPageIndex());
        assertEquals(2, response.getPageSize());
        assertEquals(2, response.getTotalCount());
        MatcherAssert.assertThat(response.getItems(), containsInAnyOrder(
                allOf(
                        hasProperty("cartId", equalTo(cartId1.toString())),
                        hasProperty("status", equalTo("cartActive")),
                        hasProperty("createdDate", equalTo(date1)),
                        hasProperty("itemNames", containsInAnyOrder(
                                equalTo("First Item"),
                                equalTo("Second Item")
                        ))
                ),
                allOf(
                        hasProperty("cartId", equalTo(cartId2.toString())),
                        hasProperty("status", equalTo("cartActive")),
                        hasProperty("createdDate", equalTo(date2)),
                        hasProperty("itemNames", containsInAnyOrder(
                                equalTo("Third Item"),
                                equalTo("Fourth Item")
                        ))
                )
        ));
    }

    @Test
    void listCartsDefaultStatuses() {
        String userId = "userId";

        CartItem item1 = new CartItem();
        item1.setName("First Item");
        CartItem item2 = new CartItem();
        item2.setName("Second Item");
        CartItem item3 = new CartItem();
        item3.setName("Third Item");
        CartItem item4 = new CartItem();
        item4.setName("Fourth Item");

        UUID cartId1 = UUID.randomUUID();
        Instant date1 = Instant.ofEpochMilli(0);
        Cart cart1 = new Cart();
        cart1.setId(cartId1);
        cart1.setUserId(userId);
        cart1.setStatus(CartStatus.CART_ACTIVE);
        cart1.setCreatedDate(date1);
        cart1.setCartItems(List.of(item1, item2));

        UUID cartId2 = UUID.randomUUID();
        Instant date2 = Instant.ofEpochMilli(1000 * 60 * 60 * 24);
        Cart cart2 = new Cart();
        cart2.setId(cartId2);
        cart2.setUserId(userId);
        cart2.setStatus(CartStatus.CART_ACTIVE);
        cart2.setCreatedDate(date2);
        cart2.setCartItems(List.of(item3, item4));

        when(cartRepository.findFilteredPaginatedCarts(userId, List.of(CartStatus.CART_ACTIVE, CartStatus.CART_SUSPENDED), PageRequest.of(0, 2, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending())))
                .thenReturn(
                        PageableExecutionUtils.getPage(List.of(cart1, cart2), PageRequest.of(0, 2, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending()), () -> 2)
                );

        PageDTO<CartSummaryResponse> response = service.listCarts(userId, null, 1, 2);
        assertEquals(1, response.getPageIndex());
        assertEquals(2, response.getPageSize());
        assertEquals(2, response.getTotalCount());
        MatcherAssert.assertThat(response.getItems(), containsInAnyOrder(
                allOf(
                        hasProperty("cartId", equalTo(cartId1.toString())),
                        hasProperty("status", equalTo("cartActive")),
                        hasProperty("createdDate", equalTo(date1)),
                        hasProperty("itemNames", containsInAnyOrder(
                                equalTo("First Item"),
                                equalTo("Second Item")
                        ))
                ),
                allOf(
                        hasProperty("cartId", equalTo(cartId2.toString())),
                        hasProperty("status", equalTo("cartActive")),
                        hasProperty("createdDate", equalTo(date2)),
                        hasProperty("itemNames", containsInAnyOrder(
                                equalTo("Third Item"),
                                equalTo("Fourth Item")
                        ))
                )
        ));
    }

    @Test
    void listCartsInvalidStatus() {
        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.listCarts("userId", List.of("invalid status"), 1, 2));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("Invalid cart status: invalid status", exception.getReason());
    }

    @Test
    void removeItemFromCart() {
        Instant testStart = Instant.now().minusSeconds(1);

        UUID cartId = UUID.randomUUID();
        String userId = "userId";

        CartItem item1 = new CartItem();
        item1.setId(1L);
        item1.setName("First Item");
        CartItem item2 = new CartItem();
        item2.setName("Second Item");

        Instant date1 = Instant.ofEpochMilli(0);
        Cart cart = new Cart();
        cart.setId(cartId);
        cart.setUserId(userId);
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setCreatedDate(date1);
        cart.setCartItems(List.of(item1, item2));

        item1.setCart(cart);

        when(cartItemRepository.findById(1L)).thenReturn(Optional.of(item1));

        service.removeItem(cartId, 1);

        Mockito.verify(cartItemRepository).delete((CartItem) argThat((i -> ((CartItem) i).getId() == 1)));
    }

    @Test
        // Try to remove an item from a cart, when the specified item is not in the specified cart.
    void removeItemFromCartCartMismatch() {
        UUID cartId = UUID.randomUUID();
        String userId = "userId";

        CartItem item1 = new CartItem();
        item1.setId(1L);
        item1.setName("First Item");

        Instant date1 = Instant.ofEpochMilli(0);
        Cart cart = new Cart();
        cart.setId(cartId);
        cart.setUserId(userId);
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setCreatedDate(date1);
        cart.setCartItems(List.of(item1));

        item1.setCart(cart);

        when(cartItemRepository.findById(1L)).thenReturn(Optional.of(item1));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.removeItem(UUID.randomUUID(), 1));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void switchCart() {
        Instant testStart = Instant.now().minusMillis(10);
        String userId = "User ID";

        UUID newCartId = UUID.randomUUID();
        Cart newCart = new Cart();
        newCart.setId(newCartId);
        newCart.setStatus(CartStatus.CART_SUSPENDED);

        when(cartRepository.findById(newCartId)).thenReturn(Optional.of(newCart));

        ArgumentCaptor<Cart> cartCaptor = ArgumentCaptor.forClass(Cart.class);
        when(cartRepository.save(cartCaptor.capture())).thenReturn(newCart);

        service.switchCart(newCartId, userId);

        List<Cart> savedCarts = cartCaptor.getAllValues();

        assertEquals(newCartId, savedCarts.get(0).getId());
        assertEquals(CartStatus.CART_ACTIVE, savedCarts.get(0).getStatus());
        assertTrue(savedCarts.get(0).getLastModifiedDate().isAfter(testStart));
        assertEquals(userId, savedCarts.get(0).getUserId());
    }

    @Test
    void switchCartInvalid() {
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setId(cartId);
        cart.setStatus(CartStatus.CART_ACTIVE);

        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.switchCart(cartId, "User ID"));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("Only suspended carts can be activated", exception.getReason());
        Mockito.verify(cartRepository).findById(cartId);
        Mockito.verifyNoMoreInteractions(cartRepository);
    }

    @Test
    void switchCartNotFound() {
        when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.switchCart(UUID.randomUUID(), "userId"));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void deleteCartValid() {
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);
        cart.setCartItems(List.of());

        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        service.deleteCart(cartId);

        Mockito.verify(cartRepository).delete((Cart) argThat(c -> ((Cart) c).getId().equals(cartId)));
    }

    @Test
    void deleteCartNotEmpty() {
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);
        cart.setCartItems(List.of(new CartItem()));

        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.deleteCart(cartId));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("The specified cart cannot be deleted, as it contains items", exception.getReason());
    }

    @Test
    void deleteCartNotFound() {
        when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.deleteCart(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void clearCart() {
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setId(cartId);
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setCartItems(List.of());

        when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        service.clearCart(cartId);

        Mockito.verify(cartItemRepository).deleteAllInBatch(any());
    }

    @Test
    void clearCartNotFound() {
        when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.clearCart(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void removeItem() {
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setId(cartId);

        UUID itemId = UUID.randomUUID();
        CartItem cartItem = new CartItem();
        cartItem.setId(1L);
        cartItem.setUniqueItemId(itemId);

        cart.addItem(cartItem);

        when(cartItemRepository.findByCartIdAndUniqueItemId(cartId, itemId)).thenReturn(List.of(cartItem));
        when(cartItemRepository.findById(cartItem.getId())).thenReturn(Optional.of(cartItem));

        service.removeItem(cartId, itemId);

        Mockito.verify(cartItemRepository).delete(cartItem);
    }
}