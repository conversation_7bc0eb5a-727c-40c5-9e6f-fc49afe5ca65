package com.scube.config.app_property.db;

import com.scube.config.app_property.rabbit.AppPropertyChangedEvent;
import com.scube.rabbit.core.AmqpGateway;
import jakarta.persistence.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class AppPropertyPublishChangedEvent {
    private final AmqpGateway amqpGateway;

    @PrePersist
    @PostPersist
    @PreUpdate
    @PostUpdate
    @PreRemove
    @PostRemove
    public void publish(AppProperty appProperty) {
        log.info("Publishing changed event for app property");
        amqpGateway.publish(new AppPropertyChangedEvent());
    }
}