package com.scube.config.sql_storage.db;

import com.scube.config.sql_storage.rabbit.SqlStorageChangedEvent;
import com.scube.rabbit.core.AmqpGateway;
import jakarta.persistence.PostPersist;
import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SqlStoragePublishChangedEventEntityListener {
    private final AmqpGateway amqpGateway;

    @PostPersist
    @PostUpdate
    @PostRemove
    public void publish(SqlStorage sqlStorage) {
        log.info("Publishing changed event for sql storage");
        amqpGateway.publish(new SqlStorageChangedEvent());
    }
}