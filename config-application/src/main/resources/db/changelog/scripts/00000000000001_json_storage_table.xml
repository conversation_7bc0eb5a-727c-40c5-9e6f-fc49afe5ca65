<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="json_storage_createTable" author="David">
        <createTable tableName="json_storage">
            <column autoIncrement="true" name="json_storage_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" unique="true"/>
            </column>
            <column name="json_storage_uuid" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="properties" type="JSONB">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="created_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="json_data" type="JSONB">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="audit_log_json_storage_createTable2" author="David">
        <createTable tableName="audit_log_json_storage">
            <column name="json_storage_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revision_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revision_type" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="json_storage_uuid" type="UUID"/>
            <column name="properties" type="JSONB"/>
            <column name="last_modified_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="json_data" type="JSONB">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-26">
        <createTable tableName="audit_log_revision">
            <column name="audit_log_revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_revision_pkey"/>
            </column>
            <column name="timestamp" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="auditor" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="audit_log_audit_log_revision_seq">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="audit_log_revision_seq" startValue="1"/>
    </changeSet>
    <changeSet id="addDefault_json_storage_json_storage_uuid" author="David">
        <addDefaultValue columnName="json_storage_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="json_storage"/>
    </changeSet>
</databaseChangeLog>