package com.scube.coordinator.features.demo_email;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.config_utils.app_property.AppPropertyValue;
import com.scube.coordinator.features.notification.GenerateTextCommand;
import com.scube.multi.tenant.tenancy.switch_tenant.SwitchTenant;
import com.scube.notification.client.model.Email;
import com.scube.notification.client.rabbit.ScheduleEmailFanoutEvent;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

@Service
@RequiredArgsConstructor
public class DemoEmailService {
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;

    @AppPropertyValue
    private DemoEmailProperties demoEmailProperties;

    @SwitchTenant(useAdminToken = true)
    public void sendDemoEmail(DemoEmailRequest request) {
        JsonNode unFormattedDataString = objectMapper.valueToTree(request.getData());
        if (ObjectUtils.isEmpty(unFormattedDataString))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to convert request to json");

        var emailTemplateName = "demoEmailBody";
        var emailSubjectTemplateName = "demoEmailSubject";

        var emailBody = amqpGateway.queryResult(new GenerateTextCommand(emailTemplateName, unFormattedDataString)).orElseThrow();
        var emailSubject = amqpGateway.queryResult(new GenerateTextCommand(emailSubjectTemplateName, unFormattedDataString)).orElseThrow();

        amqpGateway.publish(
                ScheduleEmailFanoutEvent.builder()
                        .tag("DemoEmail")
                        .topic("email")
                        .createdBy("CoordinatorService")
                        .email(
                                Email.builder()
                                        .to(demoEmailProperties.getTo())
                                        .from("<EMAIL>")
                                        .cc(demoEmailProperties.getCc())
                                        .bcc(demoEmailProperties.getBcc())
                                        .subject(emailSubject)
                                        .body(emailBody)
                                        .contentType("text/html")
                                        .build()
                        ).build()
        );
    }
}
