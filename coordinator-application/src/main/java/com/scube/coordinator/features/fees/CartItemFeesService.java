package com.scube.coordinator.features.fees;

import com.scube.calculation.dto.gen_dto.CartInvoiceItem;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.coordinator.features.cart.CartService;
import com.scube.coordinator.features.fees.dto.AddFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.RemoveFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.UpdateFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.strategies.ICartItemStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class CartItemFeesService {
    private final CalculationServiceConnection calculationServiceConnection;
    private final Map<String, ICartItemStrategy> strategies;
    private final CartService cartService;

    public Object updateFeeOnCartItem(Long cartItemId, UpdateFeeOnCartItemRequest request) {
        CartInvoiceItem cartItem = getCartItem(cartItemId);
        ICartItemStrategy strategy = getiCartItemStrategy(cartItem.getItemType(), cartItemId);
        return strategy.updateFee(cartItem, request);
    }

    public Object addFeeOnCartItem(Long cartItemId, AddFeeOnCartItemRequest request) {
        CartInvoiceItem cartItem = getCartItem(cartItemId);
        ICartItemStrategy strategy = getiCartItemStrategy(cartItem.getItemType(), cartItemId);
        return strategy.addFee(cartItem, request);
    }

    public Object removeFeeOnCartItem(Long cartItemId, RemoveFeeOnCartItemRequest request) {
        CartInvoiceItem cartItem = getCartItem(cartItemId);
        ICartItemStrategy strategy = getiCartItemStrategy(cartItem.getItemType(), cartItemId);
        return strategy.removeFee(cartItem, request);
    }

    @NonNull
    private ICartItemStrategy getiCartItemStrategy(String itemType, @NonNull Long cartItemId) {
        var strategyName = "%sCartItemStrategy".formatted(itemType);
        if (!strategies.containsKey(strategyName)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Strategy not found for cart item type: %s".formatted(itemType));
        }
        var strategy = strategies.get(strategyName);
        if (ObjectUtils.isEmpty(strategy)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Not Supported cart item type: %s".formatted(itemType));
        }
        return strategy;
    }

    @NotNull
    private CartInvoiceItem getCartItem(Long cartItemId) {
        CartInvoiceItem cartItem = calculationServiceConnection.cart().getCartItem(cartItemId);
        if (ObjectUtils.isEmpty(cartItem)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cart item not found for id: %s".formatted(cartItemId));
        }
        if (ObjectUtils.isEmpty(cartItem.getItemType())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Cart item type not found for id: %s".formatted(cartItemId));
        }
        return cartItem;
    }
}