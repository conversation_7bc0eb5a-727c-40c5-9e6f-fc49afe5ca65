package com.scube.coordinator.features.fees.dto;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddFeeOnCartItemRequest {
    @NotEmpty
    private String feeCode;
    @Min(0)
    private BigDecimal feeAmount;
    private String reason;

    @JsonAnySetter
    private Map<String, Object> additionalFields = new HashMap<>();
}