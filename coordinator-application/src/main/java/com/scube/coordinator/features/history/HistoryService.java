package com.scube.coordinator.features.history;

import com.scube.auth.library.ITokenService;
import com.scube.client.payment.generated.PaymentServiceConnection;
import com.scube.coordinator.features.history.rabbit.GetOrderIdByItemIdQuery;
import com.scube.payment.features.payment.processing.dto.gen_dto.GetPaymentResponseDto;
import com.scube.rabbit.core.AmqpGateway;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@Slf4j
@AllArgsConstructor
public class HistoryService {
    private final PaymentServiceConnection paymentServiceConnection;
    private final AmqpGateway amqpGateway;
    private final ITokenService tokenService;

    /**
     * Retrieves any transactions associated with a specific entityId.
     *
     * @return
     */
    public List<GetPaymentResponseDto> getTransactionsByEntityId(UUID entityId, boolean isMe) {
        log.info("Retrieving history for entity {}", entityId);

        UUID orderId = getOrderId(entityId);

        if (isMe) {
            return paymentServiceConnection.loggedInUser().getPaymentsByOrderId(orderId);
        } else {
            return paymentServiceConnection.payment().getPaymentsByOrderId(orderId);
        }
    }

    public UUID getOrderId(UUID entityId) {
        var rabbitResult = amqpGateway.queryResult(new GetOrderIdByItemIdQuery(entityId));

        if (rabbitResult == null) {
            throw new HistoryServiceException(
                    "Failed to get orderId from calculation service: Rabbit result was null");
        } else if (!rabbitResult.isSuccess()) {
            throw new HistoryServiceException(
                    "Failed to get orderId from calculation service with error: " + rabbitResult.getErrorMessage());
        }

        return rabbitResult.getResult().getOrderId();
    }

    private static final class HistoryServiceException extends RuntimeException {
        public HistoryServiceException(String message) {
            super(message);
        }
    }
}
