package com.scube.coordinator.features.participant;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.auth.library.ITokenService;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.contact_us.GetGeneratedCodeQuery;
import com.scube.coordinator.features.contact_us.GetGeneratedCodeQueryResponse;
import com.scube.coordinator.features.notification.TenantService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.RabbitResult;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ParticipantService {
    private final LicenseServiceConnection licenseServiceConnection;
    private final ITokenService tokenService;
    private final TenantService tenantService;
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;

    public JsonNode getParticipant(UUID participantEntityId) {
        var token = tokenService.getNewAccessTokenFromCurrentRealm();
        return licenseServiceConnection.profile().getIndividualEntityAndAllAssociations(participantEntityId, token);
    }

    public JsonNode getParticipantForEmail(UUID participantEntityId) {
        var participant = getParticipant(participantEntityId);
        var tenant = tenantService.getTenant();
        ((ObjectNode) participant).put("tenant", objectMapper.valueToTree(tenant));
        ((ObjectNode) participant).put("residentCode", getResidentCode(participantEntityId));
        return participant;
    }

    public String getResidentCode(UUID participantEntityId) {
        RabbitResult<GetGeneratedCodeQueryResponse> code = amqpGateway.queryResult(new GetGeneratedCodeQuery("individual", String.valueOf(participantEntityId)));
        if (ObjectUtils.isEmpty(code) || ObjectUtils.isEmpty(code.getResult()) || ObjectUtils.isEmpty(code.getResult().code()))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Unable to generate resident code");
        return code.getResult().code();
    }
}