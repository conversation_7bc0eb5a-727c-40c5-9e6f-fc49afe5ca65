package com.scube.coordinator.features.payment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@CheckPayeeNameOrBusinessPresent
public class CoordinatorPayeeDto {
    private String firstName; // validated by CheckPayeeNameOrBusinessPresent
    private String lastName; // validated by CheckPayeeNameOrBusinessPresent
    private String businessName; // validated by CheckPayeeNameOrBusinessPresent

    //@NotBlank(message = "Payee email is required")
    @Email(message = "Payee email is invalid")
    private String email;

    @NotBlank(message = "Phone number is required")
    private String phone;

    @NotBlank(message = "Mailing address is required")
    private String mailingAddress;
    private String mailingAddress2;

    @NotBlank(message = "Mailing city is required")
    private String mailingCity;

    @NotBlank(message = "Mailing state is required")
    private String mailingState;

    @NotBlank(message = "Mailing zip code is required")
    private String mailingZipCode;

    private boolean billingSameAsMailing = true;
    private String billingAddress;
    private String billingAddress2;
    private String billingCity;
    private String billingState;
    private String billingZipCode;
}
