package com.scube.coordinator.features.templates;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.permission.Permissions;
import com.scube.coordinator.features.templates.dto.GetAllTemplates;
import com.scube.coordinator.features.templates.dto.Template;
import com.scube.coordinator.features.templates.dto.UpsertTemplateRequest;
import jakarta.annotation.security.RolesAllowed;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("templates")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
@Validated
public class TemplatesController {
    private final TemplatesService service;
    private final ObjectMapper objectMapper;

    @GetMapping("all")
    @RolesAllowed(Permissions.Templates.GET_ALL_TEMPLATES)
    public GetAllTemplates getAllTemplates() {
        return service.getAllTemplates();
    }


    @GetMapping("{templateUUID}")
    @RolesAllowed(Permissions.Templates.GET_TEMPLATE)
    public Template getTemplate(@PathVariable UUID templateUUID) {
        return service.getTemplate(templateUUID);
    }

    @PutMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Templates.UPSERT_TEMPLATE)
    public void upsertTemplate(@RequestParam Map<String, String> params, @RequestParam Map<String, MultipartFile> files) throws JsonProcessingException {
        var processedParams = new HashMap<String, Object>();
        params.forEach((key, value) -> {
            try {
                Object json = objectMapper.readTree(value);
                processedParams.put(key, json);
            } catch (JsonProcessingException e) {
                processedParams.put(key, value);
            }
        });
        var paramJson = objectMapper.writeValueAsString(processedParams);
        UpsertTemplateRequest request = objectMapper.readValue(paramJson, UpsertTemplateRequest.class);
        if (!ObjectUtils.isEmpty(files)) {
            var file = files.values().stream()
                    .findFirst()
                    .orElse(null);
            request.setFile(file);
        }
        service.upsertTemplate(request);
    }
}