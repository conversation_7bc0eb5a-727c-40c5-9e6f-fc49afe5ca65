package com.scube.coordinator.features.text_extractor.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.permission.Permissions;
import com.scube.coordinator.features.text_extractor.service.ITextExtractorService;
import com.scube.lib.misc.annotations.validation.NoValidation;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("me/textextractor")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
@Validated
public class LoggedInUserTextExtractorController {
    private final ITextExtractorService service;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserTextExtractor.PROCESS_FILE)
    public Object processFile(@RequestParam("fileIdentifier") @Size(max = 255) String fileIdentifier,
                              @RequestPart("file") final MultipartFile file) {
        log.info("LoggedInUserTextExtractorController.processFile()");
        return service.processFile(file, fileIdentifier, null, true);
    }

    @PostMapping(path = "/merge", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserTextExtractor.MERGE)
    public String merge(@RequestParam MultiValueMap<String, MultipartFile> pairs,
                        @NoValidation @RequestPart(value="schema",required = false) String schema,
                        @NoValidation @RequestParam(value="promptKey",required = false) String promptKey) {
        log.debug("LoggedInUserTextExtractorController.merge()");
        return service.merge(pairs, schema, promptKey, true);
    }
}