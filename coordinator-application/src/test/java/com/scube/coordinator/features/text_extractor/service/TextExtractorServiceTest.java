package com.scube.coordinator.features.text_extractor.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.ai.dto.gen_dto.MapTextRequest;
import com.scube.ai.dto.gen_dto.MapTextResponse;
import com.scube.ai.generated.IMapTextControllerHttpExchangeProxy;
import com.scube.client.ai.generated.AiServiceConnection;
import com.scube.client.imageProcessing.generated.ImageProcessingServiceConnection;

import com.scube.imageprocessing.dto.gen_dto.ExtractTextFromImageResponse;
import com.scube.imageprocessing.generated.IImageProcessingControllerHttpExchangeProxy;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TextExtractorServiceTest {
    @Mock
    private AiServiceConnection aiServiceConnection;

    @Mock
    private ImageProcessingServiceConnection imageProcessing;

    @Mock
    private IImageProcessingControllerHttpExchangeProxy imageProcessingControllerHttpExchangeProxy;

    @Mock
    private IMapTextControllerHttpExchangeProxy mapTextControllerHttpExchangeProxy;

    @Test
    void should_return_jsonForDriverLicense() throws JsonProcessingException {

        // given
        String imageText = "{\"name\": \"John\", \"age\": 30}";
        var expectedJsonNode = new ObjectMapper().readTree(imageText);
        var expectedMapTextResponseDTO = new MapTextResponse(expectedJsonNode);

        // extract text from OCR
        when(imageProcessing.imageProcessing())
                .thenReturn(imageProcessingControllerHttpExchangeProxy);
        when(aiServiceConnection.mapText())
                .thenReturn(mapTextControllerHttpExchangeProxy);
        when(imageProcessing.imageProcessing().extractTextFromImage(any(MockMultipartFile.class)))
                .thenReturn(new ExtractTextFromImageResponse(imageText));
        when(aiServiceConnection.mapText().parseOcrText(any(MapTextRequest.class)))
                .thenReturn(expectedMapTextResponseDTO);
        TextExtractorService textExtractorService = new TextExtractorService(aiServiceConnection, imageProcessing);

        // when
        JsonNode actualJsonNode = textExtractorService.processFile(new MockMultipartFile("file", new byte[0]), "Test", null);

        // then
        assertEquals(expectedJsonNode, actualJsonNode);
    }

    @Test
    void should_return_jsonForMultiFiles() throws JsonProcessingException, JSONException, ExecutionException, InterruptedException {

        // given
        String imageText = "{\"name\":\"John\",\"age\": \"30\",\"City\":\"Anytown\"}";

        JsonNode expectedJsonNode = new ObjectMapper().readTree(imageText);
        MapTextResponse expectedMapTextResponseDTO = new MapTextResponse(expectedJsonNode);

        byte[] content = "test".getBytes();

        MultiValueMap<String, MultipartFile> pairs = new LinkedMultiValueMap<>();
        pairs.add("idFront", new MockMultipartFile("file1", "file1.txt", "text/plain", content));
        pairs.add("idFront", new MockMultipartFile("file2", "file2.txt", "text/plain", content));
        pairs.add("idFront", new MockMultipartFile("file3", "file3.txt", "text/plain", content));

        // extract text from OCR
        when(imageProcessing.imageProcessing())
                .thenReturn(imageProcessingControllerHttpExchangeProxy);
        when(aiServiceConnection.mapText())
                .thenReturn(mapTextControllerHttpExchangeProxy);
        when(imageProcessing.imageProcessing().extractTextFromImage(any(MockMultipartFile.class)))
                .thenReturn(new ExtractTextFromImageResponse(imageText));
        when(aiServiceConnection.mapText().parseOcrText(any(MapTextRequest.class)))
                .thenReturn(expectedMapTextResponseDTO);
        TextExtractorService textExtractorService = new TextExtractorService(aiServiceConnection, imageProcessing);

        // when
        String actualJson = textExtractorService.merge(pairs, null, null, false);

        // then
        JSONAssert.assertEquals(imageText, actualJson, false);
    }

    @Test
    void should_combine_jsons() throws JSONException, JsonProcessingException {

        ObjectMapper mapper = new ObjectMapper();

        // given
        ArrayList<JsonNode> testJsons = new ArrayList<>();

        testJsons.add(mapper.readTree("{\"name\": \"John\", \"City\": \"Anytown\", \"State\": \"\"}"));
        testJsons.add(mapper.readTree("{\"name\": \"\", \"age\": 30}"));
        testJsons.add(mapper.readTree("{\"age\": 50, \"City\": \"BobTown\", \"name\": \"\"}"));

        String expectedJson = "{\"name\":\"John\",\"City\":\"Anytown\",\"age\":\"30\", \"State\": \"\"}";

        // extract text from OCR
        TextExtractorService textExtractorService = new TextExtractorService(aiServiceConnection, imageProcessing);

        // when
        String combinedJson = textExtractorService.coalesceReturns(testJsons);

        // then
        JSONAssert.assertEquals(expectedJson, combinedJson, true);
    }
}
