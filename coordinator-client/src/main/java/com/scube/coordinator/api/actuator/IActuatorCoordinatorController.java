package com.scube.coordinator.api.actuator;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.AddToServiceClient;
import com.scube.client.annotation.GenerateHttpExchangeProxy;
import com.scube.client.annotation.HttpExchangeWebClient;
import org.springframework.web.service.annotation.GetExchange;

import java.util.Map;

@HttpExchangeWebClient(ServiceUrlConstant.COORDINATOR_SERVICE)
@AddToServiceClient(ServiceUrlConstant.COORDINATOR_SERVICE)
@GenerateHttpExchangeProxy
public interface IActuatorCoordinatorController {
    @GetExchange("/actuator/health")
    ActuatorHealth health();

    @GetExchange("/actuator/health/**")
    Object healthPath();

    @GetExchange("/actuator")
    Map<String, Map<String, Link>> links();
}