apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-payment-service-depl
  labels:
    app: scube-payment-service
spec:
  selector:
    matchLabels:
      app: scube-payment-service
  template:
    metadata:
      labels:
        app: scube-payment-service
    spec:
      nodeSelector:
        subnet-type: private
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-payment-service
          image: service_payment
          ports:
            - containerPort: 9006
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-payment-service-srv.backend.svc.cluster.local
              path: /api/payment/actuator/health
              port: 9006
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9006
            initialDelaySeconds: 60
            periodSeconds: 30
          env:
            - name: TZ
              value: "America/New_York"
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: <PERSON>RI<PERSON>_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: username
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: password
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: username
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: password
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-id-secret
                  key: swagger-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-secret
            - name: AUTHORIZE_DOT_NET_API_LOGIN_ID
              valueFrom:
                secretKeyRef:
                  name: authorize-dot-net-secrets
                  key: api-login-id
            - name: AUTHORIZE_DOT_NET_TRANSACTION_KEY
              valueFrom:
                secretKeyRef:
                  name: authorize-dot-net-secrets
                  key: transaction-key
            - name: AUTHORIZE_DOT_NET_MERCHANT_SIGNATURE_KEY
              valueFrom:
                secretKeyRef:
                  name: authorize-dot-net-secrets
                  key: signature-key
            - name: ENCRYPTION_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: encryption-secret
                  key: secret-Key
---
apiVersion: v1
kind: Service
metadata:
  name: scube-payment-service-srv
  labels:
    app: scube-payment-service
spec:
  selector:
    app: scube-payment-service
  type: ClusterIP
  ports:
    - name: payment-port
      protocol: TCP
      port: 9006
      targetPort: 9006
