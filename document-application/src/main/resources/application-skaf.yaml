server:
  port: 9003
  servlet:
    context-path: /api/document-service
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: never
    include-exception: false
  forward-headers-strategy: framework


logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"
    org.hibernate: "error"
    liquibase: "info"

spring:
  application:
    name: DocumentService

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    generate-ddl: true

  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB
      location: ${java.io.tmpdir}

springdoc:
  show-actuator: true
  swagger-ui:
    filter: true

keycloak:
  service-to-service-connection:
    url: http://keycloak.keycloak.svc.cluster.local:8080
    realm: clerkXpress
    client-id: ${KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID}
    client-secret: ${KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET}
  swagger:
    url: http://keycloak.keycloak.svc.cluster.local:8080
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

keycloak-host: http://keycloak.keycloak.svc.cluster.local:8080
com.c4-soft.springaddons.oidc:
  ops:
    - iss: http://keycloak.keycloak.svc.cluster.local:8080
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
      - "/get-file/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - http://localhost:3030

multi-tenancy:
  enabled: true
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: "rabbitmq.backend.svc.cluster.local"
    port: 5672
  scheduling:
    enabled: true
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
    datasource:
      url: "*****************************************************************************************"
      username: ${SPRING_DATASOURCE_USERNAME}
      password: ${SPRING_DATASOURCE_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

aws:
  region: us-east-1
  storageType: local

scheduling:
  enabled: true

cdc:
  enabled: false
  scheduler:
    enabled: false

com.scube.client:
  auth: "http://scube-auth-service-srv:9001/api/auth"

antivirus:
  enabled: true
  service: Mock
