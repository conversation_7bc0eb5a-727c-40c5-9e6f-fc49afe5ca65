package com.scube.documenttemplates.strategy.template_builder;

import com.scube.documenttemplates.dto.FillFileTemplateRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateBuilderFactory {
    private final Map<String, ITemplateBuilder> templateBuilderMap;

    public ITemplateBuilder getTemplateBuilder(@Nullable String format) {
        if (ObjectUtils.isEmpty(format)) format = "PDF";
        return templateBuilderMap.get("%sTemplateBuilder".formatted(format.toLowerCase()));
    }

    public UUID fillFileTemplate(@Nullable String format, String data, String nameKey) {
        ITemplateBuilder templateBuilder = getTemplateBuilder(format);
        return templateBuilder.fillFileTemplate(data, nameKey);
    }

    public void fillFileTemplateAsync(FillFileTemplateRequest request) {
        ITemplateBuilder templateBuilder = getTemplateBuilder(request.getFormat());
        templateBuilder.fillFileTemplateAsync(request);
    }

    public void fillFileTemplateAsync(@Nullable String format, FillFileTemplateRequest request) {
        ITemplateBuilder templateBuilder = getTemplateBuilder(format);
        templateBuilder.fillFileTemplateAsync(request);
    }
}
