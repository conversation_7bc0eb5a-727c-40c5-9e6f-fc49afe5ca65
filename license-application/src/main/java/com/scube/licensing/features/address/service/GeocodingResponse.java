package com.scube.licensing.features.address.service;

import com.scube.licensing.utils.StringTemplateUtils;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public final class GeocodingResponse {
    private final String houseNumber;
    private final String streetName;
    private final String city;
    private final String town;
    private final String county;
    private final String state;
    private final String country;
    private final String postalCode;
    private final String fullAddress;
    private final Double latitude;
    private final Double longitude;

    public GeocodingResponse(String houseNumber, String streetName, String city, String town, String county,
                             String state, String country, String postalCode, Double latitude, Double longitude) {
        this.houseNumber = houseNumber;
        this.streetName = streetName;
        this.city = normalize(city);
        this.town = normalize(town);
        this.county = county;
        this.state = state;
        this.country = country;
        this.postalCode = postalCode;
        this.fullAddress = buildFullAddress(houseNumber, streetName, city, state, postalCode);
        this.latitude = latitude;
        this.longitude = longitude;
    }

    private String buildFullAddress(String houseNumber, String streetName, String city, String state, String postalCode) {
        if (houseNumber == null) houseNumber = "";
        if (streetName == null) streetName = "";
        if (city == null) city = "";
        if (state == null) state = "";
        if (postalCode == null) postalCode = "";

        return StringTemplateUtils.buildStringFromTemplate(
                "{houseNumber} {streetName}, {city}, {state} {postalCode}",
                new HashMap<>(Map.of(
                        "houseNumber", houseNumber,
                        "streetName", streetName,
                        "city", city,
                        "state", state,
                        "postalCode", postalCode
                )));
    }

    private String normalize(String value) {
        if (value == null) return null;
        return value
                .replace("City of", "")
                .replace("Town of", "")
                .trim();
    }
}