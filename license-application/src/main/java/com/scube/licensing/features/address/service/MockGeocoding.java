package com.scube.licensing.features.address.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
@ConditionalOnProperty(name = "geocoding.type", havingValue = "mock")
public class MockGeocoding implements IGeocoding {
    @Override
    public Optional<GeocodingResponse> getGeocoding(String street, String city, String state, String postalCode) {
        var result = new GeocodingResponse(
                null,
                street,
                city,
                null,
                null,
                state,
                null,
                postalCode,
                0.00,
                0.00
        );

        return Optional.of(result);
    }
}