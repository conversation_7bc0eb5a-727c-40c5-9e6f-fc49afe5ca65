package com.scube.licensing.features.document.service;

import com.scube.auth.library.ITokenService;
import com.scube.client.document.generated.DocumentServiceConnection;
import com.scube.document.dto.gen_dto.FileUploadResponseDTO;
import com.scube.document.generated.FileUploadRequestDTO__QueryParams;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@RequiredArgsConstructor
@Transactional
public class DocumentStorageConnectionService {
    private final DocumentServiceConnection documentServiceConnection;
    private final ITokenService tokenService;

    public FileUploadResponseDTO uploadFile(MultipartFile file) {
        // DR: Get a new access token from the auth server
        // This is so we don't have to expose a /me endpoint on document service
        // exposing /me endpoint on document service would allow anyone to upload any files
        var token = tokenService.getNewAccessTokenFromCurrentRealm();
        var params = new FileUploadRequestDTO__QueryParams().file(file);
        return documentServiceConnection.document().handleFileUpload(params, (CharSequence) token);
    }
}