package com.scube.licensing.features.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.audit.auditable.properties.validation.PropertyValidator;
import com.scube.audit.auditable.properties.value_type.IPropertyValue;
import com.scube.lib.misc.MapUtils;
import com.scube.licensing.features.entity.dtos.CreateAddressRequestDto;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import lombok.AllArgsConstructor;
import java.util.*;

@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class EntityRequest<T> extends AdditionalFieldMap<T, String, Object> implements IEntityRequest {
    private final String tableName;

    protected EntityRequest(Map<String, String> map, String tableName) {
        super(MapUtils.toObjectMap(map));
        this.tableName = tableName;
    }

    public Map<String, String> toStringMap() {
        return MapUtils.toStringMap(this);
    }

    protected String getAndValidate(String key) {
        var value = getIgnoreCaseOrElseEmpty(key);
        if (value instanceof String str) {
            value = str.trim();
        }
        IPropertyValue<?> validateResult = PropertyValidator.validate(tableName, key, value);
        return validateResult.getValueToSave().orElse(value).toString();
    }

    @JsonProperty("associations")
    @Override
    public Set<EntityAssociation> getAssociations() {
        var result = new HashSet<EntityAssociation>();
        var associations = getIgnoreCaseOrElseEmpty("associations");
        if (associations instanceof Collection<?> collections) {
            collections.forEach(association -> {
                if (association instanceof Map<?, ?> map) {
                    result.add(new EntityAssociation(map));
                }
            });

        }
        return result;
    }

    public List<CreateAddressRequestDto> getAddresses() {
        return List.of();
    }
}