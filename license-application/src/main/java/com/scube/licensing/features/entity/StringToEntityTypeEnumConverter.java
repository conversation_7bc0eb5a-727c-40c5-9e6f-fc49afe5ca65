package com.scube.licensing.features.entity;

import com.scube.licensing.features.entity.dtos.EntityTypeEnum;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
public class StringToEntityTypeEnumConverter implements Converter<String, EntityTypeEnum> {
    @Override
    public EntityTypeEnum convert(@Nullable String source) {
        return EntityTypeEnum.fromValue(source);
    }
}