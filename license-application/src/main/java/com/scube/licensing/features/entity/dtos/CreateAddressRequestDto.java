package com.scube.licensing.features.entity.dtos;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class CreateAddressRequestDto {
    private String type;
    private String streetAddress;
    private String streetAddress2;
    private String city;
    private String state;
    private String zip;

    public static CreateAddressRequestDto createAddress(String type, String streetAddress, String streetAddress2, String city, String state, String zip) {
        return CreateAddressRequestDto.builder()
                .type(type)
                .streetAddress(streetAddress)
                .streetAddress2(streetAddress2)
                .city(city)
                .state(state)
                .zip(zip)
                .build();
    }

    @JsonIgnore
    public Address getAddress() {
        return new Address()
                .setStreetAddress(streetAddress)
                .setStreetAddress2(streetAddress2)
                .setCity(city)
                .setState(state)
                .setZip(zip);
    }
}