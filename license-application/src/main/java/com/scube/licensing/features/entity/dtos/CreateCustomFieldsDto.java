package com.scube.licensing.features.entity.dtos;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateCustomFieldsDto {
    private String key;
    private Object value;
    private String type;
    private String table;

    public static CreateCustomFieldsDto createCustomField(String type, String table, String key, Object value) {
        return CreateCustomFieldsDto.builder()
                .key(key)
                .value(value)
                .type(type)
                .table(table)
                .build();
    }
}
