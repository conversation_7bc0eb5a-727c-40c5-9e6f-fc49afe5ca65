package com.scube.licensing.features.entity.dtos;

import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.web.server.ResponseStatusException;

@Getter
public enum EntityTypeEnum {
    DOG("dog"),
    INDIVIDUAL("individual"),
    ADDRESS("address"),
    LICENSE("license"),
    DOCUMENT("document"),
    ENTITY_FEE("entityFee"),
    ENTITY_NOTE("entityNote"),
    BUSINESS("business"),
    CUSTOM_ENTITY("customEntity"),
    ENTITY_GROUP("entityGroup");

    private final String key;

    EntityTypeEnum(String key) {
        this.key = key;
    }

    public static EntityTypeEnum fromValue(@Nullable String value) {
        if (value != null && value.equalsIgnoreCase(Participant.class.getSimpleName())) {
            return INDIVIDUAL;
        }
        for (EntityTypeEnum type : values()) {
            if (type.key.equalsIgnoreCase(value) || type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid entityType: " + value);
    }
}
