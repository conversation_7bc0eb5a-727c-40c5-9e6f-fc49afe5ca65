package com.scube.licensing.features.entity.service;

import com.scube.lib.misc.MapUtils;
import com.scube.licensing.features.entity.dtos.EntityTypeEnum;
import com.scube.licensing.features.entity.strategies.IEntityStrategy;
import com.scube.licensing.features.profile.ProfileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class EntityService {
    private final Map<String, IEntityStrategy> entityStrategy;
    private final ProfileService profileService;

    public Object createEntity(EntityTypeEnum entityType, Map<String, Object> fields, Map<String, MultipartFile> files) {
        log.info("Start createEntity with entityType: {}", entityType);
        IEntityStrategy strategy = getiEntityStrategy(entityType);
        var map = MapUtils.toJsonMap(fields);
        var result = strategy.create(map, files);
        log.info("End createEntity...");
        return result;
    }

    public Object updateEntity(EntityTypeEnum entityType, UUID entityId, Map<String, Object> fields, Map<String, MultipartFile> files) {
        log.info("Start updateEntity with entityType: {} and entityId: {}", entityType, entityId);
        IEntityStrategy strategy = getiEntityStrategy(entityType);
        var map = MapUtils.toJsonMap(fields);
        var associable = profileService.getProfileOrElseThrow(entityType.getKey(), entityId);
        var result = strategy.update(associable, map, files);
        log.info("End updateEntity...");
        return result;
    }

    public void deleteEntity(EntityTypeEnum entityType, UUID entityId) {
        log.info("Start deleteEntity with entityType: {} and entityId: {}", entityType, entityId);
        IEntityStrategy strategy = getiEntityStrategy(entityType);
        var associable = profileService.getProfileOrElseThrow(entityType.getKey(), entityId);
        strategy.delete(associable);
        log.info("End deleteEntity...");
    }

    public Object addAssociation(EntityTypeEnum entityType, UUID entityId, Map<String, Object> fields) {
        log.info("Start addAssociation with entityType: {} and entityId: {}", entityType, entityId);
        IEntityStrategy strategy = getiEntityStrategy(entityType);
        var map = MapUtils.toJsonMap(fields);
        var associable = profileService.getProfileOrElseThrow(entityType.getKey(), entityId);
        var result = strategy.addAssociation(associable, map);
        profileService.save(associable);
        log.info("End addAssociation...");
        return result;
    }

    public Object removeAssociation(EntityTypeEnum entityType, UUID entityId, Map<String, Object> fields) {
        log.info("Start removeAssociation with entityType: {} and entityId: {}", entityType, entityId);
        IEntityStrategy strategy = getiEntityStrategy(entityType);
        var map = MapUtils.toJsonMap(fields);
        var associable = profileService.getProfileOrElseThrow(entityType.getKey(), entityId);
        var result = strategy.removeAssociation(associable, map);
        profileService.save(associable);
        log.info("End removeAssociation...");
        return result;
    }

    private @NotNull IEntityStrategy getiEntityStrategy(EntityTypeEnum entityType) {
        IEntityStrategy strategy = entityStrategy.get(entityType.getKey() + "EntityStrategy");
        if (strategy == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "No strategy found for entity type: " + entityType);
        }
        return strategy;
    }
}