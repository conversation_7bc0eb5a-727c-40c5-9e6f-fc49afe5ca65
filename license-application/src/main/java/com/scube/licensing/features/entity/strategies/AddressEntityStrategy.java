package com.scube.licensing.features.entity.strategies;

import com.scube.licensing.features.address.dto.AddressRequest;
import com.scube.licensing.features.address.service.AddressService;
import com.scube.licensing.features.entity.AssociableService;
import com.scube.licensing.features.profile.dto.AddressDto;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RequiredArgsConstructor
@Component(value = Address.ENTITY_TYPE + "EntityStrategy")
public class AddressEntityStrategy implements IEntityStrategy {
    private final AssociableService associableService;
    private final AddressService addressService;

    @Override
    public AddressDto create(Map<String, String> fields, Map<String, MultipartFile> files) {
        return addressService.createAddress(new AddressRequest(fields));
    }

    @Override
    public AddressDto update(Associable associable, Map<String, String> fields, Map<String, MultipartFile> files) {
        return addressService.updateAddress((Address) associable, new AddressRequest(fields));
    }

    @Override
    public void delete(Associable associable) {
        throw new UnsupportedOperationException("Not implemented");
    }

    @Override
    public AddressDto addAssociation(Associable associable, Map<String, String> fields) {
        var request = new AddressRequest(fields);
        associableService.addAssociations(associable, request);
        return null;
    }

    @Override
    public AddressDto removeAssociation(Associable associable, Map<String, String> fields) {
        var request = new AddressRequest(fields);
        associableService.removeAssociations(associable, request);
        return null;
    }
}