package com.scube.licensing.features.entity.strategies;

import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RequiredArgsConstructor
@Component(value = Document.ENTITY_TYPE + "EntityStrategy")
public class DocumentEntityStrategy implements IEntityStrategy {
    @Override
    public IEntityDto create(Map<String, String> fields, Map<String, MultipartFile> files) {
        throw new UnsupportedOperationException("Not implemented yet");
    }

    @Override
    public IEntityDto update(Associable associable, Map<String, String> fields, Map<String, MultipartFile> files) {
        throw new UnsupportedOperationException("Not implemented yet");
    }

    @Override
    public void delete(Associable associable) {
        throw new UnsupportedOperationException("Not implemented");
    }

    @Override
    public IEntityDto addAssociation(Associable associable, Map<String, String> fields) {
        throw new UnsupportedOperationException("Not implemented yet");
    }

    @Override
    public IEntityDto removeAssociation(Associable associable, Map<String, String> fields) {
        throw new UnsupportedOperationException("Not implemented yet");
    }
}
