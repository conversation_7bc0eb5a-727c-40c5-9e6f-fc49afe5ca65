package com.scube.licensing.features.entity.strategies;

import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface IEntityStrategy {
    IEntityDto create(Map<String, String> fields, Map<String, MultipartFile> files);
    IEntityDto update(Associable associable, Map<String, String> fields, Map<String, MultipartFile> files);
    void delete(Associable associable);
    IEntityDto addAssociation(Associable associable, Map<String, String> fields);
    IEntityDto removeAssociation(Associable associable, Map<String, String> fields);
}