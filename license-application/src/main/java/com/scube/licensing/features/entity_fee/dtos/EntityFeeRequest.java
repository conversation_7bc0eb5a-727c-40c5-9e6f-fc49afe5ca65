package com.scube.licensing.features.entity_fee.dtos;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.features.entity.IEntityRequest;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityFeeRequest implements IEntityRequest {
    private String key;
    private BigDecimal amount;
    private String comment;

    @JsonAnySetter
    private Map<String, Object> properties = new HashMap<>();

    @Getter(AccessLevel.NONE)
    @JsonProperty("associations")
    private Set<Map<String, String>> associations = new HashSet<>();

    @JsonIgnore
    public Set<EntityAssociation> getAssociations() {
        return associations.stream()
                .map(EntityAssociation::new)
                .collect(Collectors.toSet());
    }

    @JsonIgnore
    public List<CreateCustomFieldsDto> getCustomFields() {
        var result = new ArrayList<CreateCustomFieldsDto>();

        getProperties()
                .forEach((key, value) -> result.add(CreateCustomFieldsDto.createCustomField("object", EntityFee.TABLE_NAME, key, value)));

        return result;
    }

    public EntityFeeRequest(Map<String, String> map) {
    }
}