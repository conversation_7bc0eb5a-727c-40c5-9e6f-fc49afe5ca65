package com.scube.licensing.features.entity_fee.rabbit;

import com.scube.licensing.features.entity.dtos.EntityTypeEnum;
import com.scube.licensing.features.entity_fee.EntityFeeService;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
@Slf4j
public class GetUnPaidEntityFeesQueryHandler extends FanoutListenerRpc<GetUnPaidEntityFeesQueryHandler.GetUnPaidEntityFeesQuery, GetUnPaidEntityFeesQueryHandler.GetUnPaidEntityFeesQueryResponse> {
    private final EntityFeeService entityFeeService;

    @Override
    public RabbitResult<GetUnPaidEntityFeesQueryResponse> consume(GetUnPaidEntityFeesQuery event) {
        return RabbitResult.of(() -> {
                    var entityTypeEnum = EntityTypeEnum.fromValue(event.entityType());
                    var fees = entityFeeService.getUnPaidFees(entityTypeEnum, event.entityId());
                    return new GetUnPaidEntityFeesQueryResponse(fees);
                }
        );
    }

    // @formatter:off
    public record GetUnPaidEntityFeesQuery(String entityType, UUID entityId) implements IRabbitFanoutSubscriberRpc<GetUnPaidEntityFeesQueryResponse> { }
    public record GetUnPaidEntityFeesQueryResponse(List<EntityFeeDto> fees) { }
}