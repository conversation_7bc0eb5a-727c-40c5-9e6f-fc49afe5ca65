package com.scube.licensing.features.entity_group.dtos;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.licensing.features.entity_fee.dtos.Totals;
import com.scube.licensing.features.profile.dto.EventDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.utils.FeeStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FeeSetDto implements IEntityDto, IAssociableDto {
    private String groupName;
    private UUID entityId;
    private String entityType;
    private String label;
    private String description;
    @JsonIgnore
    private List<EventDto> events;

    @JsonIgnore
    private Map<String, Object> customFields;

    @JsonIgnore
    private String tableName;

    private Instant createdDateTime;
    private Instant updatedDateTime;
    private EntityAssociation createdBy;
    private EntityAssociation updatedBy;

    private List<EntityAssociation> associations;
    private String feeStatus = FeeStatus.UNPAID.toString();

    private List<EntityFeeDto> fees = new ArrayList<>();
    private Totals totals;
    private List<DocumentDto> documents = new ArrayList<>();

    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }

    public void setFeeStatus() {
        int totalFeesCount = this.fees.size();
        if (totalFeesCount > 0) {
            int nonPaidFeesCount = (int) this.fees.stream()
                    .filter(fee -> FeeStatus.valueOf(fee.getFeeStatus().toUpperCase()) != FeeStatus.PAID)
                    .count();

            String feeStatus;
            if (nonPaidFeesCount == 0) {
                feeStatus = FeeStatus.PAID.toString();
            } else {
                feeStatus = FeeStatus.UNPAID.toString();
            }
            this.feeStatus = feeStatus;
        } else {
            this.feeStatus = FeeStatus.UNPAID.toString();
        }
    }

}
