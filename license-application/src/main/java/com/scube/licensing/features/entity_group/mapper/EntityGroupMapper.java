package com.scube.licensing.features.entity_group.mapper;

import com.scube.lib.misc.BeanUtils;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.entity_fee.EntityFeeService;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.entity_group.dtos.EntityGroupDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.features.user.UserService;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.rabbit.core.AmqpGateway;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.lang.Nullable;

import java.util.List;

@Slf4j
@Mapper(componentModel = "spring", uses = {EventMapper.class})
public abstract class EntityGroupMapper {
    @Autowired
    private Environment env;

    @Autowired
    private AmqpGateway amqpGateway;

    @Autowired
    private UserService userService;

    @Mapping(target = "groupName", source = "name")
    @Mapping(target = "entityId", source = "uuid")
    @Mapping(target = "entityType", constant = EntityGroup.ENTITY_TYPE)
    @Mapping(target = "tableName", constant = EntityGroup.ENTITY_TYPE)
    @Mapping(target = "createdDateTime", source = "createdDate")
    @Mapping(target = "updatedDateTime", source = "lastModifiedDate")
    @Mapping(target = "createdBy", expression = "java(getCreatedByName(entityGroup))")
    @Mapping(target = "updatedBy", expression = "java(getUpdatedByName(entityGroup))")
    @Mapping(target = "associations", expression = "java(getAssociations(entityGroup))")
    @Mapping(target = "customFields", source = "properties")
    @Mapping(target = "fees", expression = "java(getAllFee(entityGroup))")
    public abstract EntityGroupDto toDto(EntityGroup entityGroup);

    public abstract List<EntityGroupDto> toDto(List<EntityGroup> entityGroups);

    public List<EntityAssociation> getAssociations(EntityGroup entityGroup) {
        return entityGroup.getParentAssociables().stream()
                .map(EntityAssociation::new)
                .toList();
    }

    public String getCreatedByName(EntityGroup entityGroup) {
        var username = entityGroup.getCreatedBy();
        var isTestProfile = env.getActiveProfiles().length > 0 && env.getActiveProfiles()[0].equals("test");
        if (isTestProfile) return username;
        return userService.getUserNameByUsername(username);
    }

    public String getUpdatedByName(EntityGroup entityGroup) {
        var username = entityGroup.getLastModifiedBy();
        var isTestProfile = env.getActiveProfiles().length > 0 && env.getActiveProfiles()[0].equals("test");
        if (isTestProfile) return username;
        return userService.getUserNameByUsername(username);
    }

    public GetAllEntityFeeResponse getAllFee(@Nullable Associable associable) {
        if (associable == null) return null;
        var entityFeeService = BeanUtils.getBean(EntityFeeService.class);
        return entityFeeService.getAllFees(associable.getEntityType(), associable.getUuid());
    }
}
