package com.scube.licensing.features.events;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.auth.library.enabled_true.AuthUtils;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class Event implements Serializable {
    private UUID uuid;
    private Long eventTypeId;
    @JsonIgnore
    private String eventTypeCode;
    private String createdBy;
    private Instant createdDate;
    private String comment;
    private String action;
    private boolean inherited;
    private Map<String, String> metadata;

    public Event() {
        this.uuid = UUID.randomUUID();
        this.createdBy = AuthUtils.getLoggedInUserPreferredUsername();
        this.createdDate = Instant.now();
    }


    public Event(String eventTypeCode, String action) {
        this();
        this.eventTypeCode = eventTypeCode;
        this.action = action;
    }

    public Event(String eventTypeCode, String action, Map<String, String> metadata) {
        this(eventTypeCode, action);
        this.metadata = metadata;
    }

    public Event(EventType eventType, String comment) {
        this();
        this.eventTypeId = eventType.getId();
        this.comment = comment;
    }

    public Event(EventType eventType, String comment, String action) {
        this(eventType, comment);
        this.action = action;
    }
}