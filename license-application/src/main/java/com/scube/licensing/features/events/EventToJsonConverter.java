package com.scube.licensing.features.events;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.util.List;

import static com.scube.lib.misc.CaseUtils.camelCaseToFriendlyName;

@Slf4j
@Converter
@AllArgsConstructor
public class EventToJsonConverter implements AttributeConverter<List<Event>, String> {
    private final ObjectMapper objectMapper;
    private final JdbcTemplate jdbcTemplate;

    private Long findEventTypeByCode(String eventCode) {
        if (ObjectUtils.isEmpty(eventCode))
            throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "The event is missing an eventTypeId or eventTypeCode");

        var selectQuery = "SELECT event_type_id FROM event_type WHERE code = ?";
        try {
            // Try to fetch the event_type_id if it exists
            return jdbcTemplate.queryForObject(selectQuery, Long.class, eventCode);
        } catch (EmptyResultDataAccessException e) {
            // If not found, insert the new event type and return the generated ID
            var insertQuery = "INSERT INTO event_type (%s,%s,%s,%s,%s,%s) VALUES (?,?,?,?,?,?)".formatted(
                    EventType.E_CODE, EventType.E_NAME,
                    AuditableBase.CREATED_BY, AuditableBase.CREATED_DATE, AuditableBase.LAST_MODIFIED_BY, AuditableBase.LAST_MODIFIED_DATE
            );
            var now = LocalDateTime.now();
            jdbcTemplate.update(insertQuery, eventCode, camelCaseToFriendlyName(eventCode), "system", now, "system", now);

            // After insertion, retrieve the ID of the newly inserted row
            return jdbcTemplate.queryForObject(selectQuery, Long.class, eventCode);
        }
    }


    @Override
    public String convertToDatabaseColumn(List<Event> events) {
        if (events == null)
            return null;

        for (var event : events) {
            if (ObjectUtils.isEmpty(event.getEventTypeId())) {
                event.setEventTypeId(findEventTypeByCode(event.getEventTypeCode()));
            }
        }

        try {
            return objectMapper.writeValueAsString(events);
        } catch (Exception e) {
            log.error("EventToJsonConverter.convertToDatabaseColumn() error", e);
            return null;
        }
    }

    @Override
    public List<Event> convertToEntityAttribute(String events) {
        if (events == null || events.isEmpty())
            return null;

        try {
            return objectMapper.readValue(events, new TypeReference<List<Event>>() {
            });
        } catch (Exception e) {
            log.error("EventToJsonConverter.convertToEntityAttribute() error", e);
            return null;
        }
    }
}
