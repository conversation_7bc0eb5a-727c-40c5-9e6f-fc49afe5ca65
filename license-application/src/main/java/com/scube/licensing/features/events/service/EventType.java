package com.scube.licensing.features.events.service;

public enum EventType {
    ADDRESS_CHANGE,
    DOG_DECEASED,
    DOG_REACTIVATED,
    DOG_LOST,
    DOG_FOUND,
    DOG_TRANSFER_OF_OWNERSHIP,
    DOG_RELINQUISHED,
    DOG_SET_DANGEROUS_TRUE,
    DOG_SET_DANGEROUS_FALSE,
    INDIVIDUAL_DECEASED,
    INDIVIDUAL_REACTIVATED,
    INDIVIDUAL_MOVED_OUTSIDE_JURISDICTION,
    LICENSE_CANCELED,
    LICENSE_CORRECTED,
    DOG_SIGHTED;

    EventType() {
    }
}
