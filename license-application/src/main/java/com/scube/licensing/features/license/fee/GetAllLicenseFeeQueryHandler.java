package com.scube.licensing.features.license.fee;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivity;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivityFee;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Component
public class GetAllLicenseFeeQueryHandler extends FanoutListenerRpc<GetAllLicenseFeeQueryHandler.GetAllLicenseFeesQuery, GetAllLicenseFeeQueryHandler.GetAllLicenseFeeResponse> {
    private final LicenseService licenseService;

    public GetAllLicenseFeeQueryHandler(LicenseService licenseService) {
        this.licenseService = licenseService;
    }

    @Transactional(readOnly = true)
    public RabbitResult<GetAllLicenseFeeResponse> consume(GetAllLicenseFeesQuery event) {
        return RabbitResult.of(() -> {
            License license = licenseService.findLicenseByEntityId(new LicenseService.FindLicenseByEntityIdQuery(event.getLicenseEntityId()))
                    .orElseThrow(() -> new RuntimeException("License with entity id " + event.getLicenseEntityId() + " not found"));
            return new GetAllLicenseFeeResponse(license.getLicenseActivities().stream().toList());
        });
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetAllLicenseFeesQuery implements IRabbitFanoutSubscriberRpc<GetAllLicenseFeeResponse> {
        private UUID licenseEntityId;
    }

    @Data
    @NoArgsConstructor
    public static class GetAllLicenseFeeResponse implements Serializable {
        private List<GetAllLicenseFeeDetailsResponse> items;
        private BigDecimal totalAmount;
        private BigDecimal totalPaidAmount;
        private BigDecimal totalOutstandingAmount;
        private BigDecimal totalDiscountAmount;
        private BigDecimal totalSubtotal;

        public GetAllLicenseFeeResponse(List<LicenseActivity> licenseActivities) {
            this.items = licenseActivities.stream()
                    .map(GetAllLicenseFeeDetailsResponse::new)
                    // sort by valid to date, and if null then sort by created date
                    .sorted(Comparator.comparing(GetAllLicenseFeeDetailsResponse::getValidTo, Comparator.nullsLast(Comparator.reverseOrder()))
                            .thenComparing(GetAllLicenseFeeDetailsResponse::getCreatedDate))
                    .toList();
            this.totalAmount = licenseActivities.stream()
                    .map(LicenseActivity::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            this.totalPaidAmount = licenseActivities.stream()
                    .map(LicenseActivity::getTotalPaidAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            this.totalOutstandingAmount = licenseActivities.stream()
                    .map(LicenseActivity::getTotalOutstandingAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            this.totalDiscountAmount = licenseActivities.stream()
                    .map(LicenseActivity::getTotalDiscountAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            this.totalSubtotal = licenseActivities.stream()
                    .map(LicenseActivity::getSubTotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    @Data
    @NoArgsConstructor
    public static class GetAllLicenseFeeDetailsResponse implements Serializable {
        private UUID activityId;
        private String activityType;
        private Instant validFrom;
        private Instant validTo;
        private Instant createdDate;
        private UUID orderId;
        private Instant processedDate;

        @JsonUnwrapped
        private Map<String, Object> customFields;

        private List<GetAllLicenseFeeDetailsFeeResponse> fees;
        private BigDecimal totalAmount;
        private BigDecimal totalPaidAmount;
        private BigDecimal totalOutstandingAmount;
        private BigDecimal totalDiscountAmount;
        private BigDecimal totalSubtotal;

        public GetAllLicenseFeeDetailsResponse(LicenseActivity activity) {
            this.activityId = activity.getUuid();
            this.activityType = activity.getActivityType().getKey();
            this.validFrom = activity.getValidFromDate();
            this.validTo = activity.getValidToDate();
            this.createdDate = activity.getCreatedDate();
            this.fees = activity.getLicenseActivityFees().stream()
                    .map(GetAllLicenseFeeDetailsFeeResponse::new)
                    .sorted(Comparator.comparing(GetAllLicenseFeeDetailsFeeResponse::getFeeAmount).reversed())
                    .toList();
            this.orderId = fees.stream()
                    .map(GetAllLicenseFeeDetailsFeeResponse::getOrderId)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);

            this.processedDate = fees.stream()
                    .map(GetAllLicenseFeeDetailsFeeResponse::getFeePaidDate)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);
            this.customFields = activity.getProperties();

            this.totalAmount = activity.getTotalAmount();
            this.totalPaidAmount = activity.getTotalPaidAmount();
            this.totalOutstandingAmount = activity.getTotalOutstandingAmount();
            this.totalDiscountAmount = activity.getTotalDiscountAmount();
            this.totalSubtotal = activity.getSubTotal();
        }
    }

    @Data
    @NoArgsConstructor
    public static class GetAllLicenseFeeDetailsFeeResponse implements Serializable {
        private UUID feeId;
        private String feeCode;
        private String feeStatus;
        private BigDecimal feeAmount;
        private Instant feePaidDate;
        private UUID orderId;

        @JsonAnyGetter
        private Map<String, Object> customFields;

        public GetAllLicenseFeeDetailsFeeResponse(LicenseActivityFee licenseActivityFee) {
            this.feeId = licenseActivityFee.getUuid();
            this.feeCode = licenseActivityFee.getFeeCode();
            this.feeStatus = licenseActivityFee.getPaymentStatus().getKey();
            this.feeAmount = licenseActivityFee.getAmount();
            this.feePaidDate = licenseActivityFee.getPaidDate();
            this.orderId = licenseActivityFee.getOrderId();
            this.customFields = licenseActivityFee.getProperties();
        }
    }
}