package com.scube.licensing.features.license.get_license_data_query;

import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetLicenseDataDynamicQuery implements IRabbitFanoutSubscriberRpc<GetLicenseDataDynamicQueryHandler.GetLicenseDataDynamicQueryResponse> {
    private String sql;
    private Object[] params;
}