package com.scube.licensing.features.license.is_vaccination_expired;

import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.utils.DynamicViewUtils;
import com.scube.lib.misc.dates.DateUtils;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

@Component
@AllArgsConstructor
@Slf4j
public class IsVaccinationExpiredQueryHandler extends FanoutListenerRpc<IsVaccinationExpiredQueryHandler.IsVaccinationExpiredQuery, IsVaccinationExpiredQueryHandler.IsVaccinationExpiredQueryResponse> {
    private final LicenseService licenseService;

    public RabbitResult<IsVaccinationExpiredQueryResponse> consume(IsVaccinationExpiredQuery event) {
        log.debug("Received IsVaccinationExpiredQuery: {}", event);
        var licenseData = licenseService.getLicenseData(event.licenseEntityId());
        var vaccineDueDateStr = DynamicViewUtils.getData(licenseData, "vaccineDueDate");
        var vaccineDueDate = DateUtils.toDateTime(vaccineDueDateStr);
        if (vaccineDueDate == null) return RabbitResult.of(() -> new IsVaccinationExpiredQueryResponse(false));
        //check if vaccineDueDate is expired
        return RabbitResult.of(() -> new IsVaccinationExpiredQueryResponse(vaccineDueDate.isBefore(LocalDateTime.now())));
    }

    // @formatter:off
    public record IsVaccinationExpiredQuery(UUID licenseEntityId) implements IRabbitFanoutSubscriberRpc<IsVaccinationExpiredQueryResponse> { }
    public record IsVaccinationExpiredQueryResponse(Boolean isVaccinationExpired) { }
}
