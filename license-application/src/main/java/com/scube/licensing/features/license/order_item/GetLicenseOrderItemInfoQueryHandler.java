package com.scube.licensing.features.license.order_item;

import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.LicenseDto;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Comparator;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
public class GetLicenseOrderItemInfoQueryHandler extends FanoutListenerRpc<GetLicenseOrderItemInfoQueryHandler.GetLicenseOrderItemInfoQuery, GetLicenseOrderItemInfoQueryHandler.GetLicenseOrderItemInfoQueryResponse> {
    private final ProfileService profileService;

    @Transactional //import to have this here or else getProfileAndAssociations will throw an error
    public RabbitResult<GetLicenseOrderItemInfoQueryResponse> consume(GetLicenseOrderItemInfoQuery event) {
        return RabbitResult.of(() -> {
            var license = profileService.getProfileOrElseThrow(event.entityId, License.class);
            var profileAndAssoc = profileService.getProfileAndAssociationsDto(event.entityId, "license");
            var licenseDto = (LicenseDto) profileAndAssoc.getProfile();
            var licenseType = license.getLicenseType().getName();
            var dogs = profileAndAssoc.getAssociations().stream()
                    .filter(ParticipantDto.class::isInstance)
                    .map(x -> (ParticipantDto) x)
                    .filter(x -> x.getName().equalsIgnoreCase("dog"))
                    .sorted(Comparator.comparing(ParticipantDto::getCreatedDate))
                    .toList();
            var dog = dogs.getLast().getCustomFields();
            var dogName = dog.getOrDefault("dogName", "");
            var dogTag = dog.getOrDefault("tagNumber", "");
            var licenseDuration = licenseDto.getCustomFields().getOrDefault("licenseDuration", "");
            var licenseLabel = license.getLicenseLabel();

            String name = null;
            if (!ObjectUtils.isEmpty(dogName)) {
                name = String.format("%s - %s", licenseType, dogName);
            } else {
                name = licenseType;
            }
            String description = null;
            String durationLabel = null;
            if (licenseLabel.isPresent()) {
                durationLabel = licenseLabel.get();
            } else if (licenseDuration != null && Integer.parseInt(licenseDuration.toString()) == 1) {
                durationLabel = "1 year";
            } else {
                durationLabel = String.format("%s years", licenseDuration);
            }
            if (!ObjectUtils.isEmpty(dogTag)) {
                description = String.format("Tag: %s | %s", dogTag, durationLabel);
            } else {
                description = String.format("%s", durationLabel);
            }

            return new GetLicenseOrderItemInfoQueryResponse(name, description);
        });
    }

    // @formatter:off
    public record GetLicenseOrderItemInfoQuery(UUID entityId) implements IRabbitFanoutSubscriberRpc<GetLicenseOrderItemInfoQueryResponse> { }
    public record GetLicenseOrderItemInfoQueryResponse(String name, String description) { }
}