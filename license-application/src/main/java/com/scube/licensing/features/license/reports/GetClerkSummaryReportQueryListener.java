package com.scube.licensing.features.license.reports;

import com.scube.licensing.features.license.reports.projections.ClerksSummaryReportCountsProjection;
import com.scube.licensing.infrastructure.db.repository.license.LicenseRepository;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;


@AllArgsConstructor
@Component
@Slf4j
public class GetClerkSummaryReportQueryListener extends FanoutListenerRpc<GetClerkSummaryReportQueryListener.GetClerkSummaryReportQuery, GetClerkSummaryReportQueryListener.GetClerkSummaryReportResponse> {
    private final LicenseRepository licenseRepository;

    public RabbitResult<GetClerkSummaryReportResponse> consume(GetClerkSummaryReportQueryListener.GetClerkSummaryReportQuery event) {
        return RabbitResult.of(() ->
                new GetClerkSummaryReportResponse(licenseRepository.findClerkSummaryReportCountsByDate(event.getStartDate(), event.getEndDate()))
        );
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetClerkSummaryReportQuery implements IRabbitFanoutSubscriberRpc<GetClerkSummaryReportResponse> {
        private LocalDate startDate;
        private LocalDate endDate;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetClerkSummaryReportResponse {
        private List<ClerksSummaryReportCountsProjection> counts;
    }
}
