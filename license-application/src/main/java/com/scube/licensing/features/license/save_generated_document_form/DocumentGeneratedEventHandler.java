package com.scube.licensing.features.license.save_generated_document_form;

import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.license.exception.LicenseNotFoundException;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentGeneratedEventHandler extends FanoutListener<DocumentGeneratedEventHandler.DocumentGeneratedEvent> {
    private final LicenseService licenseService;

    @Transactional
    @Override
    public void consume(DocumentGeneratedEvent event) {
        var allowedTypes = List.of("dogLicense", "purebredDogLicense");
        if (ObjectUtils.isEmpty(event.getParentType()) || allowedTypes.stream().noneMatch(event.getParentType()::equalsIgnoreCase)) {
            return;
        }
        log.info("DocumentGeneratedEvent received: {}", event);
        var license = licenseService.findLicenseByEntityId(new LicenseService.FindLicenseByEntityIdQuery(event.getParentId()));
        if (license.isEmpty())
            throw new LicenseNotFoundException("License with entityId " + event.getParentId() + " not found");
        license.get().setProperty(PropertyTypeEnum.STRING, "licenseForm", event.getDocumentId());
        log.info("DocumentGeneratedEvent processed: {}", event);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentGeneratedEvent implements IRabbitFanoutSubscriber {
        private UUID parentId;
        private String parentType;
        private String documentId;
    }
}
