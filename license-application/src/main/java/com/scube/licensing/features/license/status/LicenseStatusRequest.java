package com.scube.licensing.features.license.status;

import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatusCodeEnum;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class LicenseStatusRequest {
    private LicenseStatusCodeEnum code;
    private String name;

    public static LicenseStatus to(LicenseStatusRequest request) {
        return new LicenseStatus()
                .setCode(request.getCode())
                .setName(request.getName());
    }
}