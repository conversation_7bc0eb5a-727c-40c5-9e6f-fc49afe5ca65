package com.scube.licensing.features.license.status;

import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.repository.license.LicenseStatusRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

@Service
public class LicenseStatusService {
    private final LicenseStatusRepository licenseStatusRepository;

    public LicenseStatusService(LicenseStatusRepository licenseStatusRepository) {
        this.licenseStatusRepository = licenseStatusRepository;
    }

    //CRUD methods here
    public LicenseStatusResponse createLicenseStatus(LicenseStatusRequest request) {
        var existing = licenseStatusRepository.findByNameIgnoreCase(request.getName());
        if (existing.isPresent()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License status with name " + request.getName() + " already exists");
        }

        var licenseStatus = LicenseStatusRequest.to(request);
        var licenseSave = licenseStatusRepository.save(licenseStatus);
        return LicenseStatusResponse.from(licenseSave);
    }

    private LicenseStatus getById(Long id) {
        var status = licenseStatusRepository.findById(id);
        if (status.isPresent()) return status.get();
        throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License status with id " + id + " not found");
    }

    public LicenseStatusResponse getLicenseStatusById(Long id) {
        var licenseStatus = getById(id);
        return LicenseStatusResponse.from(licenseStatus);
    }

    public LicenseStatusResponse updateLicenseStatus(Long id, LicenseStatusRequest request) {
        var licenseStatus = getById(id);

        var exists = licenseStatusRepository.existsByNameIgnoreCase(request.getName());
        if (exists && !licenseStatus.getName().equals(request.getName())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License status with name " + request.getName() + " already exists");
        }

        licenseStatus.setCode(request.getCode());
        licenseStatus.setName(request.getName());

        var updatedStatus = licenseStatusRepository.save(licenseStatus);
        return LicenseStatusResponse.from(updatedStatus);
    }

    public void deleteLicenseStatusById(Long id) {
        licenseStatusRepository.deleteById(id);
    }

    public GetAllLicenseStatusResponse getAllLicenseStatus() {
        var result = new GetAllLicenseStatusResponse();

        var licenseStatuses = licenseStatusRepository.findAll();
        licenseStatuses.forEach(licenseStatus -> result.addItem(LicenseStatusResponse.from(licenseStatus)));

        return result;
    }
}