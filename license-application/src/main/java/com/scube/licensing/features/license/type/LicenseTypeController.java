package com.scube.licensing.features.license.type;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/config/license-type")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
public class LicenseTypeController {
    private final LicenseTypeService licenseTypeService;

    // Create
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.LicenseType.CREATE_LICENSE_TYPE)
    public ResponseEntity<LicenseTypeResponse> createLicenseType(@RequestBody LicenseTypeRequest request) {
        LicenseTypeResponse response = licenseTypeService.createLicenseType(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    // Read (Get All)
    @GetMapping
    @RolesAllowed(Permissions.LicenseType.GET_ALL_LICENSE_TYPES)
    public ResponseEntity<GetAllLicenseTypeResponse> getAllLicenseTypes() {
        GetAllLicenseTypeResponse response = licenseTypeService.getAllLicenseTypes();
        return ResponseEntity.ok(response);
    }

    // Read (Get by ID)
    @GetMapping("/{id}")
    @RolesAllowed(Permissions.LicenseType.GET_LICENSE_TYPE_BY_ID)
    public ResponseEntity<LicenseTypeResponse> getLicenseTypeById(@PathVariable Long id) {
        LicenseTypeResponse response = licenseTypeService.getLicenseTypeById(id);
        return ResponseEntity.ok(response);
    }

    // Update
    @PutMapping("/{id}")
    @RolesAllowed(Permissions.LicenseType.UPDATE_LICENSE_TYPE)
    public ResponseEntity<LicenseTypeResponse> updateLicenseType(@PathVariable Long id, @RequestBody LicenseTypeRequest request) {
        LicenseTypeResponse response = licenseTypeService.updateLicenseType(id, request);
        return ResponseEntity.ok(response);
    }

    // Delete
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LicenseType.DELETE_LICENSE_TYPE)
    public ResponseEntity<Void> deleteLicenseType(@PathVariable Long id) {
        licenseTypeService.deleteLicenseType(id);
        return ResponseEntity.noContent().build();
    }
}
