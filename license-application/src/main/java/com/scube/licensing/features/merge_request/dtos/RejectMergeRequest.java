package com.scube.licensing.features.merge_request.dtos;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Set;
import java.util.UUID;

public record RejectMergeRequest(@NotNull UUID requestedUserId, @NotEmpty Set<UUID> existingUserIds,
                                 @NotNull @Size(max = 255) String reason, @Nullable @Size(max = 1000) String comment) {
}
