package com.scube.licensing.features.merge_request.events;

import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.Data;

import java.util.UUID;

@Data
public class MergeRequestRejectedEvent implements IRabbitFanoutPublisher {
    private UUID requestedUserId;
    private String searchBy;
    private String searchValue;
    private String reason;
    private String deniedComment;

    public MergeRequestRejectedEvent(MergeRequest request) {
        this.requestedUserId = request.getRequestedUserId();
        this.searchBy = request.getSearchBy();
        this.searchValue = request.getSearchValue();
        this.reason = request.getReason();
        this.deniedComment = request.getDeniedComment();
    }
}