package com.scube.licensing.features.participant;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.lib.misc.annotations.swagger.SwaggerParameter;
import com.scube.licensing.features.participant.dto.CreateParticipantResponseDTO;
import com.scube.licensing.features.participant.swagger.Swagger_ParticipantController_CreateResident;
import com.scube.licensing.features.participant.swagger.Swagger_ParticipantController_UpdateParticipant;
import com.scube.licensing.features.participant.swagger.Swagger_ParticipantController_UpdateParticipantContact;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.validation.NullOrUndefinedToNull;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("participant")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
public class ParticipantController {
    private final ParticipantService participantService;

    @PostMapping("{participantEntityId}/mark-approved")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Mark the resident or dog as approved", description = "The clerk reviews the resident/dog information and mark as approved.")
    @SwaggerParameter(name = "participantEntityId", description = "The entity id of the resident or dog", required = true)
    @RolesAllowed(Permissions.Participant.MARK_APPROVED)
    public void markApproved(@PathVariable UUID participantEntityId) {
        participantService.markAllAsApproved(participantEntityId);
    }

    @PostMapping("{participantEntityId}/clear-approval")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Clear the approval of the resident or dog", description = "If the clerk accidentally approved the resident or dog, the clerk can clear the approval.")
    @SwaggerParameter(name = "participantEntityId", description = "The entity id of the resident or dog", required = true)
    @RolesAllowed(Permissions.Participant.MARK_UNAPPROVED)
    public void markUnapproved(@PathVariable UUID participantEntityId) {
        participantService.clearAllApproval(participantEntityId);
    }

    @PostMapping("{participantEntityId}/mark-online")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Mark the logged in user as registered online", description = "This is used to mark the logged in user as registered online in the resident portal.")
    @RolesAllowed(Permissions.Participant.MARK_ONLINE_RESIDENT)
    public void markOnlineResident(@PathVariable UUID participantEntityId) {
        participantService.martResidentAsOnline(participantEntityId);
    }

    @PostMapping("{participantEntityId}/mark-offline")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Mark the logged in user as offline", description = "This is used to mark the logged in user as offline in the resident portal.")
    @RolesAllowed(Permissions.Participant.MARK_OFFLINE_RESIDENT)
    public void markOfflineResident(@PathVariable UUID participantEntityId) {
        participantService.martResidentAsOffline(participantEntityId);
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @Swagger_ParticipantController_CreateResident
    @RolesAllowed(Permissions.Participant.CREATE_RESIDENT)
    public CreateParticipantResponseDTO createResident(@RequestParam @NullOrUndefinedToNull Map<String, String> fields, @RequestParam Map<String, MultipartFile> files) {
        var result = participantService.createResident(fields, files);
        markApproved(result.getEntityId()); // clerk auto approved
        return result;
    }

    @PatchMapping("/{entityId}/contact")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Swagger_ParticipantController_UpdateParticipantContact
    @RolesAllowed(Permissions.Participant.PATCH_PARTICIPANT_CONTACT)
    public void patchParticipantContact(@PathVariable final UUID entityId, @RequestBody @NullOrUndefinedToNull Map<String, String> request) {
        participantService.patchParticipantContact(entityId, request);
    }

    @PatchMapping(value = "/{participantEntityId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Swagger_ParticipantController_UpdateParticipant
    @RolesAllowed(Permissions.Participant.UPDATE_PARTICIPANT)
    public void updateParticipant(@PathVariable final UUID participantEntityId, @RequestParam(required = false) @NullOrUndefinedToNull Map<String, String> fields, @RequestParam(required = false) Map<String, MultipartFile> files) {
        participantService.patchParticipant(participantEntityId, fields, files);
    }

    @DeleteMapping("{entityId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Delete a participant", description = "This is used to delete a participant.")
    @SwaggerParameter(name = "entityId", description = "This can be the resident or the dog entity id", required = true)
    @RolesAllowed(Permissions.Participant.DELETE_PARTICIPANT)
    public void deleteParticipant(@PathVariable final UUID entityId) {
        participantService.deleteParticipant(entityId);
    }

    @GetMapping("query")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Participant.QUERY)
    public Page<?> query(@PageableDefault(size = 10) Pageable pageable, @RequestParam Map<String, Object> searchParams) {
        return participantService.query(pageable, searchParams);
    }
}