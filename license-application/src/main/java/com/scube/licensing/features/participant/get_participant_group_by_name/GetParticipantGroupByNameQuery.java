package com.scube.licensing.features.participant.get_participant_group_by_name;

import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantGroup;
import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import org.springframework.util.ObjectUtils;


public record GetParticipantGroupByNameQuery(String name) implements IRequestAxon<ParticipantGroup> {
    public GetParticipantGroupByNameQuery {
        if (ObjectUtils.isEmpty(name))
            throw new IllegalArgumentException("Name is required");
    }
}