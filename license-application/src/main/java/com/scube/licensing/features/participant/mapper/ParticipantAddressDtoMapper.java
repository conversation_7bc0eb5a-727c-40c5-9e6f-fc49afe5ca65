package com.scube.licensing.features.participant.mapper;

import com.scube.licensing.features.profile.dto.ParticipantAddressDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddress;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", uses = {EventMapper.class})
public abstract class ParticipantAddressDtoMapper {
    @Mapping(target = "customFields", source = "address.properties")
    @Mapping(target = "participantAddressType", source = "participantAddressType.name")
    @Mapping(target = "participantAddressId", source = "id")
    @Mapping(target = "entityId", source = "address.uuid")
    @Mapping(target = "events", source = "address.events")
    @Mapping(target = "houseNumber", source = "address.houseNumber")
    @Mapping(target = "streetName", source = "address.streetName")
    @Mapping(target = "streetAddress", source = "address.streetAddress")
    @Mapping(target = "streetAddress2", source = "address.streetAddress2")
    @Mapping(target = "town", source = "address.town")
    @Mapping(target = "city", source = "address.city")
    @Mapping(target = "state", source = "address.state")
    @Mapping(target = "zip", source = "address.zip")
    @Mapping(target = "fullAddress", source = "address.fullAddress")
    @Mapping(target = "latitude", source = "address.latitude")
    @Mapping(target = "longitude", source = "address.longitude")
    public abstract ParticipantAddressDto toDto(ParticipantAddress participantAddress);

    public abstract List<ParticipantAddressDto> toDto(Collection<ParticipantAddress> participantAddresses);
}