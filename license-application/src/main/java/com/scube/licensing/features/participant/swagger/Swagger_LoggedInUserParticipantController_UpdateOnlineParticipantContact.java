package com.scube.licensing.features.participant.swagger;

import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.lib.misc.annotations.swagger.SwaggerRequestBody;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@SwaggerInfo(summary = "Update the contact information of the logged in user", description = "This is used to update the contact information of the logged in user in the resident portal.")
@SwaggerRequestBody(
        description = "The request body should be a map of key value pairs. The key should be the contactId and the value should be a phone number or email.",
        content = @Content(examples = {
                @ExampleObject(value = """
                        {
                            "21": "(*************",
                            "23": "<EMAIL>"
                        }
                        """
                )
        })
)
public @interface Swagger_LoggedInUserParticipantController_UpdateOnlineParticipantContact {
}
