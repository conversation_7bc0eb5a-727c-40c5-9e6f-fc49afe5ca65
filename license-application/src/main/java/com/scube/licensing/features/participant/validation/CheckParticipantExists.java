package com.scube.licensing.features.participant.validation;

import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.UUID;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CheckParticipantExists.CheckParticipantExistValidator.class)
public @interface CheckParticipantExists {
    String message() default "owner does not exist. Consider creating one first.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class CheckParticipantExistValidator implements ConstraintValidator<CheckParticipantExists, UUID> {
        private final AxonGateway axonGateway;

        @Autowired
        public CheckParticipantExistValidator(AxonGateway axonGateway) {
            this.axonGateway = axonGateway;
        }

        @Override
        public void initialize(CheckParticipantExists constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(UUID entityId, ConstraintValidatorContext constraintValidatorContext) {
            return axonGateway.query(new ParticipantService.CheckParticipantExistsByEntityIdQuery(entityId));
        }
    }
}