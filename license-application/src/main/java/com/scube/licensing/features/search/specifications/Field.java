package com.scube.licensing.features.search.specifications;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Field {
    private String fieldName;

    @Getter(AccessLevel.NONE)
    private String columnName;

    @JsonProperty("isProperties")
    private boolean isProperties;

    @Getter(AccessLevel.NONE)
    private String searchType;

    @JsonProperty("coalesce")
    private boolean coalesce;

    @JsonProperty("coalesceDefaultValue")
    private String coalesceDefaultValue;

    public String getColumnName() {
        return Objects.requireNonNullElse(columnName, fieldName);
    }

    public SearchType getSearchType() {
        return SearchType.fromValue(searchType);
    }
}