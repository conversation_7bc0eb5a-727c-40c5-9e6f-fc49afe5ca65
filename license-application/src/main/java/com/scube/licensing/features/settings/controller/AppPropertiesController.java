package com.scube.licensing.features.settings.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.features.settings.dto.AppPropertyDto;
import com.scube.licensing.features.settings.dto.AppPropertyMapper;
import com.scube.licensing.features.settings.dto.UpdateAppPropertyRequest;
import com.scube.licensing.features.settings.service.AppPropertiesService;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.factory.Mappers;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("app-properties")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class AppPropertiesController {
    private final AppPropertiesService appPropertiesService;
    private final AppPropertyMapper mapper = Mappers.getMapper(AppPropertyMapper.class);

    @GetMapping
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.AppProperties.GET_ALL_PROPERTIES)
    public List<AppPropertyDto> getAllProperties() {
        return mapper.toDto(appPropertiesService.findAll());
    }

    @GetMapping("/cache")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.AppProperties.GET_CACHE)
    public Map<String, String> getCache() {
        return appPropertiesService.getCache();
    }

    @GetMapping("/{uuid}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.AppProperties.GET_PROPERTY_BY_UUID)
    public AppPropertyDto getPropertyByUuid(@PathVariable UUID uuid) {
        log.info("AppPropertiesController.getPropertyById()");

        return mapper.toDto(appPropertiesService.findByUuid(uuid));
    }

    @GetMapping("/name/{name}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.AppProperties.GET_PROPERTY_BY_NAME)
    public AppPropertyDto getPropertyByName(@PathVariable @Size(max = 255) String name) {
        log.info("AppPropertiesController.getPropertyByName()");

        return mapper.toDto(appPropertiesService.findByName(name));
    }

    @PatchMapping
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.AppProperties.UPDATE_PROPERTY)
    public AppPropertyDto updateProperty(@RequestBody UpdateAppPropertyRequest updateAppPropertyRequest) {
        log.info("AppPropertiesController.updateProperty()");
        return mapper.toDto(appPropertiesService.update(updateAppPropertyRequest));
    }

    @DeleteMapping("/{uuid}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.AppProperties.DELETE_PROPERTY_BY_UUID)
    public void deletePropertyByUuid(@PathVariable UUID uuid) {
        log.info("AppPropertiesController.deletePropertyByUuid()");

        appPropertiesService.deleteByUuid(uuid);
    }
}