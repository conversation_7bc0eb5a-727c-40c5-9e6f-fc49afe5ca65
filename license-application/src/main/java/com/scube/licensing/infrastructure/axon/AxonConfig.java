package com.scube.licensing.infrastructure.axon;

import com.scube.licensing.LicenseServiceApplication;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.middleware.CommandLoggingInterceptor;
import com.scube.licensing.infrastructure.middleware.QueryLoggingInterceptor;
import org.axonframework.commandhandling.CommandBus;
import org.axonframework.commandhandling.SimpleCommandBus;
import org.axonframework.common.transaction.TransactionManager;
import org.axonframework.config.Configurer;
import org.axonframework.queryhandling.QueryBus;
import org.axonframework.queryhandling.QueryUpdateEmitter;
import org.axonframework.queryhandling.SimpleQueryBus;
import org.axonframework.tracing.SpanFactory;
import org.reflections.Reflections;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Configuration
public class AxonConfig {

    @Bean
    public CommandBus commandBus(Configurer configurer,
                                 SpanFactory spanFactory,
                                 TransactionManager transactionManager) {
        var bus = SimpleCommandBus.builder()
                .transactionManager(transactionManager)
                .spanFactory(spanFactory)
                .messageMonitor(configurer.buildConfiguration().messageMonitor(SimpleCommandBus.class, "commandBus"))
                .build();
        bus.registerHandlerInterceptor(new CommandLoggingInterceptor());
        return bus;
    }

    @Bean
    public QueryBus queryBus(Configurer configurer,
                             SpanFactory spanFactory,
                             TransactionManager transactionManager,
                             QueryUpdateEmitter updateEmitter,
                             QueryLoggingInterceptor queryLoggingInterceptor) {
        var bus = SimpleQueryBus.builder()
                .transactionManager(transactionManager)
                .spanFactory(spanFactory)
                .queryUpdateEmitter(updateEmitter)
                .messageMonitor(configurer.buildConfiguration().messageMonitor(SimpleQueryBus.class, "queryBus"))
                .build();
        bus.registerHandlerInterceptor(queryLoggingInterceptor);
        return bus;
    }

    @Bean
    @Order(1)
    CommandLineRunner commandLineRunner() {
        return args -> {
            var reflections = new Reflections(LicenseServiceApplication.class.getPackageName());
            var requestHandlers = reflections.getSubTypesOf(IRequestHandlerAxon.class);
            requestHandlers.forEach(requestHandler -> {
                var component = requestHandler.getAnnotation(Component.class);
                if (component == null) {
                    throw new RuntimeException("IRequestHandlerAxon " + requestHandler.getName() + " must be annotated with @Component");
                }
            });
        };
    }
}