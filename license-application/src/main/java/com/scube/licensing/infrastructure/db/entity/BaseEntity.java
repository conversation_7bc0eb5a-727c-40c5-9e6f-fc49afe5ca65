package com.scube.licensing.infrastructure.db.entity;


import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@MappedSuperclass
@SuperBuilder
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public abstract class BaseEntity extends AuditableEntity implements IBaseEntity {

}