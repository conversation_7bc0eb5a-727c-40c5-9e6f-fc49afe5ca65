package com.scube.licensing.infrastructure.db.entity.custom_entity;

import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = CustomEntityInstance.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class CustomEntityInstance extends Associable {
    public static final String TABLE_NAME = "custom_entity_instance";
    public static final String ENTITY_TYPE = "customEntity";
    public static final String CUSTOM_ENTITY_INSTANCE_ID = "custom_entity_instance_id";
    public static final String ENTITY_NUMBER = "entity_number";

    @Size(max = 50)
    @Column(name = ENTITY_NUMBER, length = 50)
    private String entityNumber;

    @ManyToOne(cascade = {CascadeType.ALL})
    @JoinColumn(name = CustomEntitySubType.CUSTOM_ENTITY_SUB_TYPE_ID)
    private CustomEntitySubType customEntitySubType;

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.CUSTOM_ENTITY;


    @OneToMany(mappedBy = "parentCustomEntityInstance", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childCustomEntityInstance", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}