package com.scube.licensing.infrastructure.db.entity.license.type;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.persistence.*;
import org.hibernate.envers.Audited;

@Entity
@Table(name = LicenseTypeSetting.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class LicenseTypeSetting extends BaseEntity {
    public static final String TABLE_NAME = "license_type_setting";
    public static final String LICENSE_TYPE_ID = "license_type_setting_id";

    @ManyToOne
    @JoinColumn(name = "license_type_id", unique = true)
    private LicenseType licenseType;

    @ManyToOne
    @JoinColumn(name = "on_initial_form_create_license_status_id")
    private LicenseStatus onInitialFormCreateLicenseStatus;

    @ManyToOne
    @JoinColumn(name = "partial_save_license_status_id")
    private LicenseStatus partialSaveLicenseStatus;

    @ManyToOne
    @JoinColumn(name = "on_form_submit_license_status_id")
    private LicenseStatus onFormSubmitLicenseStatus;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}