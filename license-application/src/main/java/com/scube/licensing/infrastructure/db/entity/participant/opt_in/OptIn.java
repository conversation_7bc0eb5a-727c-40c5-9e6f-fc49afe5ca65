package com.scube.licensing.infrastructure.db.entity.participant.opt_in;

import com.scube.auth.library.enabled_true.AuthUtils;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import org.springframework.lang.NonNull;

import java.time.Instant;

@Getter
public class OptIn {
    @Size(max = 255)
    private final String name;
    private boolean active;
    private final String createdBy;
    private final Instant createdDate;
    private String lastModifiedBy;
    private Instant lastModifiedDate;

    public OptIn(@NonNull @Size(max = 255) String name, boolean active) {
        this.name = name;
        this.active = active;
        this.createdBy = AuthUtils.getLoggedInUserPreferredUsername();
        this.createdDate = Instant.now();
        this.lastModifiedBy = AuthUtils.getLoggedInUserPreferredUsername();
        this.lastModifiedDate = Instant.now();
    }

    public void update(boolean active) {
        this.active = active;
        this.lastModifiedBy = AuthUtils.getLoggedInUserPreferredUsername();
        this.lastModifiedDate = Instant.now();
    }
}