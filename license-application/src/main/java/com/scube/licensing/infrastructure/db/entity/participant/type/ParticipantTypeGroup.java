package com.scube.licensing.infrastructure.db.entity.participant.type;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.springframework.lang.Nullable;

@Entity
@Table(
        name = ParticipantTypeGroup.TABLE_NAME,
        uniqueConstraints = {
                @UniqueConstraint(
                        name = "participant_type_group_unique",
                        columnNames = {
                                ParticipantGroup.PARTICIPANT_GROUP_ID,
                                ParticipantType.PARTICIPANT_TYPE_ID
                        }
                )
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ParticipantTypeGroup extends BaseEntity {
    public static final String TABLE_NAME = "participant_type_group";
    public static final String PARTICIPANT_TYPE_GROUP_ID = "participant_type_group_id";

    @Column(name = ParticipantGroup.PARTICIPANT_GROUP_ID, insertable = false, updatable = false)
    private Long participantGroupId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = ParticipantGroup.PARTICIPANT_GROUP_ID, nullable = false)
    private ParticipantGroup participantGroup;

    @Column(name = ParticipantType.PARTICIPANT_TYPE_ID, insertable = false, updatable = false)
    private Long participantTypeId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = ParticipantType.PARTICIPANT_TYPE_ID, nullable = false)
    private ParticipantType participantType;

    public ParticipantTypeGroup(ParticipantGroup participantGroup, ParticipantType participantType) {
        this.participantGroup = participantGroup;
        this.participantType = participantType;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    @Nullable
    public String getGroupNameToLowerCase() {
        var groupName = getGroupName();
        return groupName != null ? groupName.toLowerCase() : null;
    }

    @Nullable
    public String getGroupName() {
        if (participantGroup != null) {
            return participantGroup.getName();
        }
        return null;
    }

    @Nullable
    public String getTypeName() {
        if (participantType != null) {
            return participantType.getName();
        }
        return null;
    }
}