package com.scube.licensing.infrastructure.db.repository.entity_fee;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface EntityFeeRepository extends AuditableEntityRepository<EntityFee, Long> {
    List<EntityFee> findAllByOrderId(UUID orderId);

    @Query(value = """
            SELECT *
            FROM entity_fee
            WHERE (
                    properties->>'startDate' IS NULL
                    or CAST(NULLIF(properties->>'startDate', '') as DATE) <= CURRENT_DATE
                )
                AND (
                    properties->>'endDate' IS NOT NULL
                    AND CAST(NULLIF(properties->>'endDate', '')as DATE) >= CURRENT_DATE
                )
                AND COALESCE(properties->>'cronExpression', '') != ''
                AND COALESCE(properties->>'isStopped', 'false') = 'false'
            """, nativeQuery = true)
    List<EntityFee> findActiveEntityFeesWithCronExpression();
}