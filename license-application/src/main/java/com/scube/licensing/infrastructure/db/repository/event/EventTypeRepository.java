package com.scube.licensing.infrastructure.db.repository.event;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface EventTypeRepository extends AuditableEntityRepository<EventType, Long> {
    Optional<EventType> findByCode(@Size(max = 255) String code);

    boolean existsByCode(@Size(max = 255) String code);

    Optional<EventType> findByName(@Size(max = 255) String name);

    @Query("SELECT et FROM EventType et WHERE et.profileType.name = :profileTypeName")
    List<EventType> findByProfileTypeName(@Size(max = 255) String profileTypeName);
}