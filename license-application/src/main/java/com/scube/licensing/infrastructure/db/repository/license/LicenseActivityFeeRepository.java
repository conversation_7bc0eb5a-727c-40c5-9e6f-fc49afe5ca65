package com.scube.licensing.infrastructure.db.repository.license;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivityFee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public interface LicenseActivityFeeRepository extends AuditableEntityRepository<LicenseActivityFee, Long> {
    @Modifying
    @Transactional
    @Query("UPDATE LicenseActivityFee l SET l.paymentStatus = :paymentStatus, l.paidDate = :paidDate WHERE l.orderId = :orderId")
    void updatePaymentStatusByOrderId(
            @Param("paymentStatus") LicenseActivityFee.PaymentStatus paymentStatus,
            @Param("orderId") UUID orderId,
            @Param("paidDate") Instant paidDate
    );

    List<LicenseActivityFee> findAllByOrderId(UUID orderId);
}