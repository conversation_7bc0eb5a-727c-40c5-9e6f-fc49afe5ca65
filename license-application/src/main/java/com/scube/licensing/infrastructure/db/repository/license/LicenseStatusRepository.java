package com.scube.licensing.infrastructure.db.repository.license;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatusCodeEnum;
import jakarta.validation.constraints.Size;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LicenseStatusRepository extends AuditableEntityRepository<LicenseStatus, Long> {
    Optional<LicenseStatus> findByNameIgnoreCase(@Size(max = 255) String name);
    List<LicenseStatus> findAllByCodeIn(List<LicenseStatusCodeEnum> codes);
    boolean existsByNameIgnoreCase(@Size(max = 255) String name);
}
