package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.status.ParticipantStatus;
import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantGroup;
import jakarta.validation.constraints.Size;

import java.util.Optional;

public interface ParticipantStatusRepository extends AuditableEntityRepository<ParticipantStatus, Long> {
    Optional<ParticipantStatus> findByParticipantGroupAndNameIgnoreCase(ParticipantGroup participantGroup, @Size(max = 255) String name);

    boolean existsByParticipantGroupAndNameIgnoreCase(ParticipantGroup participantGroup, @Size(max = 255) String name);
}
