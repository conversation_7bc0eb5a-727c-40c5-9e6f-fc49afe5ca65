package com.scube.licensing.infrastructure.db.repository.profile;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.profile_builder.ProfileType;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProfileTypeRepository extends AuditableEntityRepository<ProfileType, Long> {
    Optional<ProfileType> findByNameIgnoreCase(@Size(max = 255) String name);

    boolean existsByNameIgnoreCase(@Size(max = 255) String name);

    @Query(nativeQuery = true, value = "SELECT * FROM get_profile_header(:profileType, :entityId)")
    List<Map<String, Object>> getProfileHeader(@Param("profileType") @Size(max = 255) String profileType, @Param("entityId") UUID entityId);

    @Query(nativeQuery = true, value = "SELECT * FROM get_profile_affiliation(:profileType, :entityId)")
    List<Map<String, Object>> getProfileAffiliation(@Param("profileType") @Size(max = 255) String profileType, @Param("entityId") UUID entityId);
}