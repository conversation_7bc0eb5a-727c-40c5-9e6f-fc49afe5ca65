package com.scube.licensing.infrastructure.middleware;

import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandMessage;
import org.axonframework.messaging.InterceptorChain;
import org.axonframework.messaging.MessageHandlerInterceptor;
import org.axonframework.messaging.unitofwork.UnitOfWork;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(1)
@Slf4j
public class CommandLoggingInterceptor implements MessageHandlerInterceptor<CommandMessage<?>> {
    @Override
    public Object handle(UnitOfWork<? extends CommandMessage<?>> unitOfWork, InterceptorChain interceptorChain) throws Exception {
        var commandName = unitOfWork.getMessage().getPayloadType().getPackageName();
        log.info("Start {}", commandName);

        var result = interceptorChain.proceed();

        log.info("End {}", commandName);

        return result;
    }
}