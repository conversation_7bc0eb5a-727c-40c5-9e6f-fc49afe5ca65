spring:
  application:
    name: LicenseService
  threads:
    virtual:
      enabled: false
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB

  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/db.changelog-master.xml

  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    properties:
      hibernate:
        format_sql: true

  mvc:
    async:
      request-timeout: 1200000

logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"
    org.hibernate: "error"
    liquibase: "info"
    com.scube.licensing: "debug"
    com.scube: "debug"



keycloak:
  authoritiesConverterType: NATIVE
  host: http://localhost:8443/realms/master
  public-host: http://localhost:8443/realms/master
  admin:
    url: http://localhost:8443
    realm: master
    client-id: test
    client-secret: test
  swagger:
    url: http://localhost:8443
    realm: clerkXpress
    client-id: test

com.c4-soft.springaddons.oidc:
  ops:
    - iss: http://localhost:8443/realms/master
      jwkSetUri: http://localhost:8443/realms/master/protocol/openid-connect/certs
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - http://localhost:3030

multi-tenancy:
  enabled: false
  keycloak:
    enabled: false
  database:
    enabled: false

GOOGLE_MAPS_API_KEY: test
# valid values: google, nominatim
geocoding.type: "mock"
nominatim.api.url: "https://test"

com.scube.client:
  auth: "http://${CLIENT_LIB_HOST:localhost}:9001/api/auth"
  document: "http://${CLIENT_LIB_HOST:localhost}:9003/api/document-service"
  config: "http://${CLIENT_LIB_HOST:localhost}:10001/api/config"
  documentTemplate: "http://${CLIENT_LIB_HOST:localhost}:9009/api/document-template"
  calculation: "http://${CLIENT_LIB_HOST:localhost}:9002/api/calculation"
  license: "http://${CLIENT_LIB_HOST:localhost}:9004/api/license"
