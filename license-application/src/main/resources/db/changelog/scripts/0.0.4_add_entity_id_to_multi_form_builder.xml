<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1692382384079-1">
        <addColumn tableName="multi_form_builder">
            <column name="entity_id" type="UUID">
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="davidr (generated)" id="1692382384079-2">
        <sql>
            UPDATE multi_form_builder SET entity_id = gen_random_uuid()::UUID WHERE
            entity_id IS NULL;
        </sql>
    </changeSet>

    <changeSet author="davidr (generated)" id="1692382384079-3">
        <addUniqueConstraint columnNames="entity_id" constraintName="uk_j3ta166lgj76nbmhj8a1w0236"
                             tableName="multi_form_builder"/>
        <!-- add not null constraint-->
        <addNotNullConstraint columnDataType="UUID" columnName="entity_id" tableName="multi_form_builder"/>
    </changeSet>
</databaseChangeLog>
