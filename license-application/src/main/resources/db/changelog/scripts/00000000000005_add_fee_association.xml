<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1692989933202-1">
        <createTable tableName="license_fee">
            <column autoIncrement="true" name="license_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="license_fee_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="license_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692989933202-2">
        <createTable tableName="license_type_fee">
            <column autoIncrement="true" name="license_type_fee_id" startWith="12" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="license_type_fee_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="license_type_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692989933202-3">
        <createTable tableName="license_type_fee_conditional">
            <column autoIncrement="true" name="license_type_fee_conditional_id" startWith="10" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="license_type_fee_conditional_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="custom_field_name" type="VARCHAR(255)"/>
            <column name="field_values" type="BYTEA"/>
            <column name="license_type_fee_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692989933202-4">
        <addForeignKeyConstraint baseColumnNames="license_type_id" baseTableName="license_type_fee" constraintName="fk72nh6ebe9gxtpqgpm71vq0m4x" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="license_type_id" referencedTableName="license_type" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692989933202-5">
        <addForeignKeyConstraint baseColumnNames="license_id" baseTableName="license_fee" constraintName="fk8kbbcvuqr93ixcsjvsoturvux" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="license_id" referencedTableName="license" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692989933202-6">
        <addForeignKeyConstraint baseColumnNames="license_type_fee_id" baseTableName="license_type_fee_conditional" constraintName="fk8p7khv4yehxm7gobj0aa3o1li" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="license_type_fee_id" referencedTableName="license_type_fee" validate="true"/>
    </changeSet>
</databaseChangeLog>
