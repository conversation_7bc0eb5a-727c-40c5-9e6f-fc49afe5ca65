<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1693722439901-5">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="association_value_entry_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-6">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="domain_event_entry_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-7">
        <createTable tableName="license_type_fee_conditional_field_values">
            <column name="license_type_fee_conditional_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="field_values" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-8">
        <createTable tableName="multi_form_conditional_display_field_values">
            <column name="multi_form_conditional_display_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="multi_form_conditional_display_field_values_pkey"/>
            </column>
            <column name="field_values" type="VARCHAR(100)"/>
            <column name="field_values_order" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="multi_form_conditional_display_field_values_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-9">
        <addForeignKeyConstraint baseColumnNames="license_type_fee_conditional_id" baseTableName="license_type_fee_conditional_field_values" constraintName="fk1t6ee214p1x3ymj1aj6x4s64p" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="license_type_fee_conditional_id" referencedTableName="license_type_fee_conditional" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-10">
        <addForeignKeyConstraint baseColumnNames="multi_form_conditional_display_id" baseTableName="multi_form_conditional_display_field_values" constraintName="fkc23m7hib8un33c8rq9vx2f5bd" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="multi_form_conditional_display_id" referencedTableName="multi_form_conditional_display" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-11">
        <dropColumn columnName="field_values" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-12">
        <dropColumn columnName="field_values" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-13">
        <dropSequence sequenceName="hibernate_sequence"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-1">
        <modifyDataType columnName="enqueued_at" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-2">
        <modifyDataType columnName="last_touched" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-3">
        <modifyDataType columnName="name" newDataType="varchar(100)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1693722439901-4">
        <modifyDataType columnName="processing_started" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
</databaseChangeLog>
