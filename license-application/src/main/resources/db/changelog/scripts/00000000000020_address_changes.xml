<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr" id="1695843694567-1">
        <sql splitStatements="true">
            DROP VIEW IF EXISTS view_pivoted_license;
            DROP VIEW IF EXISTS view_license_for_reports;
            DROP VIEW IF EXISTS view_dog_licenses;
            DROP VIEW IF EXISTS view_participant;
            DROP VIEW IF EXISTS view_contact;
            DROP VIEW IF EXISTS view_custom_field_value;
            drop function if exists insert_update_custom_field_value;
            drop function if exists get_participants;
            drop function if exists get_dogs;
            drop function if exists get_parcels;
            drop function if exists get_licenses;
        </sql>
    </changeSet>
    <changeSet author="davidr" id="1695843694567-2">
        <modifyDataType tableName="address" columnName="house_number" newDataType="VARCHAR(255)"/>
    </changeSet>
    <changeSet author="davidr" id="1695843694567-3">
        <modifyDataType tableName="address" columnName="city" newDataType="VARCHAR(255)"/>
    </changeSet>
    <changeSet author="davidr" id="1695843694567-4">
        <modifyDataType tableName="address" columnName="state" newDataType="VARCHAR(255)"/>
    </changeSet>
    <changeSet author="davidr" id="1695843694567-5">
        <modifyDataType tableName="address" columnName="zip" newDataType="VARCHAR(255)"/>
    </changeSet>
</databaseChangeLog>