<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <!-- 1. Drop the existing foreign key constraint -->
    <changeSet author="benj" id="modify-fk-cascade-1">
        <dropForeignKeyConstraint baseTableName="license_activity" constraintName="fk632xb116gmjr0no5rb8smylb6"/>
        <dropForeignKeyConstraint baseTableName="license_activity_fee" constraintName="fk3q03bys6i6gluehlfe9a80xr2"/>
    </changeSet>

    <!-- 2. Add a new foreign key constraint with cascade delete -->
    <changeSet author="benj" id="modify-fk-cascade-2">
        <addForeignKeyConstraint baseColumnNames="license_id" baseTableName="license_activity"
                                 constraintName="fk632xb116gmjr0no5rb8smylb6"
                                 onDelete="CASCADE" onUpdate="NO ACTION"
                                 referencedColumnNames="license_id" referencedTableName="license"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695002604577-133">
        <addForeignKeyConstraint baseColumnNames="license_activity_id" baseTableName="license_activity_fee"
                                 constraintName="fk3q03bys6i6gluehlfe9a80xr2"
                                 onDelete="CASCADE" onUpdate="NO ACTION"
                                 referencedColumnNames="license_activity_id" referencedTableName="license_activity"
                                 validate="true"/>
    </changeSet>
</databaseChangeLog>