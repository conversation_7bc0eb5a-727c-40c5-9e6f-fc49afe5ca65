<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Ben (generated)" id="1699994737214-147">
        <addColumn tableName="address">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1699994737214-152">
        <addColumn tableName="custom_entity_instance">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1699994737214-153">
        <addColumn tableName="license">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1699994737214-154">
        <addColumn tableName="participant">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1699994737214-148">
        <addColumn tableName="audit_log_address">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1699994737214-149">
        <addColumn tableName="audit_log_custom_entity_instance">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1699994737214-150">
        <addColumn tableName="audit_log_license">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1699994737214-151">
        <addColumn tableName="audit_log_participant">
            <column name="events" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="addEventTypeTable">
        <createTable tableName="event_type">
            <column autoIncrement="true" name="event_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="event_type_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="makeEventTypeCodeUnique">
        <addUniqueConstraint columnNames="code" constraintName="uk_event_type_code" tableName="event_type"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="addAuditLogEventTypeTable">
        <createTable tableName="audit_log_event_type">
            <column name="event_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_event_type_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_event_type_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="addAuditLogEventTypeRevisionIdConstraint">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_event_type" constraintName="fk4hwc3i69c0wbn9p2gk9fxoyfa" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet id="insertTransferOfOwnershipEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'transferOfOwnership', 'Transfer of Ownership', 'Transfer of Ownership')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertDangerousDogEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dangerousDog', 'Dangerous Dog', 'Dangerous Dog')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertAddressChangeEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'addressChange', 'Address Change', 'Address Change')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
</databaseChangeLog>
