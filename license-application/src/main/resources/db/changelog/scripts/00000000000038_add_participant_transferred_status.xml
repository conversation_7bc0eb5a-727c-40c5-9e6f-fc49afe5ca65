<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="insertParticipantStatusDogTransferred" author="Ben">
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code, participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Transferred', 'INACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertLicenseReactivatedEvent" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'licenseReactivated', 'License Reactivated', 'License Reactivated',
            (select profile_type_id from profile_type where name = 'license'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
</databaseChangeLog>
