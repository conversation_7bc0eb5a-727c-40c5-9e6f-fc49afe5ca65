<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="David" id="update-createdby-to-clerk-fn-create-view-participant2">
        <sql splitStatements="false">
            CREATE OR REPLACE VIEW view_participant AS
            SELECT
            p.participant_id,
            p.entity_id,
            p.name as participant_name,
            pg.name as group_name,
            pt.name as type_name,
            ps.name as status_name
            from participant p

            left join participant_type_group ptg
            on ptg.participant_type_group_id = p.participant_type_group_id

            left join participant_group pg
            on pg.participant_group_id = ptg.participant_group_id

            left join participant_type pt
            on pt.participant_type_id = ptg.participant_type_id

            left join participant_status ps
            on ps.participant_status_id = p.participant_status_id;
        </sql>
    </changeSet>
    <changeSet author="David" id="update-createdby-to-clerk-fn-create-view-custom-field-value2">
        <sql splitStatements="false">
            CREATE OR REPLACE VIEW view_custom_field_value AS
            SELECT
            tt.name as table_name,
            tcf.name as field_name,
            cv.parent_id,
            cv.value_type,
            cv.date_value,
            cv.datetime_value,
            cv.int_value,
            casted_string_value string_value,
            coalesce(p.entity_id, l.entity_id) as entity_id,
            cv.boolean_value,
            cv.custom_field_value_id

            FROM custom_field_value cv
            JOIN table_custom_field tcf
            ON tcf.table_custom_field_id = cv.table_custom_field_id
            JOIN table_type tt
            ON tt.table_type_id = tcf.table_type_id

            left join participant p
            on p.participant_id = cv.parent_id
            and tt.name = 'participant'
            left join license l
            on l.license_id = cv.parent_id
            and tt.name = 'license';
        </sql>
    </changeSet>
    <changeSet author="David" id="update-createdby-to-clerk-fn-create2">
        <sql splitStatements="false">
            CREATE OR REPLACE FUNCTION insert_update_custom_field_value(
            in fn_table_name varchar(100),
            in fn_field_name varchar(2000),
            in fn_parent_id bigint,
            in fn_value_type varchar(100),
            in fn_value varchar(2000),
            in fn_audit_user varchar(2000) default 'fn',
            in fn_conversion_reference varchar(255) default ''
            ) RETURNS void
            LANGUAGE plpgsql
            AS '
            DECLARE
            var_table_custom_field_id bigint;
            var_table_type_id bigint;
            var_custom_field_value_id bigint;
            BEGIN
            select table_type_id
            into var_table_type_id
            from table_type tt
            where tt.name = fn_table_name;

            var_table_custom_field_id := (
            select table_custom_field_id
            from table_custom_field tcf
            where tcf.name = fn_field_name
            and table_type_id in (var_table_type_id)
            );

            -- create table_custom_field if not exists
            IF COALESCE(var_table_custom_field_id, 0) &lt;= 0 and var_table_type_id > 0 then
            insert into table_custom_field(created_by, created_date, last_modified_by, last_modified_date, code, name,
            table_type_id, conversion_reference)
            values(fn_audit_user, now(), fn_audit_user, now(), fn_field_name, fn_field_name, var_table_type_id,
            fn_conversion_reference)
            RETURNING table_custom_field_id INTO var_table_custom_field_id;
            end if;

            --check if var_table_custom_field_id is null or empty and raise exception
            IF COALESCE(var_table_custom_field_id, 0) &lt;= 0 then
            RAISE EXCEPTION ''Custom field with name "%" not found for table "%"'', fn_field_name, fn_table_name;
            END IF;

            -- handle custom field value updates
            var_custom_field_value_id := (
            select custom_field_value_id from custom_field_value cfv
            where cfv.table_custom_field_id = var_table_custom_field_id
            and parent_id = fn_parent_id
            );

            if coalesce(var_custom_field_value_id, 0) &lt;= 0 then
            -- insert
            --string
            IF fn_value_type = ''string'' then
            INSERT INTO custom_field_value (
            value_type, created_by, created_date, last_modified_by, last_modified_date,
            parent_id, string_value, table_custom_field_id, conversion_reference
            )
            values(
            ''string'', fn_audit_user, now(), fn_audit_user, now(),
            fn_parent_id, fn_value, var_table_custom_field_id, fn_conversion_reference
            );

            END IF;

            --date
            IF fn_value_type = ''date'' and coalesce(fn_value,'''') != '''' then
            INSERT INTO custom_field_value (
            value_type, created_by, created_date, last_modified_by, last_modified_date,
            parent_id, date_value, table_custom_field_id, conversion_reference
            )
            values(
            ''date'', fn_audit_user, now(), fn_audit_user, now(),
            fn_parent_id, fn_value::date, var_table_custom_field_id, fn_conversion_reference
            );

            END IF;

            --datetime
            IF fn_value_type = ''datetime'' and coalesce(fn_value,'''') != '''' then
            INSERT INTO custom_field_value (
            value_type, created_by, created_date, last_modified_by, last_modified_date,
            parent_id, datetime_value, table_custom_field_id, conversion_reference
            )
            values(
            ''datetime'', fn_audit_user, now(), fn_audit_user, now(),
            fn_parent_id, fn_value::timestamp, var_table_custom_field_id, fn_conversion_reference
            );

            END IF;

            --int
            IF fn_value_type = ''int'' then
            INSERT INTO custom_field_value (
            value_type, created_by, created_date, last_modified_by, last_modified_date,
            parent_id, int_value, table_custom_field_id, conversion_reference
            )
            values(
            ''int'', fn_audit_user, now(), fn_audit_user, now(),
            fn_parent_id, fn_value::int, var_table_custom_field_id, fn_conversion_reference
            );

            END IF;

            --boolean
            IF fn_value_type = ''boolean'' then
            INSERT INTO custom_field_value (
            value_type, created_by, created_date, last_modified_by, last_modified_date,
            parent_id, boolean_value, table_custom_field_id, conversion_reference
            )
            values(
            ''boolean'', fn_audit_user, now(), fn_audit_user, now(),
            fn_parent_id, fn_value::boolean, var_table_custom_field_id, fn_conversion_reference
            );

            END IF;
            else
            -- update
            update custom_field_value
            set last_modified_by = fn_audit_user,
            last_modified_date = now(),
            --update conversion_reference if not empty
            conversion_reference = case when coalesce(fn_conversion_reference,'''') != '''' then fn_conversion_reference
            else conversion_reference end,
            value_type = fn_value_type,
            string_value = case when fn_value_type = ''string'' then fn_value else null end,
            date_value = case when fn_value_type = ''date'' then fn_value::date else null end,
            datetime_value = case when fn_value_type = ''datetime'' then fn_value::timestamp else null end,
            int_value = case when fn_value_type = ''int'' then fn_value::int else null end,
            boolean_value = case when fn_value_type = ''boolean'' then fn_value::boolean else null end
            where custom_field_value_id = var_custom_field_value_id;
            end if;
            END;
            ';
        </sql>
    </changeSet>
    <changeSet author="David" id="update-createdby-to-clerk">
        <sql>
            select
            insert_update_custom_field_value(
            'participant', 'dogName', p.participant_id , 'string',
            'No Name Provided', '', 'dog name set to no name provided'
            )
            from participant p
            inner join participant_type_group ptg
            on ptg.participant_type_group_id = p.participant_type_group_id
            inner join participant_group pg
            on pg.participant_group_id = ptg.participant_group_id
            and pg."name" = 'Dog'
            where not exists (
            select null from view_custom_field_value vcfv
            where vcfv.table_name = 'participant'
            and vcfv.field_name = 'dogName'
            and coalesce(vcfv.string_value,'') != ''
            and vcfv.parent_id = p.participant_id
            );
        </sql>
    </changeSet>
    <changeSet author="David" id="migrate_licenseExempt_from_license_to_dog">
        <sql>
            select insert_update_custom_field_value(
            'participant', 'licenseExempt', vp.participant_id , 'boolean',
            'true', '', 'license exemption update'
            )
            from license l
            inner JOIN association a
            ON a.parent_association_type = 'LICENSE'
            AND a.child_association_type = 'PARTICIPANT'
            AND a.parent_id = l.license_id
            inner join view_participant vp
            on vp.participant_id = a.child_id
            and vp.group_name Ilike 'dog'
            left join view_custom_field_value dvcfv
            on dvcfv.field_name = 'licenseExempt'
            and dvcfv.table_name = 'participant'
            and dvcfv.parent_id = vp.participant_id
            where l.license_id in (
            select vcfv.parent_id from view_custom_field_value vcfv
            where vcfv.field_name = 'licenseExempt'
            and vcfv.table_name = 'license'
            and (vcfv.boolean_value = true or vcfv.string_value ilike 'true')
            )
        </sql>
    </changeSet>
    <changeSet author="David" id="update_boolean_field_to_stay_bool">
        <sql>
            update custom_field_value cfv
            set
            value_type = 'boolean',
            boolean_value = (case when casted_string_value ilike 'true' then true else false end),
            date_value = null,
            datetime_value = null,
            int_value = null,
            string_value = null
            where cfv.custom_field_value_id in (
            select
            vcfv.custom_field_value_id
            from view_custom_field_value vcfv
            where field_name in (
            select distinct field_name from view_custom_field_value where value_type ilike '%bool%'
            )
            and value_type != 'boolean'
            )
        </sql>
    </changeSet>
</databaseChangeLog>