<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="David" id="00000000000047-01">
        <createTable tableName="merge_request">
            <column autoIncrement="true" name="merge_request_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" unique="true"/>
            </column>
            <column name="entity_id" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="requested_user_id" type="UUID"/>
            <column name="existing_user_id" type="UUID"/>
            <column name="tag_number" type="VARCHAR(255)"/>
            <column name="license_number" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="David" id="00000000000047-02">
        <createTable tableName="audit_log_merge_request">
            <column name="merge_request_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revision_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revision_type" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="requested_user_id" type="UUID"/>
            <column name="existing_user_id" type="UUID"/>
            <column name="tag_number" type="VARCHAR(255)"/>
            <column name="license_number" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <!--    alter the existing_user_id in both tables to jsonb-->
    <changeSet author="David" id="00000000000047-03">
        <addColumn tableName="merge_request">
            <column name="existing_user_id_jsonb" type="jsonb"/>
        </addColumn>
        <dropColumn tableName="merge_request" columnName="existing_user_id"/>
        <renameColumn tableName="merge_request" oldColumnName="existing_user_id_jsonb"
                      newColumnName="existing_users"/>
    </changeSet>

    <changeSet author="David" id="00000000000047-04">
        <addColumn tableName="audit_log_merge_request">
            <column name="existing_user_id_jsonb" type="jsonb"/>
        </addColumn>
        <dropColumn tableName="audit_log_merge_request" columnName="existing_user_id"/>
        <renameColumn tableName="audit_log_merge_request" oldColumnName="existing_user_id_jsonb"
                      newColumnName="existing_users"/>
    </changeSet>
    <!--    delete everything from the merge_request -->
    <changeSet author="David" id="00000000000047-05">
        <delete tableName="merge_request"/>
    </changeSet>
    <changeSet author="David" id="00000000000047-06">
        <delete tableName="audit_log_merge_request"/>
    </changeSet>

    <!--    add reason and deniedComment column-->
    <changeSet author="David" id="addColumn_reason_merge_request">
        <addColumn tableName="merge_request">
            <column name="reason" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="addColumn_denied_comment_merge_request">
        <addColumn tableName="merge_request">
            <column name="denied_comment" type="VARCHAR(2000)"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="addColumn_reason_audit_log_merge_request">
        <addColumn tableName="audit_log_merge_request">
            <column name="reason" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="addColumn_denied_comment_audit_log_merge_request">
        <addColumn tableName="audit_log_merge_request">
            <column name="denied_comment" type="VARCHAR(2000)"/>
        </addColumn>
    </changeSet>


</databaseChangeLog>