<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="David" id="participant_addColumn_associationType">
        <addColumn tableName="participant">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="PARTICIPANT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="audit_log_participant_addColumn_associationType">
        <addColumn tableName="audit_log_participant">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="PARTICIPANT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="David" id="address_addColumn_associationType">
        <addColumn tableName="address">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="ADDRESS">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="audit_log_address_addColumn_associationType">
        <addColumn tableName="audit_log_address">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="ADDRESS">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="David" id="custom_entity_instance_addColumn_associationType">
        <addColumn tableName="custom_entity_instance">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="CUSTOM_ENTITY">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="audit_log_custom_entity_instance_addColumn_associationType">
        <addColumn tableName="audit_log_custom_entity_instance">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="CUSTOM_ENTITY">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="David" id="license_addColumn_associationType">
        <addColumn tableName="license">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="LICENSE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="audit_log_license_addColumn_associationType">
        <addColumn tableName="audit_log_license">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="LICENSE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="David" id="document_addColumn_associationType">
        <addColumn tableName="document">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="DOCUMENT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="audit_log_document_addColumn_associationType">
        <addColumn tableName="audit_log_document">
            <column name="association_type" type="varchar(255)" defaultOnNull="true" defaultValue="DOCUMENT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>


</databaseChangeLog>