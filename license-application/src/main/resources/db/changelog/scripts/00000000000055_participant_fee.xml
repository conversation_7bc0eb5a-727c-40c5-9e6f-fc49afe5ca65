<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1712610284451-67">
        <createTable tableName="audit_log_participant_fee">
            <column name="participant_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_participant_fee_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_participant_fee_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="participant_fee_uuid" type="UUID"/>
            <column name="amount" type="numeric(38, 2)"/>
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="order_id" type="UUID"/>
            <column name="paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="payment_status" type="VARCHAR(255)"/>
            <column name="participant_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-68">
        <createTable tableName="participant_fee">
            <column autoIncrement="true" name="participant_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="participant_fee_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="participant_fee_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="numeric(38, 2)"/>
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="order_id" type="UUID"/>
            <column name="paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="payment_status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="participant_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-69">
        <createIndex indexName="idx_document_document_type_id" tableName="document">
            <column name="document_type_id"/>
        </createIndex>
        <createIndex indexName="idx_document_document_uuid" tableName="document">
            <column name="document_uuid"/>
        </createIndex>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-70">
        <createIndex indexName="idx_participant_participant_status_id" tableName="participant">
            <column name="participant_status_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-71">
        <addUniqueConstraint columnNames="participant_fee_uuid" constraintName="uk_tlb7tnmam8gq0hu1crk9btuiu" tableName="participant_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-72">
        <createIndex indexName="idx_profile_name" tableName="profile_type">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-74">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_participant_fee" constraintName="fk20iovne24sy5htldiedc0uu4i" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-75">
        <addForeignKeyConstraint baseColumnNames="participant_id" baseTableName="participant_fee" constraintName="fk4q0bkr08xt34yyqxt2l0rkrar" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="participant_id" referencedTableName="participant" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-86">
        <sql>
            drop index if exists idx_license_activity_conversion_reference;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="1712610284451-54">
        <addNotNullConstraint columnDataType="varchar(255)" columnName="name" tableName="profile_type" validate="true"/>
    </changeSet>
</databaseChangeLog>