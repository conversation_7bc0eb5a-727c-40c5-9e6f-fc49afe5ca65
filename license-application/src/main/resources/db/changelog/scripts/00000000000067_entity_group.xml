<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="createTable_entityGroup" author="David">
        <createTable tableName="entity_group">
            <column autoIncrement="true" name="entity_group_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entity_group_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="entity_group_uuid" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="events" type="jsonb"/>
            <column name="name" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet id="createTable_audit_log_entity_group" author="">
        <createTable tableName="audit_log_entity_group">
            <column name="entity_group_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_group_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_group_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="entity_group_uuid" type="UUID"/>
            <column name="events" type="jsonb"/>
            <column name="name" type="TEXT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>