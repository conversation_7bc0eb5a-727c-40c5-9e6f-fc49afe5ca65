drop view if exists view_participant_address cascade;
CREATE OR REPLACE VIEW view_participant_address AS
SELECT
	pa.participant_id,
	addr.address_id,
	addr.address_uuid as entity_id,
	addr.city,
	addr.latitude,
	addr.longitude,
	addr.state,
	addr.street_address,
	addr.street_address_2,
	addr.zip,
	addr.house_number,
	addr.road,
	addr.town,
	addr.full_address as full_address,
	pat.name as address_type
from participant_address pa
LEFT JOIN address addr
	ON pa.address_id = addr.address_id
left join participant_address_type pat
on pat.participant_address_type_id = pa.participant_address_type_id;