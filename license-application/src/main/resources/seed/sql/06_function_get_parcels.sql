CREATE OR REPLACE FUNCTION get_parcels(
    IN searchParcelNumber varchar,
    IN searchLotNumber varchar,
    IN searchBlockNumber varchar,
    IN searchSubdivision varchar,
    IN searchAddress varchar,
    IN searchAddress2 varchar,
    IN searchCity varchar,
    IN searchState varchar,
    IN searchZip varchar
)
RETURNS TABLE (
    entity_id uuid
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT distinct
        addr.address_uuid as entity_id
    FROM address addr

	WHERE
	(
		(COALESCE(searchParcelNumber, '') = '' OR addr.properties->>'parcelNumber' ILIKE '%'|| searchParcelNumber || '%')
		and (COALESCE(searchLotNumber, '') = '' OR addr.properties->>'lotNumber' ILIKE '%'|| searchLotNumber || '%')
		and (COALESCE(searchBlockNumber, '') = '' OR addr.properties->>'blockNumber' ILIKE '%'|| searchBlockNumber || '%')
		and (COALESCE(searchSubdivision, '') = '' OR addr.properties->>'subDivision' ILIKE '%'|| searchSubdivision || '%')

		--address
		AND (COALESCE(searchAddress, '') = '' OR addr.street_address ILIKE '%'|| searchAddress || '%')
		AND (COALESCE(searchAddress2, '')  = '' OR addr.street_address_2 ILIKE '%'|| searchAddress2 || '%')
		AND (COALESCE(searchCity, '') = '' OR addr.city ILIKE  '%'|| searchCity || '%')
		AND (COALESCE(searchState, '') = '' OR addr.state ILIKE  '%'|| searchState || '%')
		AND (COALESCE(searchZip, '') = '' OR addr.zip ILIKE  '%'|| searchZip || '%')
	)
	and (
			  --atleast one must be not null or empty
			  COALESCE(searchParcelNumber, '') != ''
				OR COALESCE(searchLotNumber, '') != ''
				OR COALESCE(searchBlockNumber, '') != ''
				OR COALESCE(searchSubdivision, '') != ''
				OR COALESCE(searchAddress, '') != ''
				OR COALESCE(searchAddress2, '') !=''
				OR COALESCE(searchCity, '') !=''
				OR COALESCE(searchState, '') !=''
				OR COALESCE(searchZip, '') !=''
		  )

    limit 10;
END;
$$;

--drop function get_parcels

--SELECT * FROM get_parcels('',null,null,null,'250',null,'','','');