--drop view license.view_license_activity_fee;
create or replace view license.view_license_activity_fee as
SELECT
    vwfee.license_activity_id,
    vwfee.activity_type,
    SUM(vwfee.amount) AS total,
    SUM(CASE WHEN vwfee.is_altered THEN vwfee.amount ELSE 0 END) AS altered_amount,
    SUM(CASE WHEN vwfee.is_unaltered THEN vwfee.amount ELSE 0 END) AS unaltered_amount,
    SUM(CASE WHEN vwfee.is_senior THEN vwfee.amount ELSE 0 END) AS senior_amount,
    SUM(CASE WHEN vwfee.is_exempt THEN vwfee.amount ELSE 0 END) AS exempt_amount,
    SUM(CASE WHEN vwfee.is_state_exempt THEN vwfee.amount ELSE 0 END) AS state_exempt_amount,
    SUM(CASE WHEN vwfee.is_state_fee THEN vwfee.amount ELSE 0 END) AS state_fee_amount,
    SUM(CASE WHEN vwfee.is_local_fee THEN vwfee.amount ELSE 0 END) AS local_fee_amount,
    COUNT(vwfee.fee_code) AS fee_count,
	BOOL_OR(vwfee.is_altered) AS has_altered_fee,
    BOOL_OR(vwfee.is_unaltered) AS has_unaltered_fee,
    BOOL_OR(vwfee.is_senior) AS has_senior_fee,
    BOOL_OR(vwfee.is_exempt) AS has_exempt_fee,
    BOOL_OR(vwfee.is_state_exempt) AS has_state_exempt_fee,
    BOOL_OR(vwfee.is_state_fee) AS has_state_fee,
    BOOL_OR(vwfee.is_local_fee) AS has_local_fee
FROM license.view_license_activity_fee_detail vwfee
GROUP BY vwfee.license_activity_id, vwfee.activity_type;
