drop view if exists view_dog_licenses;
CREATE OR REPLACE VIEW view_dog_licenses AS
Select

    l.valid_from_date,
    dog.string_value

from license l

--join participant get dog
LEFT JOIN LATERAL(
    select
        pDog.participant_id,
        pDog.properties->>'dogSpayedOrNeutered' as string_value
    from association aDog

    inner JOIN view_participant pDog
        on pDog.participant_id = aDog.child_id
        and pDog.group_name = 'Dog'

        where aDog.parent_association_type = 'LICENSE'
        AND aDog.child_association_type = 'PARTICIPANT'
        and aDog.parent_id = l.license_id
) AS dog ON true