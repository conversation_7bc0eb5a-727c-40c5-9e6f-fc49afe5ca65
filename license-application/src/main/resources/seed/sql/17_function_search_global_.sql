drop function if exists fn_search_global(varchar);
CREATE OR REPLACE FUNCTION fn_search_global(
    IN fn_search_value varchar(255)
)
RETURNS TABLE (
    "avatarUUID" varchar(255),
    "entityType" varchar(255),
    "latitude" varchar(255),
    "primaryDisplay" varchar(255),
    "thirdDisplay.type" varchar(255),
    "thirdDisplay.value" varchar(255),
	"entityId" varchar(255),
    "secondaryDisplay" varchar(255),
    "longitude" varchar(255),
    "licenseForm" varchar(255),
    "status" varchar(255)
)
LANGUAGE plpgsql
AS $$
DECLARE
    fn_wild_card_value TEXT;
    fn_exact_value TEXT;

    fn_full_name_search_value TEXT;
    fn_first_name_search_value TEXT;
    fn_last_name_search_value TEXT;
    fn_contact_search_value TEXT;
    fn_entity_id_search_value TEXT;
    fn_tag_number_search_value TEXT;
    fn_dog_name_search_value TEXT;
    fn_license_number_search_value TEXT;
    fn_street_address_search_value TEXT;
    fn_street_address_2_search_value TEXT;
    fn_business_search_value TEXT;
BEGIN
    fn_search_value := remove_any_extra_spaces_and_trim(fn_search_value);
    fn_wild_card_value := '%' || fn_search_value || '%';
    fn_exact_value := fn_search_value;

    if fn_search_value LIKE '%*%' or fn_search_value LIKE '%\%%' ESCAPE '\' then
        --remove all asterisk with %
        fn_search_value := replace(fn_search_value, '*', '%');
        fn_wild_card_value := fn_search_value;
        fn_exact_value := fn_search_value;
    elsif fn_search_value LIKE '%"%' then
        --remove all double quotes with empty string
        fn_search_value := replace(fn_search_value, '"', '');
        fn_wild_card_value := fn_search_value;
        fn_exact_value := fn_search_value;
    end if;

    -- default exact match
    fn_entity_id_search_value := fn_exact_value;
    fn_license_number_search_value := fn_exact_value;
	fn_contact_search_value := fn_exact_value;
	fn_tag_number_search_value := fn_exact_value;
	fn_business_search_value := fn_exact_value;

    -- default wild card match
    fn_full_name_search_value := fn_wild_card_value;
    fn_first_name_search_value := fn_wild_card_value;
    fn_last_name_search_value := fn_wild_card_value;
    fn_dog_name_search_value := fn_wild_card_value;
    fn_street_address_search_value := fn_wild_card_value;
    fn_street_address_2_search_value := fn_wild_card_value;

    RETURN QUERY
        select distinct
            fn_get_avatar(p.entity_id)::varchar(255) as "avatarUUID",
            'individual'::varchar(255) as "entityType",
            ''::varchar(255) as "latitude",
            remove_any_extra_spaces_and_trim(
                coalesce(p.properties->>'firstName', '') || ' ' ||
                coalesce(p.properties->>'middleName', '') || ' ' ||
                coalesce(p.properties->>'lastName', '') || ' ' ||
                coalesce(p.properties->>'suffix', '')
            )::varchar(255) as "primaryDisplay",
            lower(contact."type")::varchar(255) as "thirdDisplay.type",
            contact."value"::varchar(255) as "thirdDisplay.value",
            p.entity_id::varchar(255) as "entityId",
            address."value"::varchar(1000) as "secondaryDisplay",
            ''::varchar(255) as "longitude",
            null::varchar(255) as "licenseForm",
            p.status_name::varchar(255) as "status"
        from view_participant p

        left join lateral (
            select
                c.contact_value as "value",
                c.group_name as "type"
            from view_contact c

            where c.participant_id = p.participant_id
			and coalesce(c.contact_value, '') <> ''
        ) as contact on true

        left join lateral (
            select
                pa.full_address as "value"
            from view_participant_address pa
            where pa.participant_id = p.participant_id
            and pa.address_type = 'Home'
            limit 1
        ) as address on true

        left join lateral(
            select 1 as "value"
            from participant_address pa
            LEFT JOIN address addr
            ON pa.address_id = addr.address_id
            where pa.participant_id = p.participant_id
            and addr.street_address ILIKE fn_street_address_search_value
        ) as search_address on true

        where p.group_name = 'Individual'
    	and (
    	        remove_any_extra_spaces_and_trim
                (
                    coalesce(p.properties->>'firstName', '') || ' ' || coalesce(p.properties->>'middleName', '') || ' ' || coalesce(p.properties->>'lastName', '')
                ) ILIKE fn_full_name_search_value
                or remove_any_extra_spaces_and_trim
                (
                    coalesce(p.properties->>'firstName', '') || ' ' || coalesce(p.properties->>'lastName', '')
                ) ILIKE fn_full_name_search_value
                or p.properties->>'firstName' ILIKE fn_first_name_search_value
                or p.properties->>'lastName' ILIKE fn_last_name_search_value
    			or contact."value" ILIKE fn_contact_search_value
    		    or p.entity_id::text ILIKE fn_entity_id_search_value
    			or coalesce(search_address."value", 0) = 1
    		)


    union all

        select
            fn_get_avatar(p.entity_id)::varchar(255) as "avatarUUID",
            'dog'::varchar(255) as "entityType",
            ''::varchar(255) as "latitude",
            remove_any_extra_spaces_and_trim(
                coalesce(p.properties->>'dogName', '') || ' (' || coalesce(p.properties->>'dogBreed', '') || ')'
            )::varchar(255) as "primaryDisplay",
            coalesce(lower(lic.license_status), '')::varchar(255) as "thirdDisplay.type",
            remove_any_extra_spaces_and_trim(
               coalesce(lic.license_status, '') || ' - ' || coalesce(lic.valid_from_date, '') || ' to ' || coalesce(lic.valid_to_date, '') || ' (' || coalesce(lic.duration, '') || ')'
            )::varchar(255) as "thirdDisplay.value",
            p.entity_id::varchar(255) as "entityId",
            remove_any_extra_spaces_and_trim(
                'Tag Number: ' || coalesce(p.properties->>'tagNumber', '') || ' | License #: ' || lic.license_number
            )::varchar(255) as "secondaryDisplay",
            ''::varchar(255) as "longitude",
            null::varchar(255) as "licenseForm",
            p.status_name::varchar(255) as "status"
        from view_participant p

        left join lateral (
            select
                l.license_number,
                TO_CHAR(l.valid_from_date, 'MM-DD-YYYY') as valid_from_date,
                TO_CHAR(l.valid_to_date, 'MM-DD-YYYY') as valid_to_date,
                calculate_date_difference(now()::timestamp, coalesce(l.valid_to_date, now())::timestamp) as duration,
                ls.name as license_status
            from association a

            LEFT JOIN license l
                ON a.child_id = l.license_id

            JOIN license_status ls
                    ON ls.license_status_id = l.license_status_id

            where  a.parent_association_type = 'PARTICIPANT'
            AND a.child_association_type = 'LICENSE'
            AND a.parent_id = p.participant_id
            limit 1
        ) as lic on true

        where p.group_name = 'Dog'
        and (
                p.entity_id::text ILIKE fn_entity_id_search_value
                or p.properties->>'tagNumber' ILIKE fn_tag_number_search_value
                or p.properties->>'dogName' ILIKE fn_dog_name_search_value
                or lic.license_number ILIKE fn_license_number_search_value
            )

    union all
        select
            ''::varchar(255) as "avatarUUID",
            'parcel'::varchar(255) as "entityType",
            addr.latitude::varchar(255) as "latitude",
            remove_any_extra_spaces_and_trim(
                coalesce(addr.street_address, '') || ', ' || coalesce(addr.street_address_2, '')
            )::varchar(255) as "primaryDisplay",
            null::varchar(255) as "thirdDisplay.type",
            null::varchar(255) as "thirdDisplay.value",
            addr.address_uuid::varchar(255) as "entityId",
            remove_any_extra_spaces_and_trim(
                coalesce(addr.city, '') || ', ' || coalesce(addr.state, '') || ' ' || coalesce(addr.zip, '')
            )::varchar(255) as "secondaryDisplay",
            addr.longitude::varchar(255) as "longitude",
            null::varchar(255) as "licenseForm",
            null::varchar(255) as "status"
        from address addr

        where
            (
                addr.address_uuid::text ILIKE fn_entity_id_search_value
                or addr.street_address ILIKE fn_street_address_search_value
                or addr.street_address_2 ILIKE fn_street_address_2_search_value
            )

    union all

    SELECT
        ''::varchar(255) as "avatarUUID",
        'license'::varchar(255) as "entityType",
        ''::varchar(255) as "latitude",
        remove_any_extra_spaces_and_trim(
            'License #: ' || coalesce(lic.license_number, '') || ' (' || coalesce(lt.name, '') || ')'
        )::varchar(255) as "primaryDisplay",
        coalesce(lower(ls.name), '')::varchar(255) as "thirdDisplay.type",
        remove_any_extra_spaces_and_trim(
           coalesce(ls.name, '') || ' - ' || coalesce(TO_CHAR(lic.valid_from_date, 'MM-DD-YYYY'), '') || ' to ' || coalesce(TO_CHAR(lic.valid_to_date, 'MM-DD-YYYY'), '') || ' (' || coalesce(calculate_date_difference(now()::timestamp, coalesce(lic.valid_to_date, now())::timestamp), '') || ')'
        )::varchar(255) as "thirdDisplay.value",
        lic.license_uuid::varchar(255) as "entityId",
        (
            'License Holder: ' || individual.full_name
        )::varchar(255) as "secondaryDisplay",
        ''::varchar(255) as "longitude",
        (
            '/document-service/download?documentUUID=' || coalesce(lic.properties->>'licenseForm', '')
        )::varchar(255) as "licenseForm",
        ls.name::varchar(255) as "status"
    from license lic

    JOIN license_status ls
        ON ls.license_status_id = lic.license_status_id
    JOIN license_type lt
        ON lt.license_type_id = lic.license_type_id

    --participant
    LEFT JOIN LATERAL(
        SELECT
            remove_any_extra_spaces_and_trim(
                coalesce(pIndividual.properties->>'firstName', '') || ' ' ||
                coalesce(pIndividual.properties->>'middleName', '') || ' ' ||
                coalesce(pIndividual.properties->>'lastName', '') || ' ' ||
                coalesce(pIndividual.properties->>'suffix', '')
            ) as full_name
        FROM association aIndividual

        LEFT JOIN view_participant pIndividual
            on pIndividual.participant_id = aIndividual.child_id

        WHERE aIndividual.parent_association_type = 'LICENSE'
        AND aIndividual.child_association_type = 'PARTICIPANT'
        AND aIndividual.parent_id = lic.license_id
        and pIndividual.group_name = 'Individual'
        LIMIT 1
    ) AS individual ON true

    where
		(
			lic.license_uuid::text ILIKE fn_entity_id_search_value
			or lic.license_number ILIKE fn_license_number_search_value
		)

    -- business
    union all
    select
        ''::varchar(255) as "avatarUUID",
        'business'::varchar(255) as "entityType",
        ''::varchar(255) as "latitude",
        remove_any_extra_spaces_and_trim(
            coalesce(b.properties->>'businessName', '') ||
            coalesce(b.properties->>'dba', '') ||
             ' (' || coalesce(b.properties->>'businessType', '') || ')'
        )::varchar(255) as "primaryDisplay",
        null::varchar(255) as "thirdDisplay.type",
        null::varchar(255) as "thirdDisplay.value",
        b.business_uuid::varchar(255) as "entityId",
        ''::varchar(255) as "secondaryDisplay",
        ''::varchar(255) as "longitude",
        null::varchar(255) as "licenseForm",
        null::varchar(255) as "status"
    from business b
    WHERE EXISTS (
        SELECT 1
        FROM jsonb_each_text(b.properties) AS kv(key, value)
        WHERE value ILIKE fn_business_search_value
    )

	limit 20;
END;
$$;

--SELECT * FROM fn_search_global('james smith');