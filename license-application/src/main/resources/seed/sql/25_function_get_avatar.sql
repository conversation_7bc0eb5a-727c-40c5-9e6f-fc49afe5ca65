drop function if exists fn_get_avatar;
CREATE OR REPLACE FUNCTION fn_get_avatar(
    IN fn_entity_id uuid
)
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    fn_avatar text;
BEGIN
    select
    	d.document_service_uuid
    into fn_avatar
    from view_participant p

    inner join association a
    on a.parent_association_type = 'PARTICIPANT'
    and a.parent_id = p.participant_id

    inner join "document" d
    on d.document_id = a.child_id
    and a.child_association_type = 'DOCUMENT'

    inner join document_type dt
    on dt.document_type_id = d.document_type_id
    and dt.key ilike 'avatar'

    where p.entity_id = fn_entity_id
    limit 1;

    RETURN fn_avatar;
END;
$$;

--select fn_get_avatar('5e48cce0-df9d-4098-9a01-992cba6d66b8')