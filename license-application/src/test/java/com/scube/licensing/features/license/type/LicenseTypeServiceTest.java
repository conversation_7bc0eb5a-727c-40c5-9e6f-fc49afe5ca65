package com.scube.licensing.features.license.type;

import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import com.scube.licensing.infrastructure.db.repository.license.LicenseTypeRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ActiveProfiles("test")
class LicenseTypeServiceTest {

    @Mock
    private LicenseTypeRepository licenseTypeRepository;

    @InjectMocks
    private LicenseTypeService licenseTypeService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
     void testCreateLicenseType() {
        LicenseTypeRequest request = new LicenseTypeRequest();
        request.setName("TestName");

        when(licenseTypeRepository.existsByCodeIgnoreCase(anyString())).thenReturn(false);
        when(licenseTypeRepository.save(any())).thenReturn(new LicenseType());

        LicenseTypeResponse response = licenseTypeService.createLicenseType(request);

        assertNotNull(response);
    }

    @Test
     void testGetAllLicenseTypes() {
        when(licenseTypeRepository.findAll()).thenReturn(List.of(new LicenseType()));

        GetAllLicenseTypeResponse response = licenseTypeService.getAllLicenseTypes();

        assertFalse(response.getItems().isEmpty());
    }

    @Test
     void testGetLicenseTypeById() {
        when(licenseTypeRepository.findById(anyLong())).thenReturn(Optional.of(new LicenseType()));

        LicenseTypeResponse response = licenseTypeService.getLicenseTypeById(1L);

        assertNotNull(response);
    }

    @Test
     void testUpdateLicenseType() {
        LicenseType existingType = new LicenseType();
        existingType.setCode("TestCode");

        LicenseTypeRequest request = new LicenseTypeRequest();
        request.setName("UpdatedName");

        when(licenseTypeRepository.findById(anyLong())).thenReturn(Optional.of(existingType));
        when(licenseTypeRepository.existsByCodeIgnoreCase(anyString())).thenReturn(false);
        when(licenseTypeRepository.save(any())).thenReturn(new LicenseType());

        LicenseTypeResponse response = licenseTypeService.updateLicenseType(1L, request);

        assertNotNull(response);
    }

    @Test
     void testDeleteLicenseType() {
        when(licenseTypeRepository.existsById(anyLong())).thenReturn(true);

        Assertions.assertDoesNotThrow(() -> licenseTypeService.deleteLicenseType(1L));
    }
}
