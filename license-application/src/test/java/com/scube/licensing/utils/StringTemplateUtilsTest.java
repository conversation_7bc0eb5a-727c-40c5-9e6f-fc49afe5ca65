package com.scube.licensing.utils;

import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ActiveProfiles("test")
class StringTemplateUtilsTest {

    @Test
    void buildStringFromTemplate_first_last_only() {
        String template = "{title} {first} {middle} {last} {suffix}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("first", "John");
        fieldValues.put("last", "Smith");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("John Smith", finalString);
    }

    @Test
    void buildStringFromTemplate_first_middle_last_only() {
        String template = "{title} {first} {middle} {last} {suffix}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("first", "<PERSON>");
        fieldValues.put("last", "Smith");
        fieldValues.put("middle", "Sm");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("John Sm Smith", finalString);
    }

    @Test
    void buildStringFromTemplate_title_first_middle_last_only() {
        String template = "{title} {first} {middle} {last} {suffix}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("title", "Mr.");
        fieldValues.put("first", "John");
        fieldValues.put("last", "Smith");
        fieldValues.put("middle", "Sm");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("Mr. John Sm Smith", finalString);
    }

    @Test
    void buildStringFromTemplate_all() {
        String template = "{title} {first} {middle} {last} {suffix}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("title", "Mr.");
        fieldValues.put("first", "John");
        fieldValues.put("last", "Smith");
        fieldValues.put("middle", "Sm");
        fieldValues.put("suffix", "III");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("Mr. John Sm Smith III", finalString);
    }

    @Test
    void buildStringFromTemplate_removeExtraComma() {
        String template = "{title}, {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("title", "Mr.");
        fieldValues.put("first", "");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("Mr.", finalString);
    }

    @Test
    void buildStringFromTemplate_dontRemoveComma() {
        String template = "{title}, {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("title", "Mr.");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("Mr., John", finalString);
    }


    @Test
    void buildStringFromTemplate_Upper() {
        String template = "{title|upperCase} {first} {middle} {last} {suffix}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("title", "Mr.");
        fieldValues.put("first", "John");
        fieldValues.put("last", "Smith");
        fieldValues.put("middle", "Sm");
        fieldValues.put("suffix", "III");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("MR. John Sm Smith III", finalString);
    }

    @Test
    void buildStringFromTemplate_lower() {
        String template = "{title|lowerCase} {first} {middle} {last|lowerCase} {suffix}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("title", "Mr.");
        fieldValues.put("first", "John");
        fieldValues.put("last", "SmiTh");
        fieldValues.put("middle", "Sm");
        fieldValues.put("suffix", "III");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("mr. John Sm smith III", finalString);
    }

    @Test
    void buildStringFromTemplate_int1() {
        String template = "{value|int} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", "56.345");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("56 John", finalString);
    }

    @Test
    void buildStringFromTemplate_int2() {
        String template = "{value|int} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", "56");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("56 John", finalString);
    }

    @Test
    void buildStringFromTemplate_intNull() {
        String template = "{value|int} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", null);
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("John", finalString);
    }

    @Test
    void buildStringFromTemplate_date1() {
        String template = "{value|date:MM/dd/yyyy} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", "2020-01-01 00:00:00.000");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("01/01/2020 John", finalString);
    }

    @Test
    void buildStringFromTemplate_date2() {
        String template = "{value|date:MM/dd/yyyy hh:mm:ss} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", "2020-01-01 02:10:06.000");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("01/01/2020 02:10:06 John", finalString);
    }

    @Test
    void buildStringFromTemplate_date3() {
        String template = "{value|date:MM/dd/yyyy hh:mm a} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", "2023-07-06T21:45:52.613088");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("07/06/2023 09:45 PM John", finalString);
    }

    @Test
    void buildStringFromTemplate_currency1() {
        String template = "{value|currency} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", "56");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("$56.00 John", finalString);
    }

    @Test
    void buildStringFromTemplate_currency2() {
        String template = "{value|currency} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("value", "56.984");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("$56.98 John", finalString);
    }

    @Test
    void buildStringFromTemplate_duration1() {
        String template = "{firstDate,lastDate|duration} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("firstDate", "2020-06-01 02:10:06.000");
        fieldValues.put("lastDate", "2021-06-01");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("1 year John", finalString);
    }

    @Test
    void buildStringFromTemplate_duration2() {
        String template = "{firstDate,lastDate|duration} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("firstDate", "2020-01-01");
        fieldValues.put("lastDate", "2021-01-04");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("1 year and 3 days John", finalString);
    }

    @Test
    void buildStringFromTemplate_duration3() {
        String template = "{firstDate,lastDate|duration} {first}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("firstDate", "2020-01-01 02:10");
        fieldValues.put("lastDate", "2022-01-07 04:10");
        fieldValues.put("first", "John");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("2 years and 6 days John", finalString);
    }

    @Test
    void buildStringFromTemplate_capitalize() {
        String template = "{title|capitalize} {first} {middle} {last|lowerCase} {suffix}";
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("title", "mr. tim");
        fieldValues.put("first", "John");
        fieldValues.put("last", "SmiTh");
        fieldValues.put("middle", "Sm");
        fieldValues.put("suffix", "III");

        String finalString = StringTemplateUtils.buildStringFromTemplate(template, fieldValues);
        assertEquals("Mr. tim John Sm smith III", finalString);
    }
}