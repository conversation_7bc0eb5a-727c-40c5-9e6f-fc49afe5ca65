package com.scube.notification.db.entity;

import com.scube.notification.db.entity.base.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.Audited;

@Entity
@Table(name = AppProperty.TABLE_NAME)
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Audited
public class AppProperty extends BaseEntity {
    public static final String TABLE_NAME = "app_properties";
    public static final String ID = "app_properties_id";

    @Size(max = 100)
    @Column(unique = true, updatable = false, nullable = false)
    private String name;

    @Size(max = 250)
    @Column(nullable = false)
    private String fieldValue;

    @Size(max = 250)
    @Column
    private String description;
}
