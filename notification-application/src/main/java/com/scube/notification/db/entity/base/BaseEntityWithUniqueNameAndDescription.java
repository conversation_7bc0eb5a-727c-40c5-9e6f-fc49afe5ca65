package com.scube.notification.db.entity.base;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@MappedSuperclass
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseEntityWithUniqueNameAndDescription extends BaseEntity {
    @Column(unique = true, length = 50)
    private String name;

    @Column(length = 250)
    private String description;

    @PrePersist
    protected void onBaseEntityWithUniqueNameAndDescription() {
        if (description == null)
            description = "";
    }
}