package com.scube.notification.features.notification.rabbit.listener;

import com.scube.notification.features.notification.rabbit.events.ErrorEmailEvent;
import com.scube.notification.features.processing.email.service.SMTPErrorEmailService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
public class ErrorEmailEventListener extends FanoutListener<ErrorEmailEvent> {
    private final SMTPErrorEmailService smtpErrorEmailService;

    @Override
    public void consume(ErrorEmailEvent event) {
        log.debug("EmailEmailEventListener.consume()");

        smtpErrorEmailService.send(event.getErrorEmailRequest());
    }
}
