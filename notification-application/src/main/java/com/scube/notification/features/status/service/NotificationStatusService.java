package com.scube.notification.features.status.service;

import com.scube.notification.db.entity.NotificationStatus;
import com.scube.notification.db.repository.NotificationStatusRepository;
import com.scube.notification.exception.NotFoundException;
import com.scube.notification.features.notification.validation.service.ValidationService;
import com.scube.notification.features.status.dto.NotificationStatusCreateRequest;
import com.scube.notification.features.status.dto.NotificationStatusUpdateRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@AllArgsConstructor
@Service
@Slf4j
public class NotificationStatusService {
    private final NotificationStatusRepository notificationStatusRepository;
    private final ValidationService validationService;

    public List<NotificationStatus> findAll() {
        log.debug("NotificationStatusService.findAll()");
        return notificationStatusRepository.findAll();
    }

    public NotificationStatus findByUuid(UUID uuid) {
        log.debug("NotificationStatusService.findByUuid()");
        return notificationStatusRepository.findByUuid(uuid).orElseThrow(() -> new NotFoundException("NotificationStatus not found"));
    }

    public NotificationStatus findByName(String name) {
        log.debug("NotificationStatusService.findByName()");
        return notificationStatusRepository.findByName(name).orElseThrow(() -> new NotFoundException("NotificationStatus not found"));
    }

    public NotificationStatus create(NotificationStatusCreateRequest request) {
        log.debug("NotificationStatusService.create()");

        validationService.isUniqueName(notificationStatusRepository, "NotificationStatus", request.getName());

        NotificationStatus status = NotificationStatus.builder()
                .name(request.getName())
                .description(request.getDescription())
                .build();
        return notificationStatusRepository.save(status);
    }

    public NotificationStatus update(NotificationStatusUpdateRequest request) {
        log.debug("NotificationStatusService.update()");

        NotificationStatus status = findByUuid(request.getUuid());

        return notificationStatusRepository.save(setUpdatedFields(request, status));
    }

    public NotificationStatus setUpdatedFields(NotificationStatusUpdateRequest request, NotificationStatus status) {
        NotificationStatus updatedNotificationStatus = status;

        if (isNotBlank(request.getName()) && !request.getName().equals(status.getName())) {
            validationService.isUniqueName(notificationStatusRepository, "NotificationStatus", request.getName());
            updatedNotificationStatus.setName(request.getName());
        }

        if (isNotBlank(request.getDescription())) {
            updatedNotificationStatus.setDescription(request.getDescription());
        }

        return updatedNotificationStatus;
    }

    public void deleteByUuid(UUID uuid) {
        log.debug("NotificationStatusService.deleteByUuid()");
        notificationStatusRepository.delete(findByUuid(uuid));
    }
}

