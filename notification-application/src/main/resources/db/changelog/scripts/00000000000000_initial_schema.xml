<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="james (generated)" id="1690815186088-1">
        <createTable tableName="app_properties">
            <column autoIncrement="true" name="app_properties_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="app_properties_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(250)"/>
            <column name="field_value" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-2">
        <createTable tableName="notification">
            <column autoIncrement="true" name="notification_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="notification_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="execution_ts" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="json" type="JSONB"/>
            <column name="message" type="VARCHAR(255)"/>
            <column name="notification_type" type="VARCHAR(255)"/>
            <column name="subject" type="VARCHAR(255)"/>
            <column name="notification_status_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="notification_topic_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-3">
        <createTable tableName="notification_status">
            <column autoIncrement="true" name="notification_status_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="notification_status_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(250)"/>
            <column name="name" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-4">
        <createTable tableName="notification_template">
            <column autoIncrement="true" name="notification_template_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="notification_template_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="notification_type" type="VARCHAR(255)"/>
            <column name="template_body" type="VARCHAR(255)"/>
            <column name="notification_topic_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="VARCHAR(255)"/>
            <column name="subject" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-5">
        <createTable tableName="notification_topic">
            <column autoIncrement="true" name="notification_topic_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="notification_topic_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(250)"/>
            <column name="name" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-6">
        <addUniqueConstraint columnNames="uuid" constraintName="uk_6eyv23d1i1fmlc4fo02ghhcxt"
                             tableName="notification_status"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-7">
        <addUniqueConstraint columnNames="name" constraintName="uk_6tqfg5m6htt6d73eph6ao86us"
                             tableName="notification_status"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-8">
        <addUniqueConstraint columnNames="uuid" constraintName="uk_9tysyaup1vkl7cjos8q1qyvxe"
                             tableName="notification_topic"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-9">
        <addUniqueConstraint columnNames="name" constraintName="uk_crbdhq8ruhgfgi1cq0mufvm7v"
                             tableName="app_properties"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-10">
        <addUniqueConstraint columnNames="uuid" constraintName="uk_d1tw9n1d2290xhfxv5j9xkkj5" tableName="notification"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-11">
        <addUniqueConstraint columnNames="name" constraintName="uk_ewmx2o4ra4k8vjjaucicyxgs2"
                             tableName="notification_topic"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-12">
        <addUniqueConstraint columnNames="uuid" constraintName="uk_hbw6qdaa2cjxfkn97jvmqvhdj"
                             tableName="app_properties"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-13">
        <addUniqueConstraint columnNames="uuid" constraintName="uk_rculkup1p96xs5f1tc3sfigqq"
                             tableName="notification_template"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-14">
        <addForeignKeyConstraint baseColumnNames="notification_topic_id" baseTableName="notification"
                                 constraintName="fk3o1cvfapuoak6ssa8n3nmwb93" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="notification_topic_id" referencedTableName="notification_topic"
                                 validate="true"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-15">
        <addForeignKeyConstraint baseColumnNames="notification_topic_id" baseTableName="notification_template"
                                 constraintName="fkaq01ymxywpb1p8wtx7k62xx98" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="notification_topic_id" referencedTableName="notification_topic"
                                 validate="true"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690815186088-16">
        <addForeignKeyConstraint baseColumnNames="notification_status_id" baseTableName="notification"
                                 constraintName="fkolymmyib532hnnrj5iv0vfrd2" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="notification_status_id"
                                 referencedTableName="notification_status" validate="true"/>
    </changeSet>
</databaseChangeLog>
