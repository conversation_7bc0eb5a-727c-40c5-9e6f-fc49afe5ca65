<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="updateDefaultAuditRecord3" author="david">
        <sqlFile path="update_table_with_createdBy_and_modified_by.sql"
                 relativeToChangelogFile="true" splitStatements="false"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-19">
        <createTable tableName="audit_log_notification">
            <column name="notification_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_notification_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_notification_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="correlation_id" type="VARCHAR(255)"/>
            <column name="execution_ts" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="notification" type="JSONB"/>
            <column name="notification_type" type="VARCHAR(255)"/>
            <column name="tag" type="VARCHAR(255)"/>
            <column name="topic" type="VARCHAR(255)"/>
            <column name="notification_status_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-23">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="audit_log_revision_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-24">
        <createTable tableName="audit_log_app_properties">
            <column name="app_properties_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_app_properties_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_app_properties_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="description" type="VARCHAR(250)"/>
            <column name="field_value" type="VARCHAR(250)"/>
            <column name="name" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-25">
        <createTable tableName="audit_log_notification_status">
            <column name="notification_status_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_notification_status_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_notification_status_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-26">
        <createTable tableName="audit_log_revision">
            <column name="audit_log_revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_revision_pkey"/>
            </column>
            <column name="timestamp" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="auditor" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-27">
        <addColumn tableName="app_properties">
            <column name="conversion_reference" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-28">
        <addColumn tableName="notification">
            <column name="conversion_reference" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-29">
        <addColumn tableName="notification_status">
            <column name="conversion_reference" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-30">
        <renameColumn tableName="app_properties" oldColumnName="uuid" newColumnName="app_properties_uuid"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-31">
        <renameColumn tableName="notification_status" oldColumnName="uuid" newColumnName="notification_status_uuid"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-32">
        <renameColumn tableName="notification" oldColumnName="uuid" newColumnName="notification_uuid"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-20">
        <addUniqueConstraint columnNames="app_properties_uuid" constraintName="uk_ejhsvs246fqrv8h21wf6apkcl"
                             tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-21">
        <addUniqueConstraint columnNames="notification_uuid" constraintName="uk_su0xhqdv2enlmiqbsq07fygc3"
                             tableName="notification"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-22">
        <addUniqueConstraint columnNames="notification_status_uuid" constraintName="uk_93p5ei8s9lxgxiwra4phj0epv"
                             tableName="notification_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-33">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_notification_status"
                                 constraintName="fk30h50vhyc38ytri2tt2ef1n12" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-34">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_notification"
                                 constraintName="fkg4xwamofr46tqbnfyuncvfvec" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-35">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_app_properties"
                                 constraintName="fkihewlw1dltxq4wtidjh2gd9fw" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-36">
        <dropUniqueConstraint constraintName="uk_6eyv23d1i1fmlc4fo02ghhcxt" tableName="notification_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-37">
        <dropUniqueConstraint constraintName="uk_d1tw9n1d2290xhfxv5j9xkkj5" tableName="notification"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-38">
        <dropUniqueConstraint constraintName="uk_hbw6qdaa2cjxfkn97jvmqvhdj" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-1">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-2">
        <addNotNullConstraint columnDataType="varchar(1000)" columnName="created_by" tableName="app_properties"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-3">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="notification"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-5">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="notification_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-6">
        <addNotNullConstraint columnDataType="varchar(1000)" columnName="created_by" tableName="notification_status"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-4">
        <addNotNullConstraint columnDataType="varchar(1000)" columnName="created_by" tableName="notification"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-7">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-8">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="notification"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-9">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="notification_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-10">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-11">
        <addNotNullConstraint columnDataType="varchar(1000)" columnName="last_modified_by" tableName="app_properties"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-12">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="notification"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-13">
        <addNotNullConstraint columnDataType="varchar(1000)" columnName="last_modified_by" tableName="notification"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-14">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="notification_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-15">
        <addNotNullConstraint columnDataType="varchar(1000)" columnName="last_modified_by"
                              tableName="notification_status" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-16">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-17">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="notification"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695347071505-18">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="notification_status"/>
    </changeSet>
</databaseChangeLog>