package com.scube.notification.features.scheduling;

import com.scube.notification.db.entity.Notification;
import com.scube.notification.db.entity.NotificationStatus;
import com.scube.notification.features.notification.service.NotificationService;
import com.scube.notification.features.processing.base.factory.NotificationProcessingFactoryService;
import com.scube.notification.features.processing.base.service.NotificationProcessingService;
import com.scube.notification.features.status.service.NotificationStatusService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.UUID;

import static com.scube.notification.features.status.constants.StatusConstants.COMPLETE;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class NotificationSchedulingServiceUnitTest {
    @InjectMocks
    @Spy
    NotificationProcessingService notificationSchedulingService;

    @Mock
    NotificationStatusService notificationStatusService;

    @Mock
    NotificationService notificationService;

    @Mock
    NotificationProcessingFactoryService notificationProcessingFactoryService;


    @Test
    void processAllPendingNotifications() {
        List<Notification> pendingNotifications = List.of(new Notification(), new Notification());

        when(notificationService.findPendingNotificationsReadyToSend()).thenReturn(pendingNotifications);

        notificationSchedulingService.processAllPendingNotifications();

        verify(notificationSchedulingService, times(2)).processNotification(any(Notification.class));
    }

    @Test
    void processNotification_uuid() {
        UUID uuid = UUID.randomUUID();

        Notification expectedNotification = new Notification();
        expectedNotification.setNotificationStatus(NotificationStatus.builder()
                        .name("PENDING")
                .build());

        when(notificationService.findByUuid(uuid)).thenReturn(expectedNotification);

        notificationSchedulingService.processNotification(uuid);

        verify(notificationSchedulingService, times(1)).processNotification(expectedNotification);
    }

    @Test
    void processNotification_notification() {
        Notification notification = new Notification();

        notificationSchedulingService.processNotification(notification);

        verify(notificationStatusService, times(1)).findByName(COMPLETE);
        verify(notificationService, times(1)).save(any(Notification.class));
    }
}
