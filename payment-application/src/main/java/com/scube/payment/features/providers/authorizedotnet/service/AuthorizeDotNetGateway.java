package com.scube.payment.features.providers.authorizedotnet.service;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.config_utils.app_property.AppPropertyValue;
import com.scube.config_utils.json_storage.JsonStorageValue;
import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.enums.RefundStatus;
import com.scube.payment.features.payment.enums.RefundStatusCodeAuthorizeNet;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentResponseDto;
import com.scube.payment.features.payment.processing.rabbit.RefundedEvent;
import com.scube.payment.features.payment.processing.service.PaymentProcessingService;
import com.scube.payment.features.payment.receipts.service.ReceiptService;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.payment.features.providers.authorizedotnet.config.AuthorizeDotNetPaymentProviderSettings;
import com.scube.payment.features.providers.authorizedotnet.config.AuthorizeDotNetProperties;
import com.scube.payment.features.providers.authorizedotnet.dto.config.IAuthorizeDotNetApiResponse;
import com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment.GetHostedPaymentPageTokenRequestDto;
import com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment.GetHostedPaymentPageTokenResponseDto;
import com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment.HostedPaymentSettings;
import com.scube.payment.features.providers.authorizedotnet.dto.mapper.AuthorizeDotNetMapper;
import com.scube.payment.features.providers.authorizedotnet.dto.shared.Messages;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.*;
import com.scube.payment.features.providers.authorizedotnet.dto.webhook.AuthorizeDotNetWebhookEventDto;
import com.scube.payment.features.providers.authorizedotnet.exchange.IAuthorizeDotNetXmlExchange;
import com.scube.payment.features.providers.authorizedotnet.model.AuthDotNetMapping;
import com.scube.payment.features.providers.authorizedotnet.repo.AuthDotNetMappingRepository;
import com.scube.payment.features.providers.gateway.IPaymentProviderGateway;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.PaymentTokenResponse;
import com.scube.payment.features.webhook.inbox.domain_events.PaymentDetailsEvent;
import com.scube.rabbit.core.AmqpGateway;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.scube.payment.features.providers.authorizedotnet.config.AuthorizeDotNetProperties.PAYMENT_PROVIDER_NAME;
import static com.scube.payment.features.providers.gateway.PaymentProviderGateway.GATEWAY;

@Service(AuthorizeDotNetGateway.NAME)
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class AuthorizeDotNetGateway implements IPaymentProviderGateway<
        PaymentTokenRequest,
        PaymentTokenResponse,
        AuthorizeDotNetWebhookEventDto
        > {
    public static final String NAME = PAYMENT_PROVIDER_NAME + GATEWAY;
    private final IAuthorizeDotNetXmlExchange authorizeDotNetExchange;
    private final PaymentProcessingService paymentProcessingService;
    private final AuthorizeDotNetMapper mapper;
    private final AuthDotNetMappingRepository authDotNetMappingRepository;
    private final CalculationServiceConnection calculationServiceConnection;
    private final ReceiptService receiptService;
    private final PaymentStorageService paymentStorageService;
    private final AmqpGateway amqpGateway;

    @Setter
    @AppPropertyValue
    private AuthorizeDotNetProperties authorizeDotNetProperties;

    @Setter
    @JsonStorageValue("config=authorizeDotNetPaymentProviderSettings")
    private AuthorizeDotNetPaymentProviderSettings authorizeDotNetPaymentProviderSettings;

    public GetTransactionDetailsResponseDto getTransactionDetails(String transactionId) {
        log.debug("Getting transaction details for transactionId: " + transactionId);

        GetTransactionDetailsRequestDto getTransactionDetailsRequestDto =
                new GetTransactionDetailsRequestDto(authorizeDotNetProperties.merchantAuthentication(), transactionId);

        GetTransactionDetailsResponseDto response =
                authorizeDotNetExchange.getTransactionDetails(getTransactionDetailsRequestDto);

        validate(response);

        return response;
    }

    public void validate(IAuthorizeDotNetApiResponse response) {
        log.info("Authorize.Net Response: " + response);

        if (response != null) {
            Messages messages = response.getMessages();
            if (!messages.resultCode().equals("Ok")) {
                String errorMessage = buildDetailedErrorMessage(messages);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, errorMessage);
            }
        } else {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to get transaction details:  response is null");
        }
    }

    private String buildDetailedErrorMessage(Messages messages) {
        StringBuilder errorMessage = new StringBuilder();
        errorMessage.append("Failed to get transaction details: ").append(messages.resultCode());

        if (messages.message() != null && !messages.message().isEmpty()) {
            errorMessage.append(" - Details: ");
            for (int i = 0; i < messages.message().size(); i++) {
                var message = messages.message().get(i);
                if (i > 0) {
                    errorMessage.append("; ");
                }
                errorMessage.append("Code: ").append(message.code())
                        .append(", Text: ").append(message.text());
            }
        }

        return errorMessage.toString();
    }

    @Override
    @Transactional
    public PaymentTokenResponse getToken(PaymentTokenRequest paymentTokenRequest) {
        log.debug("Getting payment token for order: " + paymentTokenRequest.getOrderId());

        TransactionRequest transactionRequest = new TransactionRequest(paymentTokenRequest);
        String refId = getRefId(paymentTokenRequest.getOrderId());
        HostedPaymentSettings hostedPaymentSettings = getHostedPaymentSettings();

        addQueryParamsToReturnUrl(paymentTokenRequest, hostedPaymentSettings);

        log.debug(String.valueOf(hostedPaymentSettings));

        GetHostedPaymentPageTokenRequestDto request = new GetHostedPaymentPageTokenRequestDto(
                new GetHostedPaymentPageTokenRequestDto.GetHostedPaymentPageRequest(
                        authorizeDotNetProperties.merchantAuthentication(), refId, transactionRequest, hostedPaymentSettings)
        );

        GetHostedPaymentPageTokenResponseDto response = authorizeDotNetExchange.getHostedPaymentPageToken(request);

        validate(response);

        return new PaymentTokenResponse(response.getToken(), refId, PAYMENT_PROVIDER_NAME, Map.of());
    }

    private static void addQueryParamsToReturnUrl(PaymentTokenRequest paymentTokenRequest, HostedPaymentSettings hostedPaymentSettings) {
        Optional<HostedPaymentSettings.Setting> urlSettingOptional = hostedPaymentSettings.getSetting().stream()
                .filter(setting -> "hostedPaymentReturnOptions".equals(setting.getSettingName()))
                .findFirst();

        if (urlSettingOptional.isPresent()) {
            HostedPaymentSettings.Setting urlSetting = urlSettingOptional.get();
            String hostedPaymentReturnOptions = urlSetting.getSettingValue();

            hostedPaymentReturnOptions = hostedPaymentReturnOptions.replace("{orderId}", paymentTokenRequest.getOrderId().toString());
            urlSetting.setSettingValue(hostedPaymentReturnOptions);
        } else {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Return URL not found in settings");
        }

    }

    public HostedPaymentSettings getHostedPaymentSettings() {
        Map<String, Object> settingsMap = authorizeDotNetPaymentProviderSettings.getProperties();

        List<HostedPaymentSettings.Setting> settings = settingsMap.entrySet().stream()
                .map(entry -> new HostedPaymentSettings.Setting(entry.getKey(), entry.getValue().toString()))
                .toList();

        return new HostedPaymentSettings(settings);
    }

    @Transactional
    public String getRefId(UUID orderId) {
        String refId = authDotNetMappingRepository.getNewRefId();
        AuthDotNetMapping authDotNetMapping = new AuthDotNetMapping(refId, orderId);
        authDotNetMappingRepository.save(authDotNetMapping);
        return refId;
    }

    @Override
    public void authCapture(AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto) {
        log.debug("Processing authCapture webhook: {}", authorizeDotNetWebhookEventDto);

        String transactionId = String.valueOf(authorizeDotNetWebhookEventDto.getPayload().getId());

        // If there is a transactionId, it means one of the other payment service replicas
        // has already processed this authcapture.
        if (authDotNetMappingRepository.existsByTransactionId(transactionId)) {
            return;
        }

        GetTransactionDetailsResponseDto transactionDetails =
                getTransactionDetails(transactionId);

        String refId = transactionDetails.getTransrefId();
        GetTransactionDetailsResponseDto.Transaction transaction = transactionDetails.getTransaction();

        AuthDotNetMapping authDotNetMapping = authDotNetMappingRepository.findByRefIdOrThrow(refId);

        // If there is a paymentId, it means one of the other payment service replicas
        // has already processed this authcapture.
        if (authDotNetMapping.getPaymentId() != null) {
            return;
        }

        OrderInvoiceResponse orderInvoiceResponse = calculationServiceConnection.order().getOrder(authDotNetMapping.getOrderId());

        SubmitPaymentRequestDto request = SubmitPaymentRequestDto
                .builder()
                .orderId(authDotNetMapping.getOrderId())
                .orderAmount(orderInvoiceResponse.getTotal())
                .payee(mapper.toPayee(transaction))
                .paymentType(getPaymentType(transaction.payment()))
                .paymentAmount(transaction.authAmount())
                .paymentReference(transactionId)
                .status(PaymentStatus.COMPLETED)
                .transactionDate(transaction.submitTimeUTC())
                .authorizedTs(authorizeDotNetWebhookEventDto.getEventDate())
                .capturedTs(authorizeDotNetWebhookEventDto.getEventDate())
                .paymentProvider(PAYMENT_PROVIDER_NAME)
                .isOnlineTransaction(true)
                .build();

        SubmitPaymentResponseDto paymentResp = paymentProcessingService.submitPayment(request);

        authDotNetMapping.setPaymentId(paymentResp.getPaymentId());
        authDotNetMapping.setTransactionId(transactionDetails.getTransaction().transId());
        authDotNetMappingRepository.save(authDotNetMapping);

        receiptService.generateReceipt(request, paymentResp.getPaymentId(), orderInvoiceResponse);
    }

    @Override
    public void settle(AuthorizeDotNetWebhookEventDto request) {
        paymentStorageService.settle(getPaymentId(request));
    }

    @Override
    public void createRefund(RefundTransaction refundTransaction) {
        String refId = getRefId(refundTransaction.getOrderId());
        String originalTxnId = refundTransaction.getPayment().getPaymentReference();

        log.debug("Fetching transaction details for refund: {}", originalTxnId);

        try {
            GetTransactionDetailsResponseDto.Transaction transaction = getOriginalTransaction(originalTxnId);

            boolean isSettled = isTransactionSettled(transaction);

            if (!isSettled) {
                log.info("Transaction {} is not settled. Voiding instead of refunding.", originalTxnId);
                voidTransaction(refundTransaction, transaction, originalTxnId, refId);
                return;
            }

            CreateTransactionRequestDto request = buildRefundRequest(refundTransaction, transaction, originalTxnId, refId);
            CreateTransactionResponseDto response = authorizeDotNetExchange.createTransaction(request);

            validate(response);

            var txnResponse = response.getTransactionResponse();
            if (txnResponse == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Refund failed: No transaction response returned");
            }

            if (!RefundStatusCodeAuthorizeNet.APPROVED.toString().equals(txnResponse.responseCode())) {
                String errorMessage = "Refund failed with response code: " + txnResponse.responseCode();
                if (txnResponse.messages() != null && !txnResponse.messages().isEmpty()) {
                    errorMessage += " - " + txnResponse.messages().getFirst().text();
                }
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, errorMessage);
            }

            if (txnResponse.transId() == null) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Refund failed: No transaction ID returned");
            }

            String refundTxnId = txnResponse.transId();
            log.info("Refund successful. Refund Txn ID: {}", refundTxnId);

            paymentStorageService.updateRefund(refundTransaction, refundTxnId);

        } catch (Exception e) {
            log.error("Error processing refund for transaction {}: {}", originalTxnId, e.getMessage(), e);
            paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.REJECTED);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to process refund: " + e.getMessage(), e);
        }
    }

    private boolean isTransactionSettled(GetTransactionDetailsResponseDto.Transaction transaction) {
        String transactionStatus = transaction.transactionStatus();
        return "settledSuccessfully".equals(transactionStatus);
    }

    private void voidTransaction(RefundTransaction refundTransaction,
                                 GetTransactionDetailsResponseDto.Transaction transaction,
                                 String originalTxnId,
                                 String refId) {
        try {
            CreateTransactionRequestDto voidRequest = buildVoidRequest(originalTxnId, refId);
            CreateTransactionResponseDto response = authorizeDotNetExchange.createTransaction(voidRequest);

            validate(response);

            var txnResponse = response.getTransactionResponse();
            if (txnResponse == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Void failed: No transaction response returned");
            }

            if (!RefundStatusCodeAuthorizeNet.APPROVED.toString().equals(txnResponse.responseCode())) {
                String errorMessage = "Void failed with response code: " + txnResponse.responseCode();
                if (txnResponse.messages() != null && !txnResponse.messages().isEmpty()) {
                    errorMessage += " - " + txnResponse.messages().getFirst().text();
                }
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, errorMessage);
            }

            if (txnResponse.transId() == null) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Void failed: No transaction ID returned");
            }

            String voidTxnId = txnResponse.transId();
            log.info("Transaction voided successfully. Void Txn ID: {}", voidTxnId);

            paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.PAYMENT_VOIDED);
        } catch (Exception e) {
            log.error("Error voiding transaction {}: {}", originalTxnId, e.getMessage(), e);
            paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.REJECTED);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to void transaction: " + e.getMessage(), e);
        }
    }

    private CreateTransactionRequestDto buildVoidRequest(String originalTxnId, String refId) {
        var voidTxnRequest = new CreateTransactionRequestDto.VoidTransactionRequest(
                "voidTransaction",
                originalTxnId
        );

        return new CreateTransactionRequestDto(
                authorizeDotNetProperties.merchantAuthentication(),
                refId,
                voidTxnRequest
        );
    }

    @Override
    public void refund(AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto) {
        String refundTransactionId = String.valueOf(authorizeDotNetWebhookEventDto.getPayload().getId());
        RefundTransaction refundTransaction = paymentStorageService.getByRefundReference(refundTransactionId);
        paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.COMPLETED);

        OrderInvoiceResponse order = calculationServiceConnection.order().getRefundableOrder(refundTransaction.getOrderId());
        BigDecimal refundedAmount = paymentStorageService.getRefundedAmount(refundTransaction.getOrderId());

        amqpGateway.publish(new RefundedEvent(order.getOrderId(), refundedAmount));
    }

    @Override
    public void refundFailed(AuthorizeDotNetWebhookEventDto request) {
        throw new UnsupportedOperationException("No webhook event for refund failed in Authorize.NET");
    }

    @Override
    public void _void(AuthorizeDotNetWebhookEventDto request) {
        paymentStorageService._void(getPaymentId(request));
    }

    private @NotNull UUID getPaymentId(AuthorizeDotNetWebhookEventDto request) {
        String transactionId = String.valueOf(request.getPayload().getId());

        AuthDotNetMapping authDotNetMapping =
                authDotNetMappingRepository.findByTransactionIdOrThrow(transactionId);

        return authDotNetMapping.getPaymentId();
    }

    private GetTransactionDetailsResponseDto.Transaction getOriginalTransaction(String originalTxnId) {
        GetTransactionDetailsResponseDto transactionDetails = getTransactionDetails(originalTxnId);

        GetTransactionDetailsResponseDto.Transaction transaction = transactionDetails.getTransaction();
        if (transaction == null || transaction.payment() == null || transaction.payment().creditCard() == null) {
            throw new RuntimeException("Missing card details for original transaction ID: " + originalTxnId);
        }

        return transaction;
    }

    private CreateTransactionRequestDto buildRefundRequest(
            RefundTransaction refundTransaction,
            GetTransactionDetailsResponseDto.Transaction transaction,
            String originalTxnId,
            String refId
    ) {
        String lastFour = transaction.payment().creditCard().cardNumber();
        String expiry = transaction.payment().creditCard().expirationDate();

        var creditCard = new CreateTransactionRequestDto.CreditCardInfo(lastFour, expiry);
        var paymentInfo = new CreateTransactionRequestDto.PaymentInfo(creditCard);
        var refundTxnRequest = new CreateTransactionRequestDto.RefundTransactionRequest(
                "refundTransaction",
                refundTransaction.getRefundedTotal(),
                paymentInfo,
                originalTxnId
        );

        return new CreateTransactionRequestDto(
                authorizeDotNetProperties.merchantAuthentication(),
                refId,
                refundTxnRequest
        );
    }

    private static String getPaymentType(GetTransactionDetailsResponseDto.Payment payment) {
        if (payment.creditCard() != null) {
            return "card";
        } else if (payment.bankAccount() != null) {
            return "ach";
        } else {
            return "";
        }
    }

    public PaymentDetailsEvent getPaymentDetails(AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto) {
        String transactionId = String.valueOf(authorizeDotNetWebhookEventDto.getPayload().getId());

        GetTransactionDetailsResponseDto transactionDetails = getTransactionDetails(transactionId);

        String refId = transactionDetails.getTransrefId();

        AuthDotNetMapping authDotNetMapping = authDotNetMappingRepository.findByRefIdOrThrow(refId);
        return PaymentDetailsEvent.builder()
                .orderId(authDotNetMapping.getOrderId())
                .build();
    }
}
