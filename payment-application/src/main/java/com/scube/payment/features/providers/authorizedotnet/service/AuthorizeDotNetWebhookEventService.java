package com.scube.payment.features.providers.authorizedotnet.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.payment.features.providers.authorizedotnet.dto.webhook.AuthorizeDotNetWebhookEventDto;
import com.scube.payment.features.webhook.IPaymentProviderWebhookService;
import com.scube.payment.features.webhook.inbox.domain_events.PaymentDetailsEvent;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.service.WebhookInboxStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;

import static com.scube.payment.features.providers.authorizedotnet.config.AuthorizeDotNetProperties.PAYMENT_PROVIDER_NAME;

@Slf4j
@Service(PAYMENT_PROVIDER_NAME)
@RequiredArgsConstructor
@Profile("!test")
public class AuthorizeDotNetWebhookEventService implements IPaymentProviderWebhookService<AuthorizeDotNetWebhookEventDto> {

    private final AuthorizeDotNetGateway authorizeDotNetGateway;
    private final WebhookInboxStorageService webhookInboxStorageService;
    private final ObjectMapper objectMapper;

    public void processWebhook(AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto) {
        switch (authorizeDotNetWebhookEventDto.getEventType()) {
            case AUTH_CAPTURE_CREATED:
                authorizeDotNetGateway.authCapture(authorizeDotNetWebhookEventDto);
                break;
            case REFUND_CREATED:
                authorizeDotNetGateway.refund(authorizeDotNetWebhookEventDto);
                break;
            case VOID_CREATED:
                authorizeDotNetGateway._void(authorizeDotNetWebhookEventDto);
                break;
            case AUTHORIZATION_CREATED,
                    CAPTURE_CREATED,
                    FRAUD_APPROVED,
                    FRAUD_DECLINED,
                    FRAUD_HELD,
                    PRIOR_AUTH_CAPTURE_CREATED:
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unimplemented event type: " + authorizeDotNetWebhookEventDto.getEventType());
            default:
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unknown event type: " + authorizeDotNetWebhookEventDto.getEventType());
        }
    }

    @Override
    public void store(AuthorizeDotNetWebhookEventDto payload) {
        webhookInboxStorageService.store(PAYMENT_PROVIDER_NAME, payload, payload.getNotificationId().toString());
    }

    @Override
    public void process(WebhookInbox inbox) {
        Map<String, Object> payload = inbox.getPayload();
        AuthorizeDotNetWebhookEventDto dto = objectMapper.convertValue(payload, AuthorizeDotNetWebhookEventDto.class);
        processWebhook(dto);
    }

    @Override
    public PaymentDetailsEvent getPaymentDetails(WebhookInbox inbox) {
        Map<String, Object> payload = inbox.getPayload();
        AuthorizeDotNetWebhookEventDto dto = objectMapper.convertValue(payload, AuthorizeDotNetWebhookEventDto.class);
        return authorizeDotNetGateway.getPaymentDetails(dto);
    }
}