<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="james (generated)" id="1693408882396-9">
        <createTable tableName="payee">
            <column autoIncrement="true" name="payee_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="payee_pkey"/>
            </column>
            <column name="billing_address" type="VARCHAR(255)"/>
            <column name="billing_address2" type="VARCHAR(255)"/>
            <column name="billing_city" type="VARCHAR(255)"/>
            <column name="billing_same_as_mailing" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="billing_state" type="VARCHAR(255)"/>
            <column name="billing_zip_code" type="VARCHAR(255)"/>
            <column name="business_name" type="VARCHAR(255)"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="mailing_address" type="VARCHAR(255)"/>
            <column name="mailing_address2" type="VARCHAR(255)"/>
            <column name="mailing_city" type="VARCHAR(255)"/>
            <column name="mailing_state" type="VARCHAR(255)"/>
            <column name="mailing_zip_code" type="VARCHAR(255)"/>
            <column name="phone" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet id="***********" author="james">
        <renameColumn tableName="payment" oldColumnName="total" newColumnName="amount"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-10">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807"
                        minValue="1" sequenceName="fee_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-11">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807"
                        minValue="1" sequenceName="payment_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-12">
        <addColumn tableName="payment">
            <column name="order_id" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-13">
        <addColumn tableName="payment">
            <column name="payment_type" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-14">
        <addColumn tableName="payment">
            <column name="payee_id" type="int4"/>
        </addColumn>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-18">
        <addForeignKeyConstraint baseColumnNames="payee_id" baseTableName="payment"
                                 constraintName="fklxit2h3nigdg1j7kt6nuq0ipk" deferrable="false"
                                 initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE"
                                 referencedColumnNames="payee_id" referencedTableName="payee" validate="true"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-19">
        <dropColumn columnName="email" tableName="payment"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-1">
        <addNotNullConstraint columnDataType="int" columnName="payable_id" tableName="fee" validate="true"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-3">
        <addNotNullConstraint columnDataType="int" columnName="payment_id" tableName="fee" validate="true"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-4">
        <modifyDataType columnName="payment_id" newDataType="int" tableName="payment"/>
    </changeSet>
    <changeSet author="james (generated)" id="1693408882396-5">
        <addNotNullConstraint columnDataType="int" columnName="payment_id" tableName="payment" validate="true"/>
    </changeSet>
</databaseChangeLog>
