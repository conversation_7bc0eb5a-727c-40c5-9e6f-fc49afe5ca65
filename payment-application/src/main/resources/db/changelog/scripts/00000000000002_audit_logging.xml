<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1694650987906-12">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="payee_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-13">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="payment_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-14">
        <addColumn tableName="payee">
            <column name="created_by" type="varchar(250 BYTE)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-15">
        <addColumn tableName="payment">
            <column name="created_by" type="varchar(250 BYTE)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-16">
        <addColumn tableName="payee">
            <column name="created_date" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-17">
        <addColumn tableName="payment">
            <column name="created_date" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-18">
        <addColumn tableName="payee">
            <column name="last_modified_by" type="varchar(250 BYTE)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-19">
        <addColumn tableName="payment">
            <column name="last_modified_by" type="varchar(250 BYTE)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-20">
        <addColumn tableName="payee">
            <column name="last_modified_date" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-21">
        <addColumn tableName="payment">
            <column name="last_modified_date" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-22">
        <addColumn tableName="payee">
            <column name="payee_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-23">
        <addColumn tableName="payment">
            <column name="payment_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-24">
        <dropForeignKeyConstraint baseTableName="fee" constraintName="fee_payment_id_fkey"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-25">
        <dropTable tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-26">
        <dropSequence sequenceName="fee_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-27">
        <dropSequence sequenceName="payment_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-1">
        <dropForeignKeyConstraint baseTableName="payment" constraintName="fklxit2h3nigdg1j7kt6nuq0ipk"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-2">
        <addForeignKeyConstraint baseColumnNames="payee_id" baseTableName="payment"
                                 constraintName="fklxit2h3nigdg1j7kt6nuq0ipk" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="payee_id" referencedTableName="payee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-3">
        <modifyDataType columnName="amount" newDataType="number(38,2)" tableName="payment"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-4">
        <modifyDataType columnName="date_paid" newDataType="timestamp" tableName="payment"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-5">
        <modifyDataType columnName="payee_id" newDataType="bigint" tableName="payee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-6">
        <modifyDataType columnName="payee_id" newDataType="bigint" tableName="payment"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-7">
        <modifyDataType columnName="payment_id" newDataType="bigint" tableName="payment"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-8">
        <modifyDataType columnName="payment_provider" newDataType="varchar(255)" tableName="payment"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-9">
        <modifyDataType columnName="status" newDataType="varchar(255)" tableName="payment"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-10">
        <addUniqueConstraint columnNames="payment_uuid" constraintName="uk_ehd6jxjwarpj4f1a5wjaqwd6b"
                             tableName="payment"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694650987906-11">
        <addUniqueConstraint columnNames="payee_uuid" constraintName="uk_dsyx89oep1pbg2jjxgglu800k" tableName="payee"/>
    </changeSet>
</databaseChangeLog>
