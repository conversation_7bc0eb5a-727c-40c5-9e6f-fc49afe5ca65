package com.scube.payment.features.providers.authorizedotnet.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.payment.features.providers.authorizedotnet.config.AuthorizeDotNetProperties;
import com.scube.payment.features.providers.authorizedotnet.dto.webhook.AuthorizeDotNetPaymentEvent;
import com.scube.payment.features.providers.authorizedotnet.dto.webhook.AuthorizeDotNetWebhookEventDto;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.service.WebhookInboxStorageService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AuthorizeDotNetWebhookEventServiceTest {

    @Mock
    private AuthorizeDotNetGateway authorizeDotNetGateway;
    @Mock
    private WebhookInboxStorageService webhookInboxStorageService;
    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private AuthorizeDotNetWebhookEventService authorizeDotNetWebhookEventService;

    @Test
    void processAuthCapture() {
        AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto = new AuthorizeDotNetWebhookEventDto();
        authorizeDotNetWebhookEventDto.setEventType(AuthorizeDotNetPaymentEvent.AUTH_CAPTURE_CREATED);

        doNothing().when(authorizeDotNetGateway).authCapture(any(AuthorizeDotNetWebhookEventDto.class));

        authorizeDotNetWebhookEventService.processWebhook(authorizeDotNetWebhookEventDto);

        verify(authorizeDotNetGateway).authCapture(authorizeDotNetWebhookEventDto);
    }

    @Test
    void processUnimplementedWebhookType() {
        AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto = new AuthorizeDotNetWebhookEventDto();
        authorizeDotNetWebhookEventDto.setEventType(AuthorizeDotNetPaymentEvent.AUTHORIZATION_CREATED);

        assertThrows(ResponseStatusException.class, () -> authorizeDotNetWebhookEventService.processWebhook(authorizeDotNetWebhookEventDto));
    }

    @Test
    void process() {
        AuthorizeDotNetWebhookEventService authorizeDotNetWebhookEventService = spy(this.authorizeDotNetWebhookEventService);

        Map expectedPayload = Map.of();
        WebhookInbox inbox = new WebhookInbox();
        inbox.setPayload(expectedPayload);

        AuthorizeDotNetWebhookEventDto expectedDto = new AuthorizeDotNetWebhookEventDto();

        when(objectMapper.convertValue(expectedPayload, AuthorizeDotNetWebhookEventDto.class)).thenReturn(expectedDto);
        doNothing().when(authorizeDotNetWebhookEventService).processWebhook(expectedDto);

        authorizeDotNetWebhookEventService.process(inbox);

        verify(authorizeDotNetWebhookEventService).processWebhook(expectedDto);
    }

    @Test
    void store() {
        UUID notificationId = UUID.randomUUID();

        AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto = new AuthorizeDotNetWebhookEventDto();
        authorizeDotNetWebhookEventDto.setNotificationId(notificationId);

        when(webhookInboxStorageService.store(AuthorizeDotNetProperties.PAYMENT_PROVIDER_NAME, authorizeDotNetWebhookEventDto, notificationId.toString())).thenReturn(null);

        authorizeDotNetWebhookEventService.store(authorizeDotNetWebhookEventDto);

        verify(webhookInboxStorageService).store(AuthorizeDotNetProperties.PAYMENT_PROVIDER_NAME, authorizeDotNetWebhookEventDto, notificationId.toString());
    }
}