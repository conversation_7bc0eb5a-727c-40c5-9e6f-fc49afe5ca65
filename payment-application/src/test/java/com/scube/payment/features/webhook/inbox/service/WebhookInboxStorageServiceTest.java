package com.scube.payment.features.webhook.inbox.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.payment.features.webhook.IWebhookPayload;
import com.scube.payment.features.webhook.inbox.model.InboxStatus;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.repo.WebhookInboxRepository;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class WebhookInboxStorageServiceTest {
    @Mock
    private WebhookInboxRepository webhookInboxRepository;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private AmqpGateway amqpGateway;

    @InjectMocks
    private WebhookInboxStorageService webhookInboxStorageService;

    @Test
    public void testStore() {
        String providerName = "providerName";
        IWebhookPayload payload = mock(IWebhookPayload.class);

        Map<String, Object> payloadMap = mock(Map.class);

        String provider = "provider";

        WebhookInbox expectedWebhookInbox = new WebhookInbox(provider, payloadMap, "webhook-id");

        ArgumentCaptor<WebhookInbox> captor = ArgumentCaptor.forClass(WebhookInbox.class);

        when(webhookInboxRepository.save(captor.capture())).thenReturn(expectedWebhookInbox);
        doNothing().when(amqpGateway).publish(any(IRabbitFanoutPublisher.class));

        WebhookInbox actualWebhookInbox = webhookInboxStorageService.store(providerName, payload, "webhook-id");

        verify(webhookInboxRepository).save(any(WebhookInbox.class));

        assertEquals(captor.getValue().getStatus(), InboxStatus.PENDING);
        assertEquals(expectedWebhookInbox.getPayload(), actualWebhookInbox.getPayload());
        assertEquals(expectedWebhookInbox.getStatus(), actualWebhookInbox.getStatus());
        assertEquals(expectedWebhookInbox.getPaymentProvider(), actualWebhookInbox.getPaymentProvider());
    }
}
