<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.4</version>
    </parent>

    <groupId>com.scube.documenttemplates</groupId>
    <artifactId>DocumentTemplateService</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>DocumentTemplateService</name>
    <modules>
        <module>documenttemplates-application</module>
        <module>documenttemplates-client</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jib.version>3.4.5</jib.version>
        <liquibase.version>4.22.0</liquibase.version>

        <service-dependicies.version>1.1.4</service-dependicies.version>

        <registry>514329541303.dkr.ecr.us-east-1.amazonaws.com</registry>
        <imagename>service_document_template</imagename>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.scube.dependencies</groupId>
                <artifactId>ServiceDependiciesLibrary</artifactId>
                <version>${service-dependicies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib.version}</version>
                <configuration>
                    <container>
                        <!-- Manually define main class due to CI/CD issues -->
                        <mainClass>com.scube.documenttemplates.DocumentTemplateServiceApplication</mainClass>
                        <jvmFlags>
                            <jvmFlag>-XX:+UseContainerSupport</jvmFlag>
                            <jvmFlag>-XX:+IdleTuningGcOnIdle</jvmFlag>
                            <jvmFlag>-XX:+UseCGroupMemoryLimitForHeap</jvmFlag>
                            <jvmFlag>-XX:+UnlockExperimentalVMOptions</jvmFlag>
                        </jvmFlags>
                    </container>
                    <to>
                        <image>${registry}/${imagename}</image>
                        <credHelper>ecr-login</credHelper>
                    </to>
                    <from>
                        <image>ibm-semeru-runtimes:open-21-jdk</image>
                    </from>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>codeartifact</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>codeartifact</id>
                    <url>https://scube-514329541303.d.codeartifact.us-east-1.amazonaws.com/maven/maven/</url>
                </repository>
            </repositories>
        </profile>
    </profiles>
</project>

