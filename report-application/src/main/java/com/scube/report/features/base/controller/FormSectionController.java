package com.scube.report.features.base.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.report.features.base.dto.FormSectionDto;
import com.scube.report.features.base.entity.FormSection;
import com.scube.report.features.base.mapper.FormSectionMapper;
import com.scube.report.features.base.service.FormSectionService;
import com.scube.report.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/form-section")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.REPORT_SERVICE)
public class FormSectionController {
    private final FormSectionService formSectionService;
    private final FormSectionMapper mapper = FormSectionMapper.INSTANCE;

    @GetMapping
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.FormSection.GET_ALL_SECTIONS)
    public List<FormSection> getAllSections() {
        return formSectionService.getAllSections();
    }

    @GetMapping("/{uuid}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.FormSection.GET_SECTION_BY_UUID)
    public FormSectionDto getSectionByUuid(@PathVariable UUID uuid) {
        return mapper.toDto(formSectionService.getSectionByUuid(uuid));
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.FormSection.ADD_SECTION)
    public FormSectionDto addSection(@Valid @RequestBody FormSectionDto formSection) {
        return mapper.toDto(formSectionService.addSection(formSection));
    }

    @DeleteMapping("/{uuid}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.FormSection.DELETE_SECTION)
    public void deleteSection(@PathVariable UUID uuid) {
        formSectionService.deleteSection(uuid);
    }
}
