package com.scube.report.features.base.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.Audited;

import java.time.Instant;
import java.util.UUID;

import static com.scube.report.features.base.entity.Report.TABLE_NAME;

@Entity
@Table(name = TABLE_NAME)
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class Report extends BaseEntity {
    public static final String TABLE_NAME = "report";
    public static final String TABLE_ID = "report_id";

    @Size(max = 255)
    private String userId;
    private Instant completedDate;
    private UUID documentId;
    @Enumerated(EnumType.STRING)
    private ReportStatus reportStatus;

    @ManyToOne
    @JoinColumn(name = ReportType.TABLE_ID, nullable = false)
    private ReportType reportType;

    public void initialize() {
        this.reportStatus = ReportStatus.PENDING;
    }

    public void initialize(ReportType reportType) {
        this.reportType = reportType;
        this.initialize();
    }

    public void complete() {
        this.reportStatus = ReportStatus.COMPLETED;
        this.completedDate = Instant.now();
    }

    public void fail(String message) {
        this.reportStatus = ReportStatus.FAILED;
        this.completedDate = Instant.now();
        this.setProperty("errorMessage", message);
    }

    public String templateKey() {
        return this.reportType.getReportTemplateKey();
    }
}
