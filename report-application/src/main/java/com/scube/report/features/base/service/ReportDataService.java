package com.scube.report.features.base.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.auth.library.ITokenService;
import com.scube.config_utils.json_storage.utils.JsonStorageUtils;
import com.scube.config_utils.sql_storage.utils.SqlStorageUtils;
import com.scube.report.features.base.dto.GenerateReportRequest;
import com.scube.report.features.base.dto.Query;
import com.scube.report.features.base.dto.QueryType;
import com.scube.report.features.base.dto.User;
import com.scube.report.features.base.entity.ReportType;
import com.scube.report.features.base.util.DynamicViewUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.scube.report.features.licensing.dog.constants.ReportConstants.PAGE_BREAK;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReportDataService {
    private final NamedParameterJdbcTemplate jdbcTemplate;
    private final Map<String, IReportQueryService> serviceMap;
    private final ITokenService tokenService;

    @SneakyThrows
    public Object getReportData(GenerateReportRequest request) {
        return getReportData(request.getQueries(), request.getBatch());
    }

    @SneakyThrows
    public Object getReportData(ReportType reportType, Map<String, Object> params) {
        Map<String, Query> queries = new LinkedHashMap<>(reportType.queries());

        setQueryParameters(params, queries);

        if (queries.values().size() == 1 && queries.values().stream().findFirst().orElseThrow().getQueryType().equals(QueryType.SERVICE)) {
            //this is an old report so we have to flatten the json
            return query(queries).values().stream().findFirst().orElse(null);
        }

        return getReportData(queries, reportType.isBatch());
    }

    private static void setQueryParameters(Map<String, Object> params, Map<String, Query> queries) {
        for (Map.Entry<String, Query> entry : queries.entrySet()) {
            Query query = entry.getValue();
            for (Map.Entry<String, Object> param : params.entrySet()) {
                if (query.getParameters().containsKey(param.getKey())) {
                    query.getParameters().put(param.getKey(), param.getValue());
                }
            }
        }
    }

    @SneakyThrows
    public Object getReportData(Map<String, Query> queries, Boolean batch) {
        var start = System.currentTimeMillis();
        Map<String, Object> reportData = query(queries);

        reportData.put("params", coalesceQueryParameters(queries));
        reportData.put("user", new User(tokenService.getLoggedInUserInfo()));
        reportData.put("currentDate", new SimpleDateFormat("MM/dd/yyyy").format(new Date()));

        if (batch) {
            Map<String, Object> batchReportData = new LinkedHashMap<>();
            batchReportData.put("batch", reportData);
            batchReportData.put("pageBreak", PAGE_BREAK);
            return batchReportData;
        }

        log.info("getReportData: Took {}ms to generate report data", System.currentTimeMillis() - start);
        return reportData;
    }

    private static Map<String, Object> coalesceQueryParameters(Map<String, Query> queries) {
        return queries.values().stream()
                .map(Query::getParameters)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> replacement // or some other logic to merge values
                ));
    }

    public Map<String, Object> query(Map<String, Query> queries) {
        Map<String, Object> result = new HashedMap<>();

        for (var query : queries.entrySet()) {
            result.put(query.getKey(), query(query.getValue()));
        }

        return result;
    }

    public Object query(Query query) {
        return switch (query.getQueryType()) {
            case SQL -> querySql(query);
            case JSON -> queryJsonStorage(query);
            case SERVICE -> queryService(query);
        };
    }

    public List<Map<String, Object>> querySql(Query query) {
        var sql = SqlStorageUtils.getByName(query.getQueryKey()).getSql();

        return querySql(sql, query.getParameters());
    }

    public List<Map<String, Object>> querySql(String sql, Map<String, Object> params) {
        List<Map<String, Object>> data = jdbcTemplate.queryForList(sql, Objects.requireNonNullElseGet(params, Map::of));

        if (ObjectUtils.isEmpty(data)) {
            return new ArrayList<>();
        }

        var formattedData = data.stream()
                .map(DynamicViewUtils::formatData)
                .collect(Collectors.toList());

        return formattedData;
    }

    public JsonNode queryJsonStorage(Query query) {
        Map<String, String> properties = Arrays.stream(query.getQueryKey().split(","))
                .map(property -> property.split("="))
                .collect(Collectors.toMap(
                        keyValue -> keyValue[0],
                        keyValue -> keyValue[1]
                ));

        return JsonStorageUtils.get(properties).orElse(null);
    }

    public Object queryService(Query query) {
        var service = serviceMap.get(query.getQueryKey());
        return service.execute(query.getParameters());
    }
}
