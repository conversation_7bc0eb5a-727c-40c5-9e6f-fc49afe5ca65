package com.scube.report.features.licensing.dog.dto;

import java.math.BigDecimal;

public enum ReportFees {
    NEW_ALTERED_FEE("New Altered", "13.50"),
    NEW_UNALTERED_FEE("New Unaltered", "20.50"),
    NEW_ALTERED_SENIOR_FEE("New Altered Senior", "3.50"),
    NEW_UNALTERED_SENIOR_FEE("New Unaltered Senior", "10.50"),
    RENEWAL_ALTERED_FEE("Renewal Altered", "13.50"),
    RENEWAL_UNALTERED_FEE("Renewal Unaltered", "20.50"),
    RENEWAL_ALTERED_SENIOR_FEE("Renewal Altered Senior", "3.50"),
    RENEWAL_UNALTERED_SENIOR_FEE("Renewal Unaltered Senior", "10.50"),
    DOG_TAG_FEE("Dog Tags", "2.50");

    private final String feeAmount;
    private final String feeName;

    ReportFees(String feeName, String feeAmount) {
        this.feeName = feeName;
        this.feeAmount = feeAmount;
    }

    public String getFeeAmountAsString() {
        return feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return new BigDecimal(feeAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public String getFeeName() {
        return feeName;
    }
}

