package com.scube.report.features.licensing.dog.enums;

public enum ActivityType {
    NEW,
    RENEWAL,
    ADD_PUREBRED_DOG,
    OTHER;

    public static ActivityType fromString(String activityStr) {
        return switch (activityStr.toUpperCase()) {
            case "NEW" -> NEW;
            case "RENEWAL" -> RENEWAL;
            case "ADD_PUREBRED_DOG" -> ADD_PUREBRED_DOG;
            default -> OTHER;
        };
    }
}