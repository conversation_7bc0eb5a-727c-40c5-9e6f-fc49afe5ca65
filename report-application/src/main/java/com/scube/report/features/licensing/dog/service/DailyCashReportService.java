package com.scube.report.features.licensing.dog.service;

import com.scube.auth.library.ITokenService;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.payment.generated.PaymentServiceConnection;
import com.scube.payment.features.payment.processing.dto.gen_dto.GetPaymentResponseDto;
import com.scube.report.features.base.service.IReportQueryService;
import com.scube.report.features.licensing.dog.dto.reports.DailyCashReport;
import com.scube.report.features.licensing.dog.dto.LicenseProjection;
import com.scube.report.features.licensing.dog.dto.ReportHeader;
import com.scube.report.features.licensing.dog.dto.request.ReportRequest;
import com.scube.report.features.licensing.dog.enums.PaymentType;
import com.scube.report.features.licensing.dog.rabbit.GetLicenseActivitiesByOrderIdQuery;
import com.scube.report.features.licensing.dog.rabbit.GetLicenseProjectionQuery;
import com.scube.report.features.base.util.RabbitMqUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.scube.report.features.base.util.ValidationUtil.validate;
import static com.scube.report.features.licensing.dog.util.DogLicenseReportUtil.*;
import static com.scube.report.features.base.util.StringUtils.camelCaseToCapitalizedWords;

@Service("DailyCashReport")
@Slf4j
@RequiredArgsConstructor
public class DailyCashReportService implements IReportQueryService {
    private static final String REPORT_NAME = "DailyCashReport";
    private final CalculationServiceConnection calculationService;
    private final PaymentServiceConnection paymentService;
    private final ITokenService iTokenService;

    @Override
    public Object execute(Map<String, Object> params) {
        log.debug("DogLicenseReportService.getReport()");

        ReportRequest request = new ReportRequest(params);
        validate(request);

        BigDecimal cashTotal = BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
        BigDecimal personalCheckTotal = BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
        BigDecimal creditTotal = BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
        BigDecimal certifiedCheckTotal = BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
        BigDecimal moneyOrderTotal = BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
        BigDecimal achTotal = BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);

        //Retrieve all the paid orders
        List<OrderInvoiceResponse> orders = calculationService.order().findOrdersByFilters(
                new HashMap<>(Map.of(
                        "status", "ORDER_PAID",
                        "startDate", (String) params.get("startDate"),
                        "endDate", (String) params.get("endDate"))));

        List<DailyCashReport.Payment> payments = new ArrayList<>();

        for (OrderInvoiceResponse order : orders) {
            if (order == null) continue;

            List<GetPaymentResponseDto> paymentDtos = paymentService.payment().getPaymentsByOrderId(order.getOrderId());

            if (paymentDtos == null || paymentDtos.size() == 0) {
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Failed to retrieve payment information for order: " + order.getOrderId().toString());
            }

            GetPaymentResponseDto firstPayment = paymentDtos.get(0);

            PaymentType paymentType = PaymentType.from(firstPayment.getPaymentType());

            for (OrderInvoiceItem item : order.getItems()) {
                String feeDescription = "";
                String feeName = "";
                String licenseNumber = "";

                if (isDogLicense(item)) {
                    LicenseProjection dogLicense = RabbitMqUtil.query(new GetLicenseProjectionQuery(item.getItemId().toString()));

                    if (dogLicense != null) {
                        licenseNumber = dogLicense.getLicenseNumber();
                    }

                    List<String> licenseActivities = RabbitMqUtil.query(new GetLicenseActivitiesByOrderIdQuery(order.getOrderId().toString())).licenseActivities();

                    feeName = getLicenseType(item, dogLicense, licenseActivities);
                    feeDescription = getLicenseDescription(item);

                } else {
                    feeName = item.getPrimaryDisplay();
                    feeDescription = item.getSecondaryDisplay();
                }

                var payment = new DailyCashReport.Payment();
                payment.setFeeDescription(feeDescription);
                payment.setFeeName(feeName);
                payment.setLicenseNumber(licenseNumber);
                payment.setQuantity("1");
                payment.setStateFee(item.getStateFees().toString());
                payment.setCityFee(item.getCityFees().toString());
                payment.setAmount(item.getTotal().setScale(2, RoundingMode.CEILING).toString());

                if (firstPayment != null) {
                    String paymentDate = firstPayment.getTransactionDate() == null ? "" : firstPayment.getTransactionDate().atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
                    payment.setDate(paymentDate);
                    payment.setPaymentMethod(camelCaseToCapitalizedWords(firstPayment.getPaymentType()));
                    payment.setPaymentNumber(String.valueOf(firstPayment.getPaymentNumber()));
                    if (firstPayment.getPayee() != null) {
                        payment.setPaidBy(firstPayment.getPayee().getFirstName() + " " + firstPayment.getPayee().getLastName());
                    }
                    payment.setMisc(firstPayment.getPaymentReference());
                    payment.setName(firstPayment.getCreatedBy());
                }

                payments.add(payment);

                switch (paymentType) {
                    case CASH:
                        cashTotal = cashTotal.add(item.getTotal());
                        break;
                    case PERSONAL_CHECK:
                        personalCheckTotal = personalCheckTotal.add(item.getTotal());
                        break;
                    case CERTIFIED_CHECK:
                        certifiedCheckTotal = certifiedCheckTotal.add(item.getTotal());
                        break;
                    case CREDIT:
                        creditTotal = creditTotal.add(item.getTotal());
                        break;
                    case MONEY_ORDER:
                        moneyOrderTotal = moneyOrderTotal.add(item.getTotal());
                        break;
                    case ACH:
                        achTotal = achTotal.add(item.getTotal());
                        break;
                    default:
                        throw new IllegalArgumentException("Unknown payment type: " + paymentType.getKey());
                }
            }
        }

        DailyCashReport dailyCashReport = DailyCashReport.builder()
                .reportHeader(ReportHeader.of(params, iTokenService.getLoggedInUserInfo()))
                .cashTotal(cashTotal)
                .creditTotal(creditTotal)
                .personalCheckTotal(personalCheckTotal)
                .moneyOrderTotal(moneyOrderTotal)
                .certifiedCheckTotal(certifiedCheckTotal)
                .achTotal(achTotal)
                .payments(payments)
                .build();

        return dailyCashReport;
    }
}
