<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Ben" id="insertFormSections">
        <sql>
            insert into form_section (form_section_uuid, created_date, last_modified_date, created_by, last_modified_by, name)
            values
            (gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'dogFormsAndReports'),
            (gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'clerkFormsAndReports');
        </sql>
    </changeSet>
    <changeSet author="Ben" id="insertReportFormData">
        <sql>
            insert into report_type (report_type_uuid, created_date, last_modified_date, created_by, last_modified_by,
            report_template_key, form_section_id, form_data)
            values
            (gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'AnimalPopulationControlFundForm'
            ,(select form_section_id from form_section where name = 'dogFormsAndReports')
            ,'{
            "label": "Animal Population Control Fund Form",
            "description": "These funds are used in the statewide animal population control program administered by the ASPCA pursuant to New York State Agriculture and Markets Law Section 117-a.",
            "type": "date",
            "parameters": [
            {
            "type": "dateRange",
            "params": [
            "today",
            "yesterday",
            "lastMonth",
            "currentMonth"
            ],
            "defaultParam": "lastMonth"
            },
            {
            "type": "text",
            "label": "Check Number",
            "fieldName": "checkNumber"
            }
            ]
            }')
            ,(gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'ClerksMonthlyReport'
            ,(select form_section_id from form_section where name = 'clerkFormsAndReports')
            ,'{
            "label": "Clerk Reports",
            "description": "Report for all clerk activities by date range.",
            "type": "date",
            "parameters": [
            {
            "type": "dateRange",
            "params": [
            "today",
            "yesterday",
            "lastMonth",
            "currentMonth"
            ],
            "defaultParam": "lastMonth"
            }
            ]
            }')
            ,(gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'ClerkSummaryReport'
            ,(select form_section_id from form_section where name = 'clerkFormsAndReports')
            ,'{
            "label": "Clerk Summary Report",
            "description": "Summary Report for all clerk activities by date range",
            "type": "date",
            "parameters": [
            {
            "type": "dateRange",
            "label": "Date Range",
            "params": ["today", "yesterday", "lastMonth", "currentMonth"],
            "defaultParam": "today"
            }
            ]
            }')
            ,(gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'DailyCashReport'
            ,(select form_section_id from form_section where name = 'clerkFormsAndReports')
            ,'{
            "label": "Daily Cash Report",
            "description": "Report for all daily clerk transactions.",
            "type": "date",
            "parameters": [
            {
            "type": "dateRange",
            "label": "Date Range",
            "params": [
            "today",
            "yesterday",
            "lastMonth",
            "currentMonth"
            ],
            "defaultParam": "lastMonth"
            }
            ]
            }')
            ,(gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'DelinquentDogReport'
            ,(select form_section_id from form_section where name = 'dogFormsAndReports')
            ,'{
            "label": "Delinquent Dog Report",
            "description": "This report shows all the current delinquent dog licenses.",
            "type": "date",
            "parameters": [
            {
            "type": "dateRange",
            "params": [
            "today",
            "yesterday",
            "lastMonth",
            "currentMonth"
            ],
            "defaultParam": "lastMonth"
            }
            ]
            }')
            ,(gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'DogLicenseMonthlyReport'
            ,(select form_section_id from form_section where name = 'dogFormsAndReports')
            ,'{
            "label": "Dog License Report",
            "description": "This report shows the number of dog licenses issued by month.",
            "type": "date",
            "parameters": [
            {
            "type": "dateRange",
            "label": "Date Range",
            "params": [
            "today",
            "yesterday",
            "lastMonth",
            "currentMonth"
            ],
            "defaultParam": "lastMonth"
            }
            ]
            }')
            ,(gen_random_uuid(), current_timestamp, current_timestamp, 'SYSTEM', 'SYSTEM', 'DogTransactionReport'
            ,(select form_section_id from form_section where name = 'dogFormsAndReports')
            ,'{
            "label": "Dog Transaction Report",
            "description": "Report for all dog license transactions by date range.",
            "type": "date",
            "parameters": [
            {
            "type": "dateRange",
            "params": [
            "today",
            "yesterday",
            "lastMonth",
            "currentMonth"
            ],
            "defaultParam": "lastMonth"
            }
            ]
            }');
        </sql>
    </changeSet>
</databaseChangeLog>
