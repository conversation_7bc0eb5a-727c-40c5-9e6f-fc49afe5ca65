apiVersion: skaffold/v4beta1
kind: Config
metadata:
  name: backend
build:
  artifacts:
    - image: service_imageprocessing
      jib:
        project: com.scube.imageprocessing:imageprocessing-application

manifests:
  rawYaml:
    - deployment.yml

profiles:
  # Local cluster - we build and deploy things locally
  - name: local
    build:
      local:
        push: false
    activation:
      - kubeContext: docker-desktop
    deploy:
      kubectl:
        defaultNamespace: backend
