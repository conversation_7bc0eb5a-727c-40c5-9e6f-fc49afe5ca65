apiVersion: skaffold/v4beta6
kind: Config
metadata:
  name: backend
build:
  artifacts:
    - image: service_notification
      jib:
        project: com.scube.notification:notification-application

manifests:
  rawYaml:
    - deployment.yml

profiles:
  - name: local
    activation:
      - kubeContext: docker-desktop
    build:
      local:
        push: false
    deploy:
      kubectl:
        defaultNamespace: backend
