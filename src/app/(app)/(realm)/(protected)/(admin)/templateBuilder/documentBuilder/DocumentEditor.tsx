"use client";
import { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller, FormProvider } from "react-hook-form";
import { useRouter } from "next/navigation";
import { Template, useUpdateDocumentTemplate } from "@/hooks/api/useDocument";

import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import TextareaAutosize from "react-textarea-autosize";
import { QueriesSection } from "./QueriesSection";
import { DocumentFileSection } from "./DocumentFileSection";
import { use<PERSON>tom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";

interface DocumentEditorProps {
  document: Template;
}

export default function DocumentEditor({
  document,
}: DocumentEditorProps) {
  console.log(document);

  const router = useRouter();
  const updateDocumentTemplate = useUpdateDocumentTemplate();

  const availableCategoriesInitial = ["Batch Printing", "Reports", "Emails"];
  const [categories, setCategories] = useState<string[]>(
    availableCategoriesInitial,
  );

  // lift combobox state
  const [categoryOpen, setCategoryOpen] = useState(false);
  const [categoryQuery, setCategoryQuery] = useState<string>(
    document.category || "",
  );
  const [_, setToast] = useAtom(toastAtom);

  const form = useForm<Template>({
    defaultValues: {
      ...document,
      file: document.file || null,
      isReport: document.isReport || false,
    },
  });

  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch,
    setValue,
  } = form;

  const isReport = watch("isReport");

  const filteredCategories =
    categoryQuery === ""
      ? categories
      : categories.filter((cat) =>
          cat.toLowerCase().includes(categoryQuery.toLowerCase()),
        );

  const onSubmit = async (data: Template) => {
    // remove what the API doesn’t need in the payload
    delete data.documentUUID;
    delete data.formData;

    // pull out the UUID so we can still send it in the URL

    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value == null) {
        // skip null or undefined
        return;
      }

      // if it’s a File (or Blob), send it directly
      if (value instanceof File || value instanceof Blob) {
        formData.append(key, value);
      }
      // if it’s an object or array, JSON‑encode it
      else if (typeof value === "object") {
        formData.append(key, JSON.stringify(value));
      }
      // primitives: number / boolean / string
      else {
        formData.append(key, String(value));
      }
    });

    // now call your mutation
    await updateDocumentTemplate.mutateAsync(
      {
        data: formData,
      },
      {
        onSuccess(newData) {
          setToast({
            label: "Document saved",
            message: "Your document has been saved successfully.",
            status: "success",
          });
        },
        onError(err) {
          console.log(err);
          const message = (err as any)?.response?.data?.message || "There was an error saving your document.";
          setToast({
            label: "Error saving document",
            message: message,
            status: "error",
          });
        },
      },
    );
  };

  const handleCancel = () => {
    if (
      isDirty &&
      confirm("You have unsaved changes. Are you sure you want to cancel?")
    ) {
      router.back();
    } else if (!isDirty) {
      router.back();
    }
  };

  return (
    <FormProvider {...form}
      key={document.templateUUID}
    >
      <div className="flex h-full w-full flex-col overflow-auto">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <div className="relative mb-8 flex items-center justify-between">
            <div className="flex w-full justify-end gap-3">
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={updateDocumentTemplate.isLoading}
                type="button"
              >
                Cancel
              </Button>
              <Button
                variant={"primary"}
                onClick={handleSubmit(onSubmit)}
                disabled={updateDocumentTemplate.isLoading || !isDirty}
                type="submit"
              >
                {updateDocumentTemplate.isLoading ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Document Information */}
            <div className="rounded-lg bg-white shadow">
              <div className="border-b bg-gray-50 px-4 py-4">
                <h2 className="text-lg font-medium">Document Information</h2>
              </div>
              <div className="space-y-6 px-4 py-5">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-6">
                  {/* Name */}
                  <div className="sm:col-span-4">
                    <label htmlFor="name" className="block text-sm font-medium">
                      Document Name
                    </label>
                    <Input
                      id="name"
                      {...register("name", {
                        required: "Document name is required",
                      })}
                      className={cn(
                        "mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500",
                        errors.name ? "border-red-300" : "border-gray-300",
                      )}
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.name.message}
                      </p>
                    )}
                  </div>

                  {/* Category Combobox */}
                  <Controller
                    name="category"
                    control={control}
                    rules={{ required: "Category is required" }}
                    render={({ field, fieldState }) => (
                      <div className="sm:col-span-3">
                        <label className="mb-1 block text-sm font-medium">
                          Category
                        </label>
                        <Popover
                          open={categoryOpen}
                          onOpenChange={setCategoryOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              type="button"
                              variant="outline"
                              role="combobox"
                              aria-expanded={categoryOpen}
                              className={cn(
                                "w-full justify-between",
                                fieldState.error ? "border-red-500" : "",
                              )}
                            >
                              {field.value || "Select or type category"}
                              <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search or create category..."
                                value={categoryQuery}
                                onValueChange={setCategoryQuery}
                              />
                              <CommandList>
                                <CommandEmpty>No category found.</CommandEmpty>
                                <CommandGroup>
                                  {filteredCategories.map((cat) => (
                                    <CommandItem
                                      key={cat}
                                      value={cat}
                                      onSelect={(val) => {
                                        const matchedCat =
                                          filteredCategories.find(
                                            (c) =>
                                              c.toLowerCase() ===
                                              val.toLowerCase(),
                                          );
                                        field.onChange(matchedCat);
                                        setCategoryQuery(matchedCat || "");
                                        setCategoryOpen(false);
                                      }}
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          field.value === cat
                                            ? "opacity-100"
                                            : "opacity-0",
                                        )}
                                      />
                                      {cat}
                                    </CommandItem>
                                  ))}
                                  {categoryQuery &&
                                    !categories.includes(categoryQuery) && (
                                      <CommandItem
                                        value={categoryQuery}
                                        onSelect={(val) => {
                                          setCategories((prev) => [
                                            ...prev,
                                            val,
                                          ]);
                                          field.onChange(val);
                                          setCategoryOpen(false);
                                        }}
                                      >
                                        Create &quot;{categoryQuery}&quot;
                                      </CommandItem>
                                    )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        {fieldState.error && (
                          <p className="mt-1 text-sm text-red-600">
                            {fieldState.error.message}
                          </p>
                        )}
                      </div>
                    )}
                  />

                  {/* Is Report Checkbox */}
                  <div className="sm:col-span-6">
                    <div className="flex items-center space-x-2">
                      <Controller
                        name="isReport"
                        control={control}
                        render={({ field }) => (
                          <Checkbox
                            id="isReport"
                            checked={field.value}
                            onCheckedChange={(checked) => {
                              field.onChange(checked);
                            }}
                          />
                        )}
                      />
                      <label
                        htmlFor="isReport"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Is Report
                      </label>
                    </div>
                  </div>

                  {/* Description */}
                  <div className="sm:col-span-6">
                    <label
                      htmlFor="description"
                      className="block text-sm font-medium"
                    >
                      Description
                    </label>
                    <TextareaAutosize
                      id="description"
                      minRows={3}
                      maxRows={10}
                      {...register("description")}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-4 py-1 focus:border-blue-500 focus:ring-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Brief description of the document&apos;s purpose
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Document File Section */}
            <DocumentFileSection documentUUID={document.documentUUID} />

            {/* Queries Section */}
            {isReport && <QueriesSection />}
          </form>
        </div>
      </div>
    </FormProvider>
  );
}
