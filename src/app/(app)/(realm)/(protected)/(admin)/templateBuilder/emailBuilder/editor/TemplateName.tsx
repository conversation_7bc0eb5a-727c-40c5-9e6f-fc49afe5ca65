import React from 'react'

const TemplateName = ({
  templateName,
  setTemplateName
}:{
  templateName: string,
  setTemplateName: (templateName: string) => void
}) => {
  return (
    <div className="flex flex-col gap-1">
      <small className="shrink-0 text-neutral-950 font-semibold">
        Template Name:
      </small>
      <input
        className="border rounded px-2 py-1 w-full"
        value={templateName}
        onChange={(e) => setTemplateName(e.target.value)}
      />
    </div>
  )
}

export default TemplateName