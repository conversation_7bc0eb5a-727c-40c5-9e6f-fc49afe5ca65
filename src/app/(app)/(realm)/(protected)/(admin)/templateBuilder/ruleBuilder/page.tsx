"use client";
import React from "react";
import {
  RuleBuilderWizardProvider,
  useRuleBuilderWizard,
} from "./RuleBuilderWizardContext";
import Tabs from "./main/Tabs";
import JsonWizard from "./builder/JsonWizard";
import FormList from "./settings/FormList";

const RuleBuilderWizardPage = () => {
  return (
    <RuleBuilderWizardProvider>
      <RuleBuilderWizardContent />
    </RuleBuilderWizardProvider>
  );
};

const RuleBuilderWizardContent = () => {
  const { tab } = useRuleBuilderWizard();

  return (
    <div className="flex h-full w-full flex-col overflow-hidden">
      <Tabs />
      {tab === "rules" && <JsonWizard />}
      {tab === "settings" && <FormList />}
    </div>
  );
};

export default RuleBuilderWizardPage;