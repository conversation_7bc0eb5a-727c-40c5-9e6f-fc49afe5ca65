"use client";
import {
  useGetProjectIssues,
  useGetRedmineProjectId,
  useUpdateRedmineProjectId,
} from "@/hooks/api/useSupport";
import { DataTable } from "../DataTable";
import { columns } from "../Columns";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import { LuSettings } from "react-icons/lu";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const OpenTickets = () => {
  const [projectId, setProjectId] = useState<number | null>(null);

  const {
    data: redmineProjectId,
    isLoading: isLoadingRedmineProjectId,
    isError: isErrorRedmineProjectId,
  } = useGetRedmineProjectId();

  const { data, isLoading, isError } = useGetProjectIssues(
    redmineProjectId?.value,
  );

  useEffect(() => {
    if (redmineProjectId?.value) {
      setProjectId(redmineProjectId?.value);
    }
  }, [redmineProjectId]);

  // Handle loading states - wait for project ID first
  if (isLoadingRedmineProjectId)
    return <div>Loading project configuration...</div>;
  if (isErrorRedmineProjectId)
    return <div>Error loading project configuration</div>;
  if (!redmineProjectId) return <div>No project ID configured</div>;

  // Handle case where we don't have a project ID yet
  if (!projectId) return <div>Setting up project configuration...</div>;

  // Handle issues loading states
  if (isLoading) return <div>Loading tickets...</div>;
  if (isError) return <div>Error Loading Open Tickets</div>;
  if (!data) return <div>No Data</div>;

  return (
    <div>
      <DataTable columns={columns} data={data} />
    </div>
  );
};

const UpdateRedmineProjectIdModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: redmineProjectId } = useGetRedmineProjectId();
  const updateRedmineProjectId = useUpdateRedmineProjectId();
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<{ projectId: number }>({
    defaultValues: {
      projectId: redmineProjectId?.value || 0,
    },
  });

  const onSubmit = (data: { projectId: number }) => {
    updateRedmineProjectId.mutate(data.projectId, {
      onSuccess: () => {
        setToast({
          status: "success",
          label: "Success",
          message: "Redmine Project ID updated successfully",
        });
        queryClient.invalidateQueries(["redmineProjectId"]);
        queryClient.invalidateQueries(["projectIssues"]);
        setIsOpen(false);
        reset();
      },
      onError: (error: any) => {
        setToast({
          status: "error",
          label: "Error",
          message:
            error?.response?.data?.message ||
            "Failed to update Redmine Project ID",
        });
      },
    });
  };

  const { hasPermissions } = useMyProfile();

  if (!hasPermissions(["manage-admin-settings"])) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger className="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-3 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
        <LuSettings className="h-4 w-4" />
        Configure Project ID
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Redmine Project ID</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="projectId">Project ID</Label>
            <Input
              id="projectId"
              type="number"
              placeholder="Enter project ID"
              {...register("projectId", {
                required: "Project ID is required",
                min: { value: 1, message: "Project ID must be greater than 0" },
                valueAsNumber: true,
              })}
            />
            {errors.projectId && (
              <p className="text-sm text-red-500">{errors.projectId.message}</p>
            )}
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsOpen(false);
                reset();
              }}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={updateRedmineProjectId.isLoading}>
              {updateRedmineProjectId.isLoading ? "Updating..." : "Update"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default function SupportPage() {
  return (
    <div className="flex flex-col gap-4 overflow-auto p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-semibold">Support Page</h1>
        <UpdateRedmineProjectIdModal />
      </div>
      <h2>Support Tickets</h2>
      <OpenTickets />
    </div>
  );
}
