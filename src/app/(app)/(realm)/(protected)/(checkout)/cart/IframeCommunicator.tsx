import { useEffect } from 'react';

const IframeCommunicator = () => {
  useEffect(() => {
    const callParentFunction = (str: string) => {
      if (str && window.parent && window.parent.parent && window.parent.parent.postMessage) {
        window.parent.parent.postMessage(str, '*');
      }
    };

    const receiveMessage = (event: MessageEvent) => {
      if (event && event.data) {
        callParentFunction(event.data);
      }
    };

    window.addEventListener("message", receiveMessage, false);

    return () => {
      window.removeEventListener("message", receiveMessage, false);
    };
  }, []);

  return <div></div>;
};

export default IframeCommunicator;
