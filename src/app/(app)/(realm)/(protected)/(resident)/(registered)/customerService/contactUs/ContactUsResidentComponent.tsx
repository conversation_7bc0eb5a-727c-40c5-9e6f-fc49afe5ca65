"use client";
import React from "react";
import { use<PERSON><PERSON>, Controller, Control } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useContactStaff } from "@/hooks/api/useContact";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { Label } from "@/components/ui/label";
import { useGetTenantInformation } from "@/hooks/api/useSupport";
import { Skeleton } from "@/components/ui/skeleton";

export default function ContactUsResidentComponent() {
  const { data, isLoading } = useGetTenantInformation();

  console.log(data)
  const contactStaff = useContactStaff();
  const [_, setToast] = useAtom(toastAtom);
  const {
    control,
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      issue: "",
      description: "",
    },
  });

  const onSubmit = (data: any) => {
    contactStaff.mutate(data, {
      onSuccess: () => {
        setToast({
          message: "Ticket submitted successfully!",
          label: "Message Sent",
          status: "success",
        });
        reset();
      },
      onError: (error) => {
        console.log(error);
        setToast({
          label: "Error Submitting Ticket",
          message: "Unable to submit ticket. Please try again",
          status: "error",
        });
      },
    });
  };

  // Skeleton loading component
  const FormSkeleton = () => (
    <div className="mb-20 flex max-w-md flex-col gap-4 md:mb-6 lg:basis-1/2">
      <Skeleton className="mb-2 h-8 w-32" />

      <div>
        <Skeleton className="mb-2 h-5 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      <div>
        <Skeleton className="mb-2 h-5 w-24" />
        <Skeleton className="h-32 w-full" />
      </div>

      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-10 w-28" />
    </div>
  );

  // Image skeleton
  const ImageSkeleton = () => (
    <div className="hidden lg:block lg:basis-1/2">
      <Skeleton className="h-[500px] w-[500px]" />
    </div>
  );

  return (
    <div className="flex h-full flex-col overflow-auto bg-white">
      <div className="mb-16 bg-blue-400/10 px-6 py-10 text-center">
        <h1 className="mb-2 text-4xl font-bold text-slate-800">Contact Us</h1>
        <p className="text-center text-slate-700">
          Questions? Comments? Concerns? We&apos;re here to help.
        </p>
      </div>
      <div className="container flex max-w-4xl gap-16 px-6">
        {isLoading ? (
          <>
            <FormSkeleton />
            <ImageSkeleton />
          </>
        ) : (
          <>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="mb-20 flex max-w-md flex-col gap-4 md:mb-6 lg:basis-1/2"
            >
              <h2 className="mb-2 text-2xl">Submit a Ticket</h2>

              {/* Issue Category */}
              <IssueDropdown control={control} />

              {/* Description */}
              <div>
                <Label className="font-semibold" htmlFor="description">
                  Description:
                </Label>
                <Textarea
                  {...register("description", {
                    required: "Description is required",
                  })}
                />
                {errors.description && (
                  <p className="text-red-500">{errors.description.message}</p>
                )}
              </div>

              {/* Response Time */}
              <p className="text-slate-700">
                We will respond to your request within 1-2 business days.  For assistance, please contact the {data?.cityClerkOfficeName ?? "Clerk's"} Office.
              </p>

              {/* Submit */}
              <Button type="submit">Submit Ticket</Button>
            </form>

            {/* Other Container */}
            <div className="hidden lg:block lg:basis-1/2">
              <Image
                src="/images/support.png"
                width={500}
                height={500}
                layout="responsive"
                objectFit="contain"
                alt="Contact Us"
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
}

const IssueDropdown = ({
  control,
}: {
  control: Control<{ issue: string; description: string }>;
}) => {
  return (
    <div>
      <Label className="font-semibold" htmlFor="issue">
        Issue:
      </Label>
      <Controller
        name="issue"
        control={control}
        render={({ field }) => (
          <Select
            onValueChange={(value) => field.onChange(value)}
            value={field.value}
            aria-labelledby="issue-label"
          >
            <SelectTrigger className="">
              <SelectValue placeholder="Select Issue" />
            </SelectTrigger>
            <SelectContent>
              {/* Account & Documents Group */}
              <SelectGroup>
                <SelectLabel>Account & Documentation</SelectLabel>
                <SelectItem value="Account Access Problems">
                  Account Access Problems
                </SelectItem>
                <SelectItem value="Document Request Assistance">
                  Document Request Assistance
                </SelectItem>
                <SelectItem value="Payment Issues">Payment Issues</SelectItem>
              </SelectGroup>

              {/* Animal Services Group */}
              <SelectGroup>
                <SelectLabel>Animal Services</SelectLabel>
                <SelectItem value="Dog Registration Queries">
                  Dog Registration Queries
                </SelectItem>
                <SelectItem value="Reporting Lost Animals">
                  Reporting Lost Animals
                </SelectItem>
              </SelectGroup>

              {/* Technical & Feedback Group */}
              <SelectGroup>
                <SelectLabel>Technical & Feedback</SelectLabel>
                <SelectItem value="Technical Glitches">
                  Technical Glitches
                </SelectItem>
                <SelectItem value="Feedback and Suggestions">
                  Feedback and Suggestions
                </SelectItem>
              </SelectGroup>

              {/* Other */}
              <SelectGroup>
                <SelectLabel>Other</SelectLabel>
                <SelectItem value="Other">Other Inquiries</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        )}
      />
    </div>
  );
};
