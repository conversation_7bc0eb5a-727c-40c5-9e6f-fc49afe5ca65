"use client";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import BreadCrumb from "@/components/ui/breadcrumbs/BreadCrumb";
import { useGetPayeeInfo, useSubmitOrder } from "@/hooks/api/useCart";
import { useMyCart } from "@/hooks/useMyCart";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import CartForm from "../../../(checkout)/cart/CartForm";
import PaymentForm from "../../../(checkout)/cart/PaymentForm";
import CartSummary from "../../../(checkout)/cart/CartSummary";

type Order = {
  cartId: string;
  items: any;
  payee: any;
  paymentProvider?: string;
  paymentType: string;
  paymentReference: string;
  transactonDate?: string | Date;
  status?: string;
  amount: number;
  orderId?: string;
  adjustmentReason?: string;
  adjustedAmount?: number;
};

export default function ResidentPage({
  params: { realm },
}: {
  params: { realm: string };
}) {
  const { push } = useRouter();
  const [total, setTotal] = useState(0);
  const [processingPayment, setProcessingPayment] = useState(false);
  const { cartSummary } = useMyCart();

  const cartId = cartSummary?.cartId ?? null;
  const { data } = useGetPayeeInfo(cartId as string);

  const methods = useForm({
    mode: "onChange",
  });
  const { setValue } = methods;

  useEffect(() => {
    if (data) {
      for (const key in data) {
        setValue(key, data[key]);
      }
    }
  }, [data, setValue]);

  const submitOrderMutation = useSubmitOrder();

  const LoadingModal = () => {
    return (
      <div className="fixed left-1/2 top-1/2 w-auto max-w-full -translate-x-1/2 -translate-y-1/2 rounded border border-black bg-white p-4 text-center shadow-xl">
        <h2 className="text-4xl font-bold">Loading...</h2>
        <div className="flex w-full justify-center text-center">
          <LoadingSpinner />
        </div>
        <p>Processing Payment please wait</p>
      </div>
    );
  };

  const onSubmit = async (data: any) => {
    setProcessingPayment(true);
    const {
      paymentMethod,
      paymentReference,
      adjustmentReason,
      adjustedAmount,
      ...restOfData
    } = data;
    const cartItems = cartSummary?.items.map((item: any) => {
      return {
        cartItemId: item.cartItemId,
        itemId: item.itemId,
        itemType: item.itemType,
      };
    });

    let order: Order = {
      cartId: cartSummary?.cartId as string,
      amount: total,
      paymentType: paymentMethod,
      paymentReference,
      adjustmentReason,
      adjustedAmount,
      payee: restOfData,
      items: cartItems,
    };

    console.log(order);

    submitOrderMutation.mutate(order, {
      onSuccess: (data: any) => {
        const balance = data.balance;
        if (balance > 0) {
          console.log("balance is greater than 0");
        } else {
          push(`/myCart/orderSuccess?orderId=${data.orderId}`);
        }
        setProcessingPayment(false);
      },
      onError: (error) => {
        push(
          `/myCart?error=${error.response.data.message}&status=${error.response.status.toString()}`,
        );
        setProcessingPayment(false);
      },
    });
  };

  const { handleSubmit } = methods;

  if (processingPayment) return <LoadingModal />;

  return (
    <div className="mx-auto flex h-full w-full flex-col gap-6 overflow-auto bg-neutral-100 p-6">
      <div className="container mx-auto">
        <BreadCrumb />
        <FormProvider {...methods}>
          <form
            className="flex flex-col gap-10 xl:flex-row "
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="flex flex-col gap-10">
              <CartForm />
              <PaymentForm />
            </div>
            <CartSummary cart={cartSummary as any} loading={processingPayment} />
          </form>
        </FormProvider>
      </div>
    </div>
  );
}
