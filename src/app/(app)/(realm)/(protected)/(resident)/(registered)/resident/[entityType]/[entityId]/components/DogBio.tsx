import React, { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { FiEdit } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import { useUpdateResidentDogProfile } from "@/hooks/api/useProfiles";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";

export default function DogBio({
  entityId,
  bio,
}: {
  entityId: string;
  bio: string;
}) {
  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [bioText, setBioText] = useState<string>(bio);
  const [_, setToast] = useAtom(toastAtom);

  const bioMutation = useUpdateResidentDogProfile();
  const queryClient = useQueryClient();

  // Create body
  const createBody = (bio: string) => {
    const fd = new FormData();
    fd.append("dogBio", bio);
    return fd;
  };

  return (
    <div>
      <div className="flex items-center gap-2 text-neutral-700">
        <h2 className="text-xl font-medium">About Me</h2>
        <button
          className="translate-y-0.5 hover:text-blue-600"
          onClick={() => setCanEdit(!canEdit)}
        >
          <FiEdit />
        </button>
      </div>
      <div className="flex max-w-lg flex-col gap-2">
        {canEdit ? (
          <Textarea
            value={bioText}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
              setBioText(e.target.value);
            }}
            maxLength={2000}
            className="w-full"
          />
        ) : (
          <p>{bioText?.length ? bioText : "No Bio Available"}</p>
        )}
        {canEdit && (
          <div className="flex items-center justify-start gap-2">
            <Button
              variant="primary"
              size="sm"
              onClick={() => {
                bioMutation.mutate(
                  {
                    entityId,
                    body: createBody(bioText),
                  },
                  {
                    onSuccess: () => {
                      setCanEdit(false);
                      setToast({ label: "Bio Updated", status: "success" });
                      queryClient.invalidateQueries(["profile2"]);
                    },
                    onError: () => {
                      setToast({
                        label: "Error Updating Bio",
                        status: "error",
                      });
                    },
                  },
                );
              }}
            >
              Save
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setCanEdit(false);
                setBioText(bio);
              }}
            >
              Cancel
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
