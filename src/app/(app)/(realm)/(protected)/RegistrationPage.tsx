import Machine from "@/components/formBuilderV2/components/Machine";
import { useGetJSONStorage } from "@/hooks/api/useAdmin";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { useSearchParams } from "next/navigation";
import Loading from "@/app/(app)/loading";

const RegistrationPage = () => {
  const { profile, profileIsFetching, hasPermissions } = useMyProfile();

  const form = hasPermissions(["super-admin"])
    ? "new-admin-registration"
    : "new-resident-registration";

  const { data: jsonListData, isLoading: jsonListIsLoading } =
    useGetJSONStorage("onlineForm", form);

  const registrationCode = useSearchParams().get("registrationCode") || null;

  const primaryEmail = profile?.individual?.contacts.find(
    (contact: any) => contact.type === "Email" && contact.group === "Primary",
  );

  const primaryPhone = profile?.individual?.contacts.find(
    (contact: any) => contact.type === "Phone" && contact.group === "Home",
  );

  const additionalContext = {
    entityType: "individual",
    entityId: profile.individual.entityId,
    registrationCode: registrationCode?.length ? registrationCode : null,
    hasRegistrationCode: registrationCode ? "yes" : null,
    main: {
      phone: primaryPhone?.value || null,
      email: primaryEmail?.value || null,
    },
  };

  if (jsonListIsLoading || profileIsFetching)
    return <Loading text={"Loading Form..."} />;
  if (jsonListData) {
    return (
      <Machine config={jsonListData} additionalContext={additionalContext} />
    );
  }

  return null;
};

export default RegistrationPage;
