"use client";
import {
  ApprovalContainer,
  ContentContainer,
} from "@/components/approval/components/Approval";

import {
  ApprovalSidebarContainer,
  SidebarTitle,
} from "@/components/approval/components/ApprovalSidebar";
import {
  ApprovalProvider,
  useMergeApprovalContext,
} from "@/components/approval/hooks/useMergeApproval";
import { Button } from "@/components/ui/button";
import { MergeApprovalSidebar } from "./MergeApprovalSidebar";
import MergeAccountContent from "./MergeAccountContent";
import { useParams, useRouter, useSearchParams } from "next/navigation";

export default function LicenseApprovalPage() {
  return (
    <ApprovalContainer>
      <ApprovalProvider>
        <ApprovalSidebarContainer>
          <SidebarTitle title="Merge Accounts" />
          <div className="flex h-full flex-col gap-2 overflow-auto">
            <MergeApprovalSidebar />
          </div>
        </ApprovalSidebarContainer>
        <ContentContainer>
          <RenderContent />
        </ContentContainer>
      </ApprovalProvider>
    </ApprovalContainer>
  );
}

const MergeApprovalBar = () => {
  const { mergeApprovalList } = useMergeApprovalContext();
  const { replace } = useRouter();
  const residentId = useSearchParams().get("requestedUserId") as string;


  const nextResident = () => {
    const currentResidentIndex = mergeApprovalList.findIndex(
      (item: any) => item.entityId === residentId,
    );

    console.log(currentResidentIndex)

    const nextResidentIndex = currentResidentIndex + 1;

    if (nextResidentIndex >= mergeApprovalList.length) {
      return null;
    }

    return mergeApprovalList[nextResidentIndex].entityId;
  };

  const prevResident = () => {
    const currentResidentIndex = mergeApprovalList.findIndex(
      (item: any) => item.entityId === residentId,
    );

    const prevResidentIndex = currentResidentIndex - 1;

    if (prevResidentIndex < 0) {
      return null;
    }

    return mergeApprovalList[prevResidentIndex].entityId;
  };

  const nextResidentId = nextResident();
  const prevResidentId = prevResident();

  const url = `/approval/accounts/merge`;

  return (
    <div className="flex items-center justify-between gap-4 bg-white px-1 py-1 rounded shadow-lg">
      <Button
        variant="default"
        disabled={!prevResidentId}
        onClick={() => {
          if (prevResidentId) {
            replace(
              `${url}?requestedUserId=${prevResidentId}`,
            );
          }
        }}
      >
        Prev Resident
      </Button>
      <div className="flex items-center justify-center gap-4">
        {/* <MergeDeniedModal />
        <MergeApprovalModal /> */}
      </div>
      <Button
        variant="default"
        disabled={!nextResidentId}
        onClick={() => {
          if (nextResidentId) {
            replace(
              `${url}?requestedUserId=${nextResidentId}`,
            );
          }
        }}
      >
        Next Resident
      </Button>
    </div>
  );
};

const RenderContent = () => {
  const { replace } = useRouter();
  const residentId = useSearchParams().get("requestedUserId") as string;
  const url = `/approval/accounts/merge`
  const { mergeApprovalList, loadingMergeList, errorMergeList } =
    useMergeApprovalContext();

  if (mergeApprovalList.length === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <p>No merge requests</p>
      </div>
    );
  }

  if (loadingMergeList) return <p>Loading...</p>;
  if (errorMergeList) return <p>Error loading merge list</p>;

  if (!residentId)
    replace(
      `${url}?requestedUserId=${mergeApprovalList[0].entityId}`,
    );

  return (
    <>
      <MergeAccountContent />
      <MergeApprovalBar />
    </>
  );
};
