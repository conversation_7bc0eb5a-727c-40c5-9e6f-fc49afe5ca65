import Image from "next/image";
import { cn } from "@/lib/utils";
import { useCallback, useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { License } from "@/types/LicenseType";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";
import { Document } from "@/types/DocumentType";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import { FaArrowRotateLeft, FaArrowRotateRight } from "react-icons/fa6";
import { useGetDocumentBlob } from "@/hooks/api/useServices";
import { FiGrid, FiList } from "react-icons/fi";
import { Individual } from "@/types/IndividualType";
import { useCurrentLicenseContext } from "@/components/approval/hooks/useDogLicenseApproval";
import { isSenior } from "@/components/license/licenseHelper";
import ProfileBuilderFactory from "@/components/builders/profileBuilder/ProfileBuilderFactory";
import ApprovalEntityHeader from "./ApprovalEntityHeader";

export const OwnersContentApproval = ({
  owners,
  license,
}: {
  owners: Individual[];
  license: License;
}) => {
  console.log(owners);

  const [currentOwner, setCurrentOwner] = useState<Individual | null>(
    owners?.[0] || null,
  );

  useEffect(() => {
    setCurrentOwner(owners?.[0] ?? null);
  }, [owners]);

  const containerVariants = {
    hidden: { opacity: 0, y: -50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 100 },
    },
    exit: { opacity: 0, y: 50, transition: { duration: 0.2 } },
  };

  if (!owners) return null;

  return (
    <>
      {(owners.length > 1 ||
        license?.licenseType?.code === "purebredDogLicense") && (
        <div className="flex w-[300px] flex-col gap-2 rounded bg-white p-2 shadow">
          {owners.map((owner: Individual) => (
            <OwnerTab
              owner={owner}
              key={owner.entityId}
              currentOwnerEntityId={currentOwner?.entityId || ""}
              setCurrentOwner={setCurrentOwner}
            />
          ))}
        </div>
      )}
      <AnimatePresence mode="wait">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="w-full overflow-hidden rounded bg-white shadow"
        >
          {currentOwner && <OwnerContent owner={currentOwner} />}
        </motion.div>
      </AnimatePresence>
    </>
  );
};

export const OwnerTab = ({
  owner,
  currentOwnerEntityId,
  setCurrentOwner,
}: {
  owner: Individual;
  currentOwnerEntityId: string;
  setCurrentOwner: (owner: Individual) => void;
}) => {
  const active = currentOwnerEntityId === owner?.entityId;
  return (
    <button
      className={cn(
        "flex w-full gap-2 rounded p-2 transition-all hover:bg-blue-100",
        active && "bg-blue-50",
      )}
      key={owner?.entityId}
      onClick={() => setCurrentOwner(owner)}
    >
      <AvatarImage
        entityType="individual"
        src={getAvatarBlob(owner?.documents) as string}
        alt="owner"
        width={active ? 40 : 40}
        height={active ? 40 : 40}
        className={cn(
          "h-fit rounded object-cover object-center shadow",
          active && "rounded-lg",
        )}
      />
      <div className={cn("flex flex-col text-left", active && "text-left")}>
        <div className={cn("", active && "font-medium ")}>
          {owner.firstName}
        </div>
        <small className="text-neutral-600">{}</small>
      </div>
    </button>
  );
};

const OwnerContent = ({ owner }: { owner: Individual }) => {
  const { refetchCurrentLicense, isRefetchingCurrentLicense } =
    useCurrentLicenseContext();
  const [isThumbnail, setIsThumbnail] = useState<boolean>(true);

  return (
    <div className="flex h-full w-full gap-2 overflow-hidden ">
      <div className="flex h-full w-full flex-col gap-4 overflow-auto p-4">
        {/* <OwnerHeader owner={owner} /> */}
        <ApprovalEntityHeader
          entity={owner}
          entityId={owner.entityId}
          entityType="individual"
        />
        <ProfileBuilderFactory
          entity={owner}
          profileType="individual"
          entityIsFetching={isRefetchingCurrentLicense}
          entityRefetch={refetchCurrentLicense}
        />
      </div>
      <div className="flex h-full w-full flex-col overflow-auto p-4">
        <div className="mb-2 flex justify-between text-xl font-semibold text-neutral-600">
          Documents
          <div className="flex gap-2 px-4">
            <button
              className={cn(
                "flex size-8 items-center justify-center rounded text-lg",
                isThumbnail && "bg-blue-500 text-white",
              )}
              onClick={() => setIsThumbnail(true)}
            >
              <FiGrid />
            </button>
            <button
              className={cn(
                "flex size-8 items-center justify-center rounded text-lg",
                !isThumbnail && "bg-blue-500 text-white",
              )}
              onClick={() => setIsThumbnail(false)}
            >
              <FiList />
            </button>
          </div>
        </div>
        <div
          className={cn(
            "flex w-full flex-col gap-4",
            isThumbnail && "flex-row flex-wrap",
          )}
        >
          {owner?.documents && owner.documents.length > 0 ? (
            owner.documents.map((doc: Document) => (
              <FileModal
                file={doc}
                thumbnails={isThumbnail}
                key={doc.entityId}
              />
            ))
          ) : (
            <div>No documents found</div>
          )}
        </div>
      </div>
    </div>
  );
};

const FileModal = ({
  file,
  thumbnails,
}: {
  file: {
    documentUuid: string;
    name: string;
  };
  thumbnails: boolean;
}) => {
  const { data, isLoading, isError } = useGetDocumentBlob(file.documentUuid);

  const [zoomLevel, setZoomLevel] = useState<number>(100);
  const [rotate, setRotate] = useState<number>(0);

  const maxZoom = 200;
  const minZoom = 50;

  const zoomOut = () => setZoomLevel((prev) => Math.max(prev - 5, minZoom));
  const zoomIn = () => setZoomLevel((prev) => Math.min(prev + 5, maxZoom));
  const rotateRight = () => setRotate((prev) => prev + 90);
  const rotateLeft = () => setRotate((prev) => prev - 90);

  const handleZoomChange = useCallback((value: number[]) => {
    setZoomLevel(value[0]);
  }, []);

  const scale = zoomLevel / 100;

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error</div>;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div
          className={cn(
            "w-auto rounded bg-neutral-200 shadow",
            thumbnails && "w-60 shrink-0",
          )}
        >
          <button
            className={cn(
              "relative aspect-square w-full cursor-pointer rounded border p-1",
              thumbnails && "w-60 shrink-0",
            )}
          >
            <Image
              src={URL.createObjectURL(data)}
              alt="Document image"
              fill
              className={cn(
                "object-contain object-center",
                thumbnails && "rounded object-cover",
              )}
            />
          </button>
          <div className="px-2 py-1 text-lg">{file.name}</div>
        </div>
      </DialogTrigger>
      <DialogContent className="h-full max-w-6xl">
        <div className="flex h-full flex-col overflow-hidden">
          <motion.div
            drag
            dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
            className="relative h-full w-full cursor-grab overflow-hidden"
            style={{ touchAction: "none" }}
          >
            <div
              className="relative h-full w-full"
              style={{
                transform: `scale(${scale})`,
                transformOrigin: "center",
              }}
            >
              <Image
                src={URL.createObjectURL(data)}
                alt="Document image"
                fill
                className="object-contain object-center"
                style={{ transform: `rotate(${rotate}deg)` }}
              />
            </div>
          </motion.div>
          <div className="flex items-center justify-center gap-4 p-4">
            <Button onClick={rotateLeft}>
              <FaArrowRotateLeft />
            </Button>
            <Button onClick={zoomOut}>-</Button>
            <Slider
              className="slider-container"
              min={minZoom}
              max={maxZoom}
              step={1}
              value={[zoomLevel]}
              onValueChange={handleZoomChange}
            />
            <Button onClick={zoomIn}>+</Button>
            <Button onClick={rotateRight}>
              <FaArrowRotateRight />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
