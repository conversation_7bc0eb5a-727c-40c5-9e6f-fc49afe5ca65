import { useState } from "react";
import { AiFillPlusCircle } from "react-icons/ai";
import { FiMoreVertical } from "react-icons/fi";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toastAtom } from "@/components/ui/toast/toast";
import { useAtom } from "jotai";
import { entityDisplay } from "./entityHelper";
import { BiEnvelope, BiPhone } from "react-icons/bi";
import AvatarImage from "@/components/AvatarImage";

type SearchResultProps = {
  result: any;
};

const AffiliationResult = ({ result }: SearchResultProps) => {

  const [showIcons, setShowIcons] = useState<boolean>(false);
  const [, setToast] = useAtom(toastAtom);

  
  const router = useRouter();

  if (!result) return null;

  const checkResult = {
    [result.entityType]: result,
  };

  console.log(checkResult);

  console.log(result);
  const ent: any = entityDisplay(checkResult, result.entityType);

  console.log(ent);

  type Icon = {
    [key: string]: string;
  };

  const icons: Icon = {
    individual: "/images/icons/user.png",
    dog: "/images/icons/dog.png",
    company: "/images/icons/organization.png",
    address: "/images/icons/address.png",
  };

  const containsType = (type: string) =>
    result?.thirdDisplay?.some((item: any) => item.type === type);

  if (containsType("draft")) return null;

  return (
    <div
      className={`flex cursor-pointer flex-col items-center gap-2 rounded border hover:bg-gradient-to-br hover:from-blue-100 hover:to-blue-50`}
    >
      <button
        className="flex w-full items-center gap-2 p-2 text-left"
        onClick={() => {
          if (containsType("draft")) {
            setToast({
              status: "error",
              message: "Can not view draft entity, must edit first.",
            });
          } else {
            router.push(
              `/profile/${result.entityType}/${result.entityId}?tab=profile`,
            );
          }
        }}
      >
        <div className="h-[70] w-[70] rounded-full bg-neutral-200"></div>
        <AvatarImage
          entityType={result.entityType}
          src={ent?.avatarUrl}
          alt="Picture of the user"
          className="mr-6 rounded"
          width={48}
          height={48}
        />
        {/* Information */}
        <div className="w-full">
          <p className="text-lg font-bold">{ent?.primaryDisplay}</p>
          <p className="text-sm text-neutral-500">{ent?.secondaryDisplay}</p>
          {ent?.contacts && (
            <div className="flex flex-wrap items-center gap-4">
              <p className="flex items-center text-sm text-neutral-600">
                <BiPhone className="mr-1 inline-block" />
                {ent?.contacts?.phone}
              </p>
              <p className="flex items-center text-sm text-neutral-600">
                <BiEnvelope className="mr-1 inline-block" />
                {ent?.contacts?.email}
              </p>
            </div>
          )}
          <p className="text-sm text-red-600">{ent?.warningDisplay ?? ""}</p>
        </div>

        <div className="relative h-auto w-auto xl:hidden">
          <button
            className="relative flex h-8 w-8 items-center justify-center rounded border hover:bg-blue-200"
            onClick={(e) => {
              e.stopPropagation();
              setShowIcons(!showIcons);
            }}
          >
            <FiMoreVertical className="text-2xl" />
            {showIcons && <Icons entityId={result.entityId} />}
          </button>
        </div>

        {/* <div className="hidden xl:flex">
          <Icons entityId={result.entityId} />
        </div> */}
      </button>
    </div>
  );
};

export default AffiliationResult;

const Icons = ({ entityId }: { entityId: string; }) => {
  
  console.log(entityId);
  return (
    <div
      className="
        absolute bottom-10 right-0 z-10 ml-auto mt-2 flex flex-nowrap items-center gap-2 rounded border bg-white p-2 text-2xl shadow-lg
        xl:relative xl:bottom-0 xl:mt-0  xl:border-none xl:bg-transparent xl:shadow-none
      "
    >
      {[
        {
          link: `/license/dogLicenses/create?individualId=${entityId}&returnTo=/dogLicenses/search`,
          icon: <AiFillPlusCircle />,
          text: "License",
          color: "hover:bg-green-500",
          notification: null,
        },
        // {
        //   link: ``,
        //   icon: <AiFillBell />,
        //   text: "Alerts",
        //   color: "hover:bg-yellow-500",
        //   notification: null,
        // },
        // {
        //   link: ``,
        //   icon: <MdFlag />,
        //   text: "Report",
        //   color: "hover:bg-red-500",
        //   notification: null,
        // },`
      ].map((item, index) => {
        return (
          <Link
            href={item.link}
            key={index}
            className={`
                rounded-lg p-2 text-xl text-neutral-600 hover:text-white
                ${item.color}
              `}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex flex-col items-center justify-center  gap-2">
              {item.icon} <span className="shrink-0 text-xs">{item.text}</span>
            </div>
          </Link>
        );
      })}
    </div>
  );
};
