import { Title } from "@/components/profile/ProfileComponents";
import { useEntity } from "@/hooks/providers/useEntity";
import { useMyPermissions } from "@/hooks/useMyPermissions";
import MergeAccount from "./accountManagement/MergeAccount";
import DisableAccount from "./accountManagement/DisableAccount2";
import LostDog from "./accountManagement/LostDog";
import ImpoundedDog from "./accountManagement/ImpoundedDog";


export default function AccountManagement() {
  const { hasPermissions } = useMyPermissions();
  const { entityType, entity } = useEntity();



  const hasEntityType = (allowedEntityTypes: string[]) => {
    return allowedEntityTypes.includes(entityType);
  }

  const canEdit = hasPermissions(["super-admin"]);

  const isActive = entity[entityType]?.active ?? false;
  console.log(isActive)
  console.log(canEdit)

  if(!canEdit && !isActive) {
    return (
      <div className="flex flex-col gap-2">
        <Title>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span>Account Management</span>
            </div>
          </div>
        </Title>
        <p className="text-muted-foreground">This account is not active. If this is a mistake or you need to make changes please contact your local clerk&apos;s office.</p>
      </div>
    )
  }

  return (
    <div>
      <Title>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span>Account Management</span>
          </div>
        </div>
      </Title>
      <div className="flex flex-col gap-6">
        {hasEntityType(["individual"]) && canEdit && <MergeAccount />}
        {hasEntityType(["individual", "dog"]) && <DisableAccount />}
        {hasEntityType(["dog"]) && <LostDog />}
        {hasEntityType(["dog"]) && canEdit && <ImpoundedDog />}
      </div>
    </div>
  );
}


