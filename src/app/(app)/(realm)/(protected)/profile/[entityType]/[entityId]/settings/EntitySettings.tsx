"use client";
import { use<PERSON><PERSON> } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import { useDisableResidentProfile } from "@/hooks/api/useProfiles";
import PageContainer from "@/components/ui/Page/PageContainer";
import { motion } from "framer-motion";
import { Title } from "@/components/profile/ProfileComponents";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useState } from "react";
import { useEntity } from "@/hooks/providers/useEntity";
import AccountManagement from "./AccountManagement";
import { useMyPermissions } from "@/hooks/useMyPermissions";
import DocumentManagement from "./DocumentManagement";

export type OptInStateType = {
  [key: string]: boolean;
}[];

export default function EntitySettings() {
  const { entityId, entityType } = useEntity();
  const { hasPermissions } = useMyPermissions();

  return (
    <motion.div
      initial={{ opacity: 0, x: -100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="lg:mx-auto flex flex-col gap-10 lg:container w-full p-3 lg:p-0"
    >
      <PageContainer>
        <div className="flex flex-col gap-10 xl:flex-row">
          <div className="flex w-full flex-col gap-7">
            <div className="flex w-full flex-col gap-20">
              {/* Document Management */}
              {entityType === "individual" && <DocumentManagement />}
              <AccountManagement />

              {/* Security */}
              {/* TODO: Add back later --Sean B */}
              {/* <div>
                <Title>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span>Account Security</span>
                    </div>
                  </div>
                </Title>

                <div className="flex flex-col gap-4">
                  <Section label={`Status (${currentEntity?.status})`}>
                    <Switch
                      checked={entityStatus}
                      onCheckedChange={(value) => {
                        setEntityStatus(value);
                        updateAccountStatus.mutate(entityStatus, {
                          onSuccess: () => {
                            queryClient.invalidateQueries();
                            setOpen(false);
                            setToast({
                              status: "success",
                              label: "Profile Updated",
                              message: "Successfully Updated Profile",
                            });
                          },
                          onError: (error: any) => {
                            console.log(error);
                            setToast({
                              status: "error",
                              label: "Error Disabling Account",
                              message: error?.response?.data?.message,
                            });
                            setEntityStatus(!value);
                          },
                        });
                      }}
                    />
                    {updateAccountStatus.isLoading && (
                      <span className="animate-pulse">Saving...</span>
                    )}
                  </Section>
                </div>
              </div> */}

              {hasPermissions(["super-admin"]) && (
                <PermanentSettings
                  entityId={entityId}
                  entityType={entityType}
                />
              )}
            </div>
          </div>
        </div>
      </PageContainer>
    </motion.div>
  );
}

const PermanentSettings = ({
  entityId,
  entityType,
}: {
  entityId: string | undefined;
  entityType: string | undefined;
}) => {
  const [open, setOpen] = useState(false);
  const clerkDisableResidentOnlineAccount = useDisableResidentProfile();
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  if (!entityId) return null;

  return (
    <div className="rounded-lg border-2 border-red-600 bg-red-50/60 p-4">
      <Title>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span>Permanent Settings</span>
          </div>
        </div>
      </Title>

      {entityType === "individual" && (
        <div className="flex flex-col gap-4">
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button
                className="w-fit hover:bg-red-400"
                size="sm"
                variant="destructive"
                onClick={() => setOpen(true)}
              >
                Delete Online Account
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Are you absolutely sure?</DialogTitle>
                <DialogDescription>
                  This will delete this user&apos;s online account.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    console.log("Disabling Account");
                    clerkDisableResidentOnlineAccount.mutate(
                      {
                        entityId: entityId as string,
                      },
                      {
                        onSuccess: () => {
                          queryClient.invalidateQueries();
                          setToast({
                            status: "success",
                            label: "Account Disabled",
                            message: "Successfully Disabled Account",
                          });
                          setOpen(false);
                        },
                        onError: (error: any) => {
                          console.log(error);
                          setToast({
                            status: "error",
                            label: "Error Disabling Account",
                            message: error?.response?.data?.message,
                          });
                        },
                      },
                    );
                  }}
                  className="hover:bg-red-400"
                >
                  Continue
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      )}
    </div>
  );
};
