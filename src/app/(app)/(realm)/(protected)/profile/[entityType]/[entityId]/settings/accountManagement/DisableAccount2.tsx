import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toastAtom } from "@/components/ui/toast/toast";
import { useHandleEvent } from "@/hooks/api/useProfiles";
import { useEntity } from "@/hooks/providers/useEntity";
import { useAtom } from "jotai";
import { useEffect, useState } from "react";
import { CgSpinner } from "react-icons/cg";

const DisableAccount = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [eventType, setEventType] = useState<string | null>(null);
  const mutateEvent = useHandleEvent();
  const [_, setToast] = useAtom(toastAtom);
  const { entityId, entityType, entity, entityRefetch, entityIsFetching } =
    useEntity();

  const currentEntity = entity[entityType];
  const isActive = currentEntity?.active || false;

  const [isSwitchOn, setIsSwitchOn] = useState<boolean>(!isActive);

  useEffect(() => {
    setIsSwitchOn(!isActive);
  }, [isActive]);

  const handleSwitchChange = (checked: boolean) => {
    if (checked) {
      setOpen(true);
    } else {
      reactivateAccount();
    }
  };

  const reactivateAccount = () => {
    const enableKey = Object.keys(options[entityType].enable).find(
      (key) => options[entityType].enable[key] === "Reactivate",
    ) as string;

    mutateEvent.mutate(
      {
        entityId,
        eventType: enableKey,
        entityType,
        body: new FormData(),
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Account Restored",
            message: "Successfully Restored Account",
          });
          entityRefetch();
          setIsSwitchOn(true);
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Restoring Account",
            message: error.message,
          });
          setIsSwitchOn(false);
        },
      },
    );
  };

  const handleDialogClose = () => {
    setOpen(false);
    setIsSwitchOn(!isActive);
    setEventType(null);
  };

  const options: {
    [key: string]: {
      disable: {
        [key: string]: string;
      };
      enable: {
        [key: string]: string;
      };
    };
  } = {
    individual: {
      disable: {
        individualMovedOutsideJurisdiction: "Moved",
        individualDeceased: "Deceased",
      },
      enable: {
        individualReactivated: "Reactivate",
      },
    },
    dog: {
      disable: {
        dogDeceased: "Deceased",
        dogRelinquished: "Relinquished",
      },
      enable: {
        dogReactivated: "Reactivate",
      },
    },
  };

  const handleSelection = () => {
    if (eventType) {
      mutateEvent.mutate(
        {
          entityId,
          eventType,
          entityType,
          body: new FormData(),
        },
        {
          onSuccess: () => {
            setToast({
              status: "success",
              label: "Disable Account",
              message: "Successfully Disabled Account",
            });
            entityRefetch();
            handleDialogClose();
          },
          onError: (error: any) => {
            setToast({
              status: "error",
              label: "Error Disabling Account",
              message: error.message,
            });
          },
        },
      );
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <p className="w-[180px] font-semibold">Account Disabled</p>
        <Switch
          checked={isSwitchOn}
          onCheckedChange={handleSwitchChange}
          className=""
          disabled={entityIsFetching}
        />
        {entityIsFetching && (
          <>
            <CgSpinner className="animate-spin text-red-500" size={28} />
            <span className="animate-pulse">Loading...</span>
          </>
        )}
      </div>
      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            handleDialogClose();
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Disable Account</DialogTitle>
            <DialogDescription>
              <div className=" mt-6 mb-2">
                <div className="text-black">Reason:</div>
                <Select
                  onValueChange={(value) => {
                    setEventType(value);
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent className="z-[10000]">
                    <SelectGroup>
                      {Object.keys(options[entityType].disable).map((key) => (
                        <SelectItem key={key} value={key}>
                          {options[entityType].disable[key]}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              This will disable this {entityType}&apos;s online account.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleDialogClose}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleSelection}
              disabled={!eventType || entityIsFetching}
            >
              {entityIsFetching ? (
                <span className="animate-pulse">Disabling...</span>
              ) : (
                <span>Disable Account</span>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DisableAccount;
