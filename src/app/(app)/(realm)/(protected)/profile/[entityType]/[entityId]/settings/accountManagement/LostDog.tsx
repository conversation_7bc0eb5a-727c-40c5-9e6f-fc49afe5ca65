import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { toastAtom } from "@/components/ui/toast/toast";
import { useHandleEvent } from "@/hooks/api/useProfiles";
import { useEntity } from "@/hooks/providers/useEntity";
import { Dog } from "@/types/DogType";
import { useAtom } from "jotai";
import { useEffect, useState } from "react";
import { CgSpinner } from "react-icons/cg";

const LostDog = () => {
  const [open, setOpen] = useState<boolean>(false);
  const mutateEvent = useHandleEvent();
  const [_, setToast] = useAtom(toastAtom);
  const { entityId, entityType, entity, entityRefetch, entityIsFetching } =
    useEntity();

  const currentEntity = entity[entityType] as Dog;
  const isLost = currentEntity?.status === "Lost" || false;

  const [isSwitchOn, setIsSwitchOn] = useState<boolean>(isLost);

  useEffect(() => {
    setIsSwitchOn(isLost);
  }, [isLost]);

  const isActive = currentEntity?.active ?? false;


  const handleSwitchChange = (checked: boolean) => {
    if (!isActive) {
      console.log("Entity is not active, cannot change status.");
      return;
    }

    if (checked) {
      setOpen(true);
    } else {
      markDogFound();
    }
  };

  const markDogFound = () => {
    mutateEvent.mutate(
      {
        entityId,
        eventType: "dogFound",
        entityType,
        body: new FormData(),
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Dog Found",
            message: "Successfully marked dog as found",
          });
          entityRefetch();
          setIsSwitchOn(false);
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Updating Dog Status",
            message: error.message,
          });
          setIsSwitchOn(true);
        },
      },
    );
  };

  const handleMarkLost = () => {
    mutateEvent.mutate(
      {
        entityId,
        eventType: "dogLost",
        entityType,
        body: new FormData(),
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "Dog Marked as Lost",
            message: "Successfully marked dog as lost",
          });
          entityRefetch();
          handleDialogClose();
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error Updating Dog Status",
            message: error.message,
          });
        },
      },
    );
  };

  const handleDialogClose = () => {
    setOpen(false);
    setIsSwitchOn(isLost);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <p className="w-[180px] font-semibold">Lost Dog</p>
        <Switch
          checked={isSwitchOn}
          onCheckedChange={handleSwitchChange}
          className=""
          disabled={entityIsFetching || !isActive}
        />
        {!isActive && (
          <span className="text-sm text-red-500">
            This account is not active.
          </span>
        )}
        {entityIsFetching && (
          <>
            <CgSpinner className="animate-spin text-red-500" size={28} />
            <span className="animate-pulse">Loading...</span>
          </>
        )}
      </div>
      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            handleDialogClose();
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Action</DialogTitle>
            <DialogDescription>
              Are you sure you want to mark this dog as lost? This action will
              notify relevant parties and update the status in the system.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleDialogClose}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleMarkLost}
              disabled={entityIsFetching}
            >
              {entityIsFetching ? (
                <span className="animate-pulse">Processing...</span>
              ) : (
                <span>Confirm</span>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LostDog;
