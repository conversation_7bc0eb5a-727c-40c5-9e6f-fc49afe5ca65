"use client";
import Image from "next/image";
import Link from "next/link";
import EmailLoginButton from "@/components/login/EmailLoginButton";
import SingleSignOn from "@/components/login/SingleSignOn";
import LoginSignupContainer from "@/components/landingPage/LoginSignupContainer";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import Loading from "./loading";
import { useKeycloak } from "@/hooks/useKeycloak";
import { useMyPermissions } from "@/hooks/useMyPermissions";
import { TenantType, useGetAllTenants } from "@/hooks/api/useRealm";
const LoginSignup = ({
  returnTo,
  type,
}: {
  returnTo: string;
  type: "login" | "signup";
}) => {
  const { data, isError, isLoading } = useGetAllTenants();
  const tenant = useSearchParams().get("tenant");
  const [loading, setLoading] = useState(true);
  const { hasPermissions, permissions } = useMyPermissions();
  const { push } = useRouter();
  const { isLoggedIn } = useKeycloak();
  const [tenantLogo, setTenantLogo] = useState<string | null>(null);

  useEffect(() => {
    setLoading(false);
    if (isLoggedIn()) {
      if (returnTo) {
        return push(returnTo);
      }
      console.log(permissions)
      const path = `/${hasPermissions(["super-admin"]) ? "dashboard" : "home"}${window.location.search}`
      return push(path);
    } else {
      console.log("Not logged in");
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoggedIn]);

  useEffect(() => {
    if (data) {
      const tenantExists = data?.find(
        (t: TenantType) => t.name === (tenant as string),
      );
      console.log(tenantExists);
      setTenantLogo(tenantExists?.logo);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  if (loading || isLoading) return <Loading text={"Loading"} />;

  return (
    <LoginSignupContainer>
      <div className="flex w-full max-w-md flex-col items-center rounded-2xl bg-white/80 px-10 py-12 shadow-2xl backdrop-blur-md">
        <Image
          src={tenantLogo || "/logos/clerkxpressLogo.png"}
          width={120}
          height={120}
          alt={"Logo for City"}
          className="mb-8 bg-white"
        />
        <h1 className="mb-2 text-4xl font-bold text-gray-900">Welcome!</h1>
        {type === "login" ? (
          <p className="mb-8 max-w-md text-center text-base text-gray-500">
            For account access, kindly sign in below. New here? Click{" "}
            <Link
              href={!returnTo ? `/signup` : `/signup?returnTo=${returnTo}`}
              className="font-semibold text-blue-600 hover:text-blue-800"
            >
              Sign Up
            </Link>{" "}
            to establish your account.
          </p>
        ) : (
          <p className="mb-8 max-w-md text-center text-base text-gray-500">
            Ready to become a part of our community? Fill out your details below
            to create your account.
          </p>
        )}

        <EmailLoginButton type={type} />
        <div className="my-6 flex w-full items-center gap-4">
          <div className="h-[1px] w-full bg-gray-200"></div>
          <div className="text-sm font-medium text-gray-400">or</div>
          <div className="h-[1px] w-full bg-gray-200"></div>
        </div>
        <SingleSignOn type={type} />

        <div className="mt-12 flex w-full flex-col items-center gap-4">
          {type === "login" ? (
            <p className="text-center text-sm text-gray-600">
              Don&apos;t have an account yet?
              <Link
                href={!returnTo ? `/signup` : `/signup?returnTo=${returnTo}`}
                className="ml-2 font-semibold text-blue-600 underline underline-offset-2 transition-colors hover:text-blue-800"
              >
                Sign Up
              </Link>
            </p>
          ) : (
            <div className="flex flex-col items-center gap-2">
              <small className="max-w-md text-center text-xs text-gray-500">
                By creating an account you agree with our
                <Link
                  className="ml-1 text-neutral-800 underline underline-offset-2 hover:text-neutral-600"
                  href="/termsofservice"
                >
                  Terms of Service
                </Link>
                and
                <Link
                  className="ml-1 text-neutral-800 underline underline-offset-2 hover:text-neutral-600"
                  href="/privacy"
                >
                  Privacy Policy
                </Link>
              </small>
              <p className="text-center text-sm text-gray-600">
                Already have an account?
                <Link
                  href={`/login`}
                  className="ml-2 font-semibold text-blue-600 underline underline-offset-2 transition-colors hover:text-blue-800"
                >
                  Login
                </Link>
              </p>
            </div>
          )}
        </div>
      </div>
    </LoginSignupContainer>
  );
};

export default React.memo(LoginSignup);
