import type { NextApiRequest, NextApiResponse } from 'next'

export default function handler( req: NextApiRequest, res: NextApiResponse) {
  const search = [
    {
      title: "Owner",
      instructions: "",
      inputs: [
        {
          label: "First Name",
          fieldName: "firstName",
          inputType: "text",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Last Name",
          fieldName: "lastName",
          inputType: "text",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Email",
          fieldName: "email",
          inputType: "email",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Phone",
          fieldName: "phone",
          inputType: "tel",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Address",
          fieldName: "address",
          inputType: "text",
          defaultValue: "",
          group: 2,
        },
        {
          label: "Address 2",
          fieldName: "address2",
          inputType: "text",
          defaultValue: "",
          group: 2,
        },
        {
          label: "City",
          fieldName: "city",
          inputType: "text",
          defaultValue: "",
          group: 2,
        },
        {
          label: "State",
          fieldName: "state",
          inputType: "select",
          defaultValue: "",
          options: [
            { label: "Select a State", value: "" },
            { label: "Alabama", value: "AL" },
            { label: "Alaska", value: "AK" },
            { label: "Arizona", value: "AZ" },
            { label: "Arkansas", value: "AR" },
            { label: "California", value: "CA" },
            { label: "Colorado", value: "CO" },
            { label: "Connecticut", value: "CT" },
            { label: "Delaware", value: "DE" },
            { label: "District Of Columbia", value: "DC" },
            { label: "Florida", value: "FL" },
            { label: "Georgia", value: "GA" },
            { label: "Hawaii", value: "HI" },
            { label: "Idaho", value: "ID" },
            { label: "Illinois", value: "IL" },
            { label: "Indiana", value: "IN" },
            { label: "Iowa", value: "IA" },
            { label: "Kansas", value: "KS" },
            { label: "Kentucky", value: "KY" },
            { label: "Louisiana", value: "LA" },
            { label: "Maine", value: "ME" },
            { label: "Maryland", value: "MD" },
            { label: "Massachusetts", value: "MA" },
            { label: "Michigan", value: "MI" },
            { label: "Minnesota", value: "MN" },
            { label: "Mississippi", value: "MS" },
            { label: "Missouri", value: "MO" },
            { label: "Montana", value: "MT" },
            { label: "Nebraska", value: "NE" },
            { label: "Nevada", value: "NV" },
            { label: "New Hampshire", value: "NH" },
            { label: "New Jersey", value: "NJ" },
            { label: "New Mexico", value: "NM" },
            { label: "New York", value: "NY" },
            { label: "North Carolina", value: "NC" },
            { label: "North Dakota", value: "ND" },
            { label: "Ohio", value: "OH" },
            { label: "Oklahoma", value: "OK" },
            { label: "Oregon", value: "OR" },
            { label: "Pennsylvania", value: "PA" },
            { label: "Rhode Island", value: "RI" },
            { label: "South Carolina", value: "SC" },
            { label: "South Dakota", value: "SD" },
            { label: "Tennessee", value: "TN" },
            { label: "Texas", value: "TX" },
            { label: "Utah", value: "UT" },
            { label: "Vermont", value: "VT" },
            { label: "Virginia", value: "VA" },
            { label: "Washington", value: "WA" },
            { label: "West Virginia", value: "WV" },
            { label: "Wisconsin", value: "WI" },
            { label: "Wyoming", value: "WY" }
          ],
          group: 2,
        },
        {
          label: "Zip",
          fieldName: "zip",
          inputType: "number",
          defaultValue: "",
          group: 2,
        }
      ]
    },
    {
      title: "Dog",
      inputs: [
        {
          label: "License Number",
          fieldName: "dogLicenseNumber",
          inputType: "text",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Name",
          fieldName: "dogName",
          inputType: "text",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Tag Number",
          fieldName: "dogTagNumber",
          inputType: "text",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Birth Year",
          fieldName: "dogBirthYear",
          inputType: "number",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Microchip Number",
          fieldName: "dogMicrochipNumber",
          inputType: "text",
          defaultValue: "",
          group: 1,
        },
        {
          label: "Purebred",
          fieldName: "dogPurebred",
          inputType: "select",
          options: [
            { label: "Select", value: '' },
            { label: "Yes", value: true },
            { label: "No", value: false }
          ],
          group: 1,
        },
        {
          label: "Breed",
          fieldName: "dogBreed",
          inputType: "text",
          defaultValue: "",
          group: 2, 
        },
        {
          label: "Primary Color",
          fieldName: "dogPrimaryColor",
          inputType: "select",
          defaultValue: "",
          options: [
            { label: "Select a color", value: '' },
            { label: "Black", value: 'black' },
            { label: "White", value: 'white' },
            { label: "Brown", value: 'brown' },
            { label: "Tan", value: 'tan' },
            { label: "Red", value: 'red' },
            { label: "Cream", value: 'cream' },
            { label: "Grey", value: 'grey' },
            { label: "Blue", value: 'blue' },
            { label: "Sable", value: 'sable' },
            { label: "Brindle", value: 'brindle' },
            { label: "Fawn", value: 'fawn' },
            { label: "Merle", value: 'merle' },
            { label: "Parti-color", value: 'parti-color' },
            { label: "Spotted", value: 'spotted' },
            { label: "Chocolate", value: 'chocolate' },
            { label: "Liver", value: 'liver' },
          ],
          group: 2,
        },
        {
          label: "Secondary Color",
          fieldName: "secondaryColor",
          inputType: "select",
          defaultValue: "",
          options: [
            { label: "Select a color", value: '' },
            { label: "Black", value: 'black' },
            { label: "White", value: 'white' },
            { label: "Brown", value: 'brown' },
            { label: "Tan", value: 'tan' },
            { label: "Red", value: 'red' },
            { label: "Cream", value: 'cream' },
            { label: "Grey", value: 'grey' },
            { label: "Blue", value: 'blue' },
            { label: "Sable", value: 'sable' },
            { label: "Brindle", value: 'brindle' },
            { label: "Fawn", value: 'fawn' },
            { label: "Merle", value: 'merle' },
            { label: "Parti-color", value: 'parti-color' },
            { label: "Spotted", value: 'spotted' },
            { label: "Chocolate", value: 'chocolate' },
            { label: "Liver", value: 'liver' },
          ],
          group: 2,
        },
        {
          label: "Sex",
          fieldName: "dogSex",
          inputType: "select",
          defaultValue: "",
          options: [
            { label: "Select a Sex", value: '' },
            { label: "Male", value: "male" },
            { label: "Female", value: "female" }
          ],
          group: 2,
        },
        {
          label: "Spayed or Neutered",
          fieldName: "dogSpayedNeutered",
          inputType: "select",
          defaultValue: "",
          options: [
            { label: "Select", value: '' },
            { label: "Yes", value: true },
            { label: "No", value: false }
          ],
          group: 2,
        }
      ]
    },
    {
      title: "Vaccine",
      inputs: [
        {
          label: "Vaccine Name",
          fieldName: "vaccineName",
          inputType: "text",
          defaultValue: "",
          size: 'md',
        },
        {
          label: "Vaccine Name",
          fieldName: "vaccineName",
          inputType: "text",
          defaultValue: "",
          size: 'sm',
        },
        {
          label: "Vaccine Name",
          fieldName: "vaccineName",
          inputType: "text",
          defaultValue: "",
          size: 'lg',
        },
      ]
    }
  ]


  res.status(200).json(search)
}
