"use client";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export default function FindError({
  errorTitle = "Error",
  errorMessage
}:{
  errorTitle?: string,
  errorMessage?: string
}) {
  const { push } = useRouter();

  console.log(errorMessage)
  return (
    <div className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white bg-opacity-10"
            style={{
              width: `${Math.random() * 10 + 5}px`,
              height: `${Math.random() * 10 + 5}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animation: `float ${Math.random() * 10 + 5}s linear infinite`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 w-full max-w-md rounded-2xl border border-white border-opacity-30 bg-white bg-opacity-90 p-8 shadow-2xl backdrop-blur-lg">
        <div className="relative mb-10 flex justify-center">
          <div className="bg-white-500 absolute inset-0 rounded-full opacity-30 blur-md filter"></div>
          <Image
            src="/logos/ClerkXpress.svg"
            alt="Company Logo"
            width={200}
            height={80}
            className="relative z-10 rounded-md"
          />
        </div>

        <h1 className="text-center text-3xl font-bold text-neutral-700">
          {errorTitle}
        </h1>

        <div className="mt-4 rounded-lg font-semibold text-lg p-4 text-red-500 text-center">
          {errorMessage}
        </div>

        <div className="mt-6 flex justify-center">
          <Button
            onClick={() => push("/")}
            className="flex w-full transform items-center justify-center space-x-2 rounded-lg bg-blue-600 px-4 py-3 font-bold text-white transition duration-200 ease-in-out hover:scale-105 hover:bg-blue-700 hover:shadow-lg"
          >
            Go Home
          </Button>
        </div>

        <p className="mt-6 text-center text-sm text-neutral-900">
          Please try again or contact support if the issue persists.
        </p>
      </div>
    </div>
  );
}
