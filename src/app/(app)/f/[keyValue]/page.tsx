"use client";
import { useRouter } from "next/navigation";
import { useGetPublicCodeDetails } from "@/hooks/api/usePublicCode";
import Loading from "@/app/(app)/loading";
import FindError from "../FindError";

export default function FindValue({
  params: { keyValue },
}: {
  params: {
    keyValue: string;
  };
}) {
  const { replace } = useRouter();
  const { data, isFetching, isError } = useGetPublicCodeDetails(keyValue);
  if (isFetching) return <Loading text="Searching..." />;
  if (isError)
    return <FindError errorMessage="Code not found. Please try again." />;

  if (!data) {
    return <FindError errorMessage="Code not found. Please try again." />;
  }

  if (data) {
    const { action, entityType } = data;
    
    if (!data?.entityType) {
      <FindError errorMessage="Invalid entity type" />;
    }
  
    if (!data?.action) {
      <FindError errorMessage="Invalid action" />;
    }

    switch (entityType) {
      case "license":
        switch (action) {
          case "lookup":
            replace(`/a/license/${data?.entityId}`);
            break;
          case "renew":
            replace(`/a/license/${data?.entityId}`);
            break;
        }
        break;
      case "individual":
        switch (action) {
          case "lookup":
            replace(`/profile/individual/${data?.entityId}?tab=profile`);
            break;
          case "register":
            replace(`/signup?registrationCode=${data.code}`);
            return <FindError errorMessage="Individual not found. If this is a mistake please contact your local clerk's office." />;
        }
        break;
        
      case "dog":
        switch (action) {
          case "lookup":
            replace(`/found/dog/${data?.entityId}?realm=${data?.realm}`);
            break;
          default:
            return <FindError errorMessage="Dog not found. If this is a mistake please contact your local clerk's office." />;
        }
        break;

      case "tag":
        switch (action) {
          case "create":
            return <FindError errorMessage="Tag not registered. If this is a mistake please contact your local clerk's office." />;
          case "lookup":
            replace(`/f/found/dog/${data?.entityId}?realm=${data?.realm}`);
            break;
          case "register":
            return <FindError  errorMessage="Dog tag is not registered. If this is a mistake please contact your local clerk's office." />;
          default:
            return <FindError errorMessage="Invalid action" />;
        }
        break;

      default:
        return <FindError errorMessage="Invalid entity type" />;
    }
  }

  return <Loading text="Searching..." />;
}
