import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { useKeycloak } from "@/hooks/useKeycloak";
import { useMyPermissions } from "@/hooks/useMyPermissions";
import { Share2, User2 } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { LuClipboardCheck } from "react-icons/lu";

export default function FoundNavbar({ label, canAccess }: { label: string, canAccess: boolean }) {
  const { push } = useRouter();
  const { entityId, entityType } = useParams();

  const { isLoggedIn, logout } = useKeycloak();
  const [isPopoverOpen, setPopoverOpen] = useState(false);


  const handleShareClick = () => {
    navigator.clipboard.writeText(window.location.href);
    setPopoverOpen(true);

    // Automatically close popover after 3 seconds
    setTimeout(() => {
      setPopoverOpen(false);
    }, 3000);
  };

  return (
    <nav className="sticky top-0 z-10 bg-white/80 shadow-sm backdrop-blur-md">
      <div className="mx-auto flex max-w-6xl items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-bold text-gray-900">{label}</h1>
        <div className="flex space-x-2">
          <Popover open={isPopoverOpen} onOpenChange={setPopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" onClick={handleShareClick}>
                <Share2 className="mr-2 h-4 w-4" />
                Share
              </Button>
            </PopoverTrigger>
            <PopoverContent side="bottom" align="start"
              className="flex gap-2 items-center border border-green-400 bg-green-50 py-2"
              alignOffset={-10}
            >
              <LuClipboardCheck /> Link copied to clipboard!
            </PopoverContent>
          </Popover>

          <Button
            variant="default"
            size="sm"
            onClick={() => {
              if (isLoggedIn()) {
                if (canAccess) {
                  push(`/profile/dog/${entityId}?tab=profile`);
                } else {
                  logout();
                }
              } else {
                push(`/login?returnTo=/found/${entityType}/${entityId}`);
              }
            }}
          >
            <User2 className="mr-2 h-4 w-4" />
            {isLoggedIn() ? (canAccess ? "Go to Profile" : "Logout") : "Login"}
          </Button>
        </div>
      </div>
    </nav>
  );
}
