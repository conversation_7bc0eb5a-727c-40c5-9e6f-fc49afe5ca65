import { useForm } from "react-hook-form";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LucideLocate } from "lucide-react";
import { useCreateSighting } from "@/hooks/api/usePublicCode";
import { useParams } from "next/navigation";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";

export default function ReportFoundDog({ refetch }: { refetch: () => void }) {
  const [_, setToast] = useAtom(toastAtom);
  const { entityId, entityType } = useParams();
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      sightingLocation: "",
      sightingDateTime: new Date()
        .toLocaleString("sv-SE", {
          timeZoneName: "short",
        })
        .slice(0, 16),
    },
  });

  const createSighting = useCreateSighting();
  const [locationError, setLocationError] = useState<string | null>(null);
  const [latitude, setLatitude] = useState<string | null>(null);
  const [longitude, setLongitude] = useState<string | null>(null);

  // Function to request geolocation
  const requestLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setLatitude(latitude.toString());
          setLongitude(longitude.toString());
          setLocationError(null);
        },
        (error) => {
          handleGeolocationError(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        },
      );
    } else {
      setLocationError("Geolocation is not supported by this browser.");
    }
  };

  const handleGeolocationError = (error: GeolocationPositionError) => {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        setLocationError("Location permission denied.");
        break;
      case error.POSITION_UNAVAILABLE:
        setLocationError("Location information is unavailable.");
        break;
      case error.TIMEOUT:
        setLocationError("The request to get your location timed out.");
        break;
      default:
        setLocationError(
          "An unknown error occurred while retrieving your location.",
        );
    }
  };

  const onSubmit = async (data: any) => {
    const { name, email, phone, sightingLocation, sightingDateTime } =
      getValues();

    // Ensure at least one contact method is provided
    if (!email && !phone) {
      setError("email", {
        type: "manual",
        message: "Email or Phone is required.",
      });
      setError("phone", {
        type: "manual",
        message: "Email or Phone is required.",
      });
      return;
    }

    clearErrors(["email", "phone"]);

    const convertToISO8601 = (datetime: string): string => {
      const [date, time] = datetime.split(" ");
      return `${date}T${time}:00Z`;
    };

    const sanitizedPhone = phone.replace(/\D/g, "");
    const sanitizedEmail = email.trim();
    const sanitizedName = name.trim();

    console.log(name, email, phone);

    const sightingData = {
      location: {
        name: "Sighting Location",
        datetime: convertToISO8601(sightingDateTime),
        location: sightingLocation,
        latitude: latitude || null,
        longitude: longitude || null,
      },
      reportedBy: {
        name: sanitizedName,
        email: sanitizedEmail,
        phone: sanitizedPhone,
      },
    };

    // Call the mutation
    createSighting.mutate(
      {
        entityId: entityId as string,
        entityType: entityType as string,
        data: sightingData,
      },
      {
        onSuccess: () => {
          console.log("Sighting reported successfully!");
          setToast({
            label: "success",
            message: "Sighting reported successfully!",
          });
          refetch();
        },
        onError: (error) => {
          console.log("Failed to report sighting:", error);
          setToast({
            label: "error",
            message: "Failed to report sighting. Please try again.",
          });
        },
      },
    );
  };

  const InputContainer = ({
    field,
    label,
    placeholder,
    errorMessage,
    required = false,
    type = "text",
  }: {
    field: "name" | "email" | "phone" | "sightingLocation" | "sightingDateTime";
    label: string;
    placeholder: string;
    errorMessage: string;
    required?: boolean;
    type?: string;
  }) => {
    return (
      <div>
        <Label htmlFor={field}>{label}</Label>
        <Input
          id={field}
          {...register(field, { required: required && errorMessage })}
          placeholder={placeholder}
          type={type}
        />
        {errors[field] && <p className="text-red-500">{errorMessage}</p>}
      </div>
    );
  };

  return (
    <div className="rounded-lg bg-white p-6 shadow-sm">
      <h3 className="mb-4 text-xl font-semibold">Report a Sighting</h3>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <InputContainer
          field="name"
          label="Your Name"
          placeholder="Enter your name"
          errorMessage="Name is required."
          required
        />

        <InputContainer
          field="email"
          label="Your Email"
          placeholder="Enter your email"
          errorMessage="Valid email is required."
          type="email"
        />

        <InputContainer
          field="phone"
          label="Phone Number"
          placeholder="Enter your phone number"
          errorMessage="Phone number is required."
          type="tel"
        />

        <div>
          <Label htmlFor="sightingLocation">
            Sighting Location and Details
          </Label>
          <textarea
            id="sightingLocation"
            {...register("sightingLocation")}
            placeholder="Please provide details of the sighting location"
            className="w-full rounded-md border p-2"
          />
          {locationError && <p className="text-red-500">{locationError}</p>}
        </div>

        <div className="flex items-center space-x-2">
          <Button onClick={requestLocation} className="p-2" type="button">
            <LucideLocate className="h-6 w-6" />
          </Button>

          {
            latitude && longitude ? (
              <p className="text-green-500">
                Location found! Latitude: {latitude}, Longitude: {longitude}
              </p>
            ) : (
              <p className="text-gray-500">Click the button to share your location</p>
            )
          }

          {/* <p>Share Location</p> */}
        </div>

        <InputContainer
          field="sightingDateTime"
          label="Date and Time of Sighting"
          placeholder="Date and Time of Sighting"
          type="datetime-local"
          errorMessage=""
        />

        <Button type="submit" className="w-full">
          Submit Sighting
        </Button>
      </form>
    </div>
  );
}
