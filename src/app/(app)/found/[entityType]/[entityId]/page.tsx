"use client";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>geA<PERSON>t,
} from "lucide-react";
import Image from "next/image";
import {
  differenceInDays,
  differenceInMonths,
  differenceInYears,
} from "date-fns";
import { useMyPermissions } from "@/hooks/useMyPermissions";
import ReportFoundDog from "./ReportFoundDog";
import FoundNavbar from "./FoundNavbar";
import { useGetPublicProfileDetails } from "@/hooks/api/usePublicCode";
import Loading from "@/app/(app)/loading";
import ErrorPage from "@/components/error/ErrorPage";
import ContactInformation from "./ContactInformation";
import Sightings from "./Sightings";
import { useSearchParams } from "next/navigation";

export default function DogProfileKeyPage({
  params: { entityType, entityId },
}: {
  params: {
    entityType: string;
    entityId: string;
  };
}) {

  const { hasPermissions } = useMyPermissions();
  const searchParams = useSearchParams();
  const realm = searchParams.get("realm");
  const admin: boolean = hasPermissions(["super-admin"]);
  const { data, isLoading, isError, error, refetch } =
    useGetPublicProfileDetails(entityType as string, entityId as string, realm as string);

  if (isLoading) {
    return <Loading text="Loading Profile" />;
  }

  if (isError) {
    console.log(error);
    return <ErrorPage message="Dog not found. Please try again." />;
  }

  if (data) {
    console.log(data)
    const { dog, license, contacts, sightings } = data;
    
    // Dog Information
    const dogName = dog?.dogName ?? "N/A";
    const dogAvatar = dog?.avatar ?? null;
    const dogBreed = dog?.dogBreed ?? "N/A";
    const dogAge = dog?.dogBirthDate
      ? getAge(new Date(dog.dogBirthDate))
      : "N/A";
    const dogIsLost = dog?.lost ?? false;
    const dogColors = [dog?.dogPrimaryColor, dog?.dogSecondaryColor].filter(
      (color) => color,
    );
    const dogSex = dog?.dogSex
      ? dog.dogSex.charAt(0).toUpperCase() + dog.dogSex.slice(1)
      : "N/A";
    const lastSeenRecords = dog?.lastSeen ?? [];
    const recentLastSeen = lastSeenRecords[0]?.location?.datetime ?? null;
    const lastSeen = recentLastSeen ? lastSeenDate(recentLastSeen) : "N/A";

    // License Information
    const licenseStatus = license?.licenseStatus ?? "N/A";
    
    console.log(dogAvatar)

    return (
      <div className="min-h-screen bg-gray-50">
        <FoundNavbar 
          label={dog.lost ? `Lost Dog: ${dogName}` : `${dogName}`} 
          canAccess
        />
        <main className="mx-auto max-w-6xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="flex flex-col gap-8 lg:flex-row">
            <div className="lg:w-2/3">
              <div className="relative mb-8 h-[400px] overflow-hidden rounded-xl">
                {/* Base 64 */}
                <Image
                  src={
                    dogAvatar
                      ? `data:image/png;base64,${dogAvatar}`
                      : `/images/icons/dog.png`
                  }
                  alt={dogName}
                  className="h-full w-full object-cover"
                  fill
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                  {recentLastSeen && dogIsLost && (
                    <p className="mb-2 text-sm font-medium text-white">
                      Last seen: {lastSeen}
                    </p>
                  )}
                  <h2 className="text-3xl font-bold text-white">{dogName}</h2>
                </div>
              </div>

              <div className="mb-8 grid grid-cols-2 gap-4 sm:grid-cols-4">
                <div className="rounded-lg bg-white p-4 shadow-sm">
                  <h3 className="text-sm font-medium text-gray-500">Breed</h3>
                  <p className="text-lg font-semibold">{dogBreed}</p>
                </div>
                <div className="rounded-lg bg-white p-4 shadow-sm">
                  <h3 className="text-sm font-medium text-gray-500">Age</h3>
                  <p className="text-lg font-semibold">{dogAge}</p>
                </div>
                <div className="rounded-lg bg-white p-4 shadow-sm">
                  <h3 className="text-sm font-medium text-gray-500">Color</h3>
                  <p className="text-lg font-semibold">{dogColors}</p>
                </div>
                <div className="rounded-lg bg-white p-4 shadow-sm">
                  <h3 className="text-sm font-medium text-gray-500">Sex</h3>
                  <p className="text-lg font-semibold">{dogSex}</p>
                </div>
              </div>
              <div className="mb-8 rounded-lg bg-white p-6 shadow-sm">
                <h3 className="mb-4 text-xl font-semibold">About {dogName}</h3>
                <p className="text-gray-600">{dog?.dogBio ?? "No Bio"}</p>
              </div>

              {/* Last Seen Location */}
              {admin && <Sightings sightings={sightings} />}
            </div>

            <div className="space-y-6 lg:w-1/3">
              {/* License status */}
              {admin && (
                <div className="rounded-lg bg-white p-6 shadow-sm">
                  <h3 className="mb-4 text-xl font-semibold">License Status</h3>
                  <div className="flex items-center">
                    {licenseStatus === "Active" ? (
                      <BadgeCheck className="mr-2 h-5 w-5 text-gray-400" />
                    ) : (
                      <BadgeAlert className="mr-2 h-5 w-5 text-red-400" />
                    )}
                    <p>{licenseStatus}</p>
                  </div>
                </div>
              )}
              
              <ReportFoundDog refetch={refetch} />
              <ContactInformation contacts={contacts} />
            </div>
          </div>
        </main>
      </div>
    );
  }

  return <ErrorPage message="Dog not found. Please try again." />;
}

function getAge(birthDate: Date): string {
  const today = new Date();
  const ageInYears = differenceInYears(today, birthDate);
  const ageInMonths = differenceInMonths(today, birthDate);

  if (ageInYears > 0) {
    return `${ageInYears} years old`;
  } else {
    return `${ageInMonths} months old`;
  }
}

function lastSeenDate(date: string) {
  const today = new Date();
  const lastSeen = new Date(date);
  const diff = differenceInDays(today, lastSeen);
  return `${diff} days ago`;
}
