@tailwind base;
@tailwind components;
@tailwind utilities;

.no-arrows::-webkit-inner-spin-button,
.no-arrows::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-arrows {
  -moz-appearance: textfield;
}

.triangle-right {
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 20px solid #2D3748;
  /* Adjust the color to match your design */
}

.triangle-top-right {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid rgba(67, 97, 238, 0.8);
  transform: rotate(45deg);
  border-radius: 3px;
}