import ToastContainer from "@/components/ui/toast/ToastContainer";
import "./globals.css";
import Providers from "@/utils/Providers";
// import { ReactScan } from "@/utils/ReactScan";

export const metadata = {
  title: "ClerkXpress",
  description: "Developed by [s]C<PERSON>",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full !scroll-smooth">
      {/* <ReactScan /> */}
      <body className="h-full">
        <Providers>
          <ToastContainer />
          {children}
        </Providers>
      </body>
    </html>
  );
}
