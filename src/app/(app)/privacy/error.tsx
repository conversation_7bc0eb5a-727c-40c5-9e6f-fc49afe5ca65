"use client";
import Navbar from "@/components/landingPage/Navbar";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import React from "react";

interface ErrorProps {
  message?: string;
}

const PrivacyError: React.FC<ErrorProps> = ({ message }) => {
  const router = useRouter();
  return (
    <div className="h-full w-full scroll-smooth">
      <Navbar />
      <div className="container relative mx-auto mt-10 flex max-w-7xl flex-col items-center justify-start gap-4 px-6">
        <p className="text-center text-2xl font-bold">
          {message ?? "Error Fetching Data"}
        </p>
        <Button variant="primary" size="sm" onClick={() => router.refresh()}>
          Refresh
        </Button>
      </div>
    </div>
  );
};

export default PrivacyError;
