"use client";
import Footer from "@/components/landingPage/Footer";
import { cn } from "@/lib/utils";
import Link from "next/link";
import React from "react";
import { generateLinks } from "./policy-helper";
import { useCMS } from "@/hooks/api/useCMS";
import Loading from "@/app/(app)/loading";
import PrivacyError from "../error";
import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import Navbar from "@/components/landingPage/Navbar";

export default function PrivacyPolicyPage() {
  const { data, isFetching, isError, error } = useCMS("privacy_policy");

  if (isFetching) {
    return (
      <>
        <Navbar />
        <Loading text="Loading Privacy Policy"/>
      </>
    );
  }

  if (isError) {
    return <PrivacyError message={(error as any)?.message} />;
  }

  const { tableOfContents, mdxData } = data;

  return (
    <div className="h-full w-full scroll-smooth">
      <Link href="#mainContent" passHref className="sr-only focus:not-sr-only">
        Skip to main content
      </Link>
      <Navbar />
      <div className="container relative mx-auto mt-32 max-w-6xl px-6">
        <H1>ONLINE PRIVACY POLICY</H1>

        <hr />
        <br />
        <div className=" flex h-full flex-col gap-10 pt-10 md:flex-row">
          {/* Table of Contents */}
          <nav className="z-10 h-auto w-full max-w-sm text-sm lg:sticky lg:top-20 lg:h-screen">
            <article id="tableOfContents">
              <H2 className="mt-0">TABLE OF CONTENTS</H2>
              <ListNoBullets>
                {tableOfContents.map((item: { id: string; label: string }) => (
                  <LinkClickable
                    key={item.id}
                    id={item.id}
                    label={item.label}
                  />
                ))}
              </ListNoBullets>
            </article>
          </nav>

          {/* Content */}
          <div className="mb-20 h-full">
            {mdxData.map(
              (item: { id: string; label: string; content: any }) => (
                <Article
                  key={item.id}
                  id={item.id}
                  className={item.id === mdxData[0].id ? "pt-0" : ""}
                >
                  <Markdown
                    remarkPlugins={[remarkGfm]}
                    components={overrideMdxComponents}
                  >
                    {item.content}
                  </Markdown>
                </Article>
              ),
            )}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

const Article = ({
  children,
  id,
  className,
}: {
  children: React.ReactNode;
  id: string;
  className?: string;
}) => {
  return (
    <article id={id} className={cn(" space-y-4 pt-20", className)}>
      {children}
    </article>
  );
};

const Paragraph = ({ children }: { children: React.ReactNode }) => {
  return (
    <p className="mb-4 text-base leading-relaxed text-gray-700">{children}</p>
  );
};

const List = ({ children }: { children: React.ReactNode }) => {
  return <ul className="list-disc pl-8 text-gray-700">{children}</ul>;
};

const LinkClickable = ({ id, label }: { id: string; label: string }) => {
  return (
    <ListItem>
      <Link
        href={`#${id}`}
        aria-label={`${label}`}
        className={cn("uppercase hover:text-blue-600 focus:text-blue-600")}
      >
        {label}
      </Link>
    </ListItem>
  );
};

const ListNoBullets = ({ children }: { children: React.ReactNode }) => {
  return <ul className="text-gray-700">{children}</ul>;
};

const ListItem = ({ children }: { children: React.ReactNode }) => {
  return <li className="mb-2">{children}</li>;
};

const H1 = ({ children }: { children: React.ReactNode }) => {
  return <h1 className="mb-6 text-3xl font-bold text-gray-900">{children}</h1>;
};

const H2 = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <h2 className={cn("mb-4 text-xl font-semibold text-gray-800", className)}>
      {children}
    </h2>
  );
};

// Content Link
const ContentLink = ({ children }: { children: React.ReactNode }) => {
  const html = generateLinks(children as string);
  return <span dangerouslySetInnerHTML={{ __html: html }} />;
};

const overrideMdxComponents: any = {
  h1: H1,
  h2: H2,
  p: Paragraph,
  ul: List,
  li: ListItem,
  a: ContentLink,
};
