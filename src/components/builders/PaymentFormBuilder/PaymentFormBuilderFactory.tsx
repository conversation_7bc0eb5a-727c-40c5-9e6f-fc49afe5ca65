import { useGetJSONStorage } from "@/hooks/api/useAdmin";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { PaymentFormBuilder } from "./PaymentFormBuilder";

interface PaymentFormBuilderFactoryProps {
    setValue: (key: string, value: any) => void;
}

const PAYMENT_FORM = "paymentForm";

const PROFILE_KEY = "payment";

export default function PaymentFormBuilderFactory({
    setValue
}: PaymentFormBuilderFactoryProps) {
  const { data, isLoading } = useGetJSONStorage(PROFILE_KEY, PAYMENT_FORM);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!data) {
    return null;
  }

  return (
    <PaymentFormBuilder setValue={setValue} config={data}/>
  );
}
