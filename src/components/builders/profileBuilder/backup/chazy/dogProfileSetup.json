{"id": "dog-profile-builder", "title": "Profile Configuration", "profile": {"entityType": "dog", "tab": "Dog", "displayName": "Dog Profile", "icon": "PawP<PERSON>t", "groups": [{"title": "Basic Information", "description": "Basic information about your dog", "order": 1, "fields": [{"id": "tagNumber", "label": "Dog Tag Number", "key": "tagNumber", "value": "{{tagNumber}}", "type": "otp", "length": 6, "required": true, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Enter tag number", "validate": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "Tag number must be at least 1 character"}, {"type": "max<PERSON><PERSON><PERSON>", "value": 6, "message": "Tag number must be 6 characters"}]}, {"id": "microchipNumber", "key": "microchipNumber", "value": "{{microchipNumber}}", "label": "Microchip Number", "type": "text", "required": false, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Enter microchip number"}, {"id": "<PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "value": "{{dogName}}", "label": "Dog Name", "type": "text", "required": true, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Enter dog name"}, {"id": "dogBreed", "key": "dogBreed", "value": "{{dogBreed}}", "label": "Breed", "type": "customSelect", "options": "{{settings: entity.dog.breeds}}", "required": true, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select breed"}, {"id": "dogBirthDate", "key": "dogBirthDate", "value": "{{dogBirthDate}}", "label": "Date of Birth", "type": "dateofbirth", "formatPattern": "MMMM dd, yyyy", "required": true, "validate": [{"type": "function", "value": "isNotFutureDate"}], "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select date of birth"}, {"id": "age", "key": "age", "value": "calculateAgeInMonthsOrYears({{dogBirthDate}})", "label": "Age", "type": "display", "required": false, "flaggable": false, "span": "1/2", "placeholder": "Enter age"}, {"id": "dogSex", "key": "dogSex", "value": "{{dogSex}}", "label": "Sex", "type": "select", "options": "{{settings: entity.dog.sex}}", "required": true, "flaggable": false, "span": "1/2", "placeholder": "Select sex"}, {"id": "dogSpayedOrNeutered", "type": "toggleField", "key": "dogSpayedOrNeutered", "value": "{{dogSpayedOrNeutered}}", "toggleCondition": {"conditionType": "AND", "conditions": [{"field": "dogSex", "comparison": "equals", "value": "Female"}]}, "if": {"label": "Spayed", "type": "select", "key": "dogSpayedOrNeutered", "value": "{{dogSpayedOrNeutered}}", "options": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "exempt"}], "display": {"exempt": "No", "yes": "Yes"}, "required": true, "flaggable": false, "span": "1/2"}, "else": {"label": "Neutered", "type": "select", "value": "{{dogSpayedOrNeutered}}", "key": "dogSpayedOrNeutered", "options": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "exempt"}], "display": {"exempt": "No", "yes": "Yes"}, "required": true, "flaggable": false, "span": "1/2"}, "placeholder": "Select yes or no"}, {"id": "dogPrimaryColor", "label": "Primary Color", "key": "dogPrimaryColor", "value": "{{dogPrimaryColor}}", "type": "customSelect", "options": "{{settings: entity.dog.colors}}", "placeholder": "Select a Primary Color", "required": true, "flaggable": false, "span": "1/2"}, {"id": "dogSecondaryColor", "label": "Secondary Color", "key": "dogSecondaryColor", "value": "{{dogSecondaryColor}}", "type": "customSelect", "options": "{{settings: entity.dog.colors}}", "placeholder": "Select a Secondary Color", "required": false, "flaggable": false, "span": "1/2"}, {"id": "dogBio", "label": "Bio", "key": "dogBio", "value": "{{dog<PERSON><PERSON>}}", "type": "textarea", "placeholder": "Enter bio", "required": false, "flaggable": false, "span": "full", "validate": [{"type": "max<PERSON><PERSON><PERSON>", "value": 500, "message": "Dog bio must be less than 500 characters"}]}, {"id": "dogSpayedOrNeuteredDocument", "label": "Spayed or Neutered Document", "type": "file", "maxSize": 209715200, "key": "dogSpayedOrNeuteredDocument", "value": "{{documents[key='dogSpayedOrNeuteredDocument']}}", "accept": [".pdf", ".jpeg", ".png", ".jpg"], "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "dogSpayedOrNeutered", "comparison": "equals", "value": "yes"}, {"field": "isAlteredExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Upload spayed or neutered document"}, {"id": "dogSpayedOrNeuteredExemptionDocument", "label": "Spayed or Neutered Exemption Document", "type": "file", "maxSize": 209715200, "key": "dogSpayedOrNeuteredExemptionDocument", "value": "{{documents[key='dogSpayedOrNeuteredExemptionDocument']}}", "accept": [".pdf", ".jpeg", ".png", ".jpg"], "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "dogSpayedOrNeutered", "comparison": "equals", "value": "yes"}, {"field": "isAlteredExempt", "comparison": "equals", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2"}]}, {"title": "Rabies Vaccination Information", "description": "Rabies vaccination information", "order": 2, "fields": [{"id": "veterinaryName", "label": "Veterinary Name", "key": "veterinaryName", "value": "{{veterinaryName}}", "type": "text", "required": true, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "validate": [{"type": "max<PERSON><PERSON><PERSON>", "value": 100, "message": "Veterinary name must be less than 100 characters"}], "placeholder": "Enter veterinary name"}, {"id": "vaccineDatesExempt", "label": "Vaccine Dates Exempt", "type": "boolean", "key": "vaccineDatesExempt", "value": "{{vaccineDatesExempt}}", "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}], "required": true, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select yes or no"}, {"id": "vaccineProducer", "label": "Vaccine Producer", "key": "vaccineProducer", "value": "{{vaccineProducer}}", "type": "select", "options": "{{settings: entity.dog.rabies_producer}}", "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select vaccine producer"}, {"id": "vaccineBrand", "label": "Vaccine Brand", "key": "vaccineBrand", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "select", "options": "{{settings: entity.dog.rabies_brand}}", "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select vaccine brand"}, {"id": "rabiesTagNumber", "label": "Rabies Tag Number", "type": "text", "key": "rabiesTagNumber", "value": "{{rabiesTagNumber}}", "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Enter rabies tag number"}, {"id": "vaccineAdministeredDate", "label": "Vaccine Administered Date", "type": "dateofbirth", "key": "vaccineAdministeredDate", "value": "{{vaccineAdministeredDate}}", "required": true, "validate": [{"type": "function", "value": "isNotFutureDate"}], "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select vaccine administered date"}, {"id": "vaccineDueDate", "label": "Vaccine Due Date", "type": "dateofbirth", "key": "vaccineDueDate", "value": "{{vaccineDueDate}}", "validate": [{"type": "function", "value": "isFutureDate"}], "required": true, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select vaccine due date"}, {"id": "vaccineLotNumber", "label": "Lot Number", "type": "text", "key": "vaccineLotNumber", "value": "{{vaccineLotNumber}}", "required": false, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Enter vaccine lot number"}, {"id": "vaccineLotExpirationDate", "label": "Lot Expiration Date", "type": "dateofbirth", "key": "vaccineLotExpirationDate", "value": "{{vaccineLotExpirationDate}}", "required": false, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select vaccine lot expiration date"}, {"id": "dogRabiesVaccinationDocument", "label": "Rabies Document", "type": "file", "maxSize": 209715200, "key": "dogRabiesVaccinationDocument", "value": "{{documents[key='dogRabiesVaccinationDocument']}}", "accept": [".pdf", ".jpeg", ".png", ".jpg"], "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": false}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select rabies document"}, {"id": "dogRabiesVaccinationExemptionDocument", "label": "Rabies Exemption Document", "type": "file", "maxSize": 209715200, "key": "dogRabiesVaccinationExemptionDocument", "value": "{{documents[key='dogRabiesVaccinationExemptionDocument']}}", "accept": [".pdf", ".jpeg", ".png", ".jpg"], "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "vaccineDatesExempt", "comparison": "coercion", "value": true}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select rabies exemption document"}]}, {"title": "Service Dog Information", "description": "Service dog certification and details", "order": 3, "fields": [{"id": "licenseExempt", "label": "Service Dog", "type": "boolean", "key": "licenseExempt", "value": "{{licenseExempt}}", "options": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}], "required": true, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select yes or no"}, {"id": "serviceDogType", "label": "Service Dog Type", "key": "serviceDogType", "value": "{{serviceDogType}}", "type": "select", "options": "{{settings: entity.dog.service_dog}}", "required": true, "visible": {"conditionType": "AND", "conditions": [{"field": "licenseExempt", "comparison": "coercion", "value": true}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select service dog type"}, {"id": "licenseExemptionDocument", "label": "Service Dog Verification Document", "key": "licenseExemptionDocument", "value": "{{documents[key='licenseExemptionDocument']}}", "type": "file", "maxSize": 209715200, "accept": [".pdf", ".jpeg", ".png", ".jpg"], "required": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"], "comparison": "not"}]}, "visible": {"conditionType": "AND", "conditions": [{"field": "licenseExempt", "comparison": "coercion", "value": true}]}, "flaggable": {"conditionType": "AND", "conditions": [{"permissions": ["super-admin"]}]}, "span": "1/2", "placeholder": "Select service dog verification document"}]}]}, "functions": {"saveProfileField": {"type": "rest", "url": "/license/participant/{{context:entityId}}", "meUrl": "/license/me/participant/{{context:entityType}}/{{context:entityId}}", "method": "PATCH", "format": "formData", "body": "{{context:body}}", "description": "Save individual field value"}, "flagField": {"type": "rest", "url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields", "meUrl": "/license/me/profile/{{context:entityType}}/{{context:entityId}}/reject-fields", "method": "PATCH", "format": "json", "body": {"fields": ["{{context:fieldId}}"]}, "description": "Flag field for review"}, "unflagField": {"type": "rest", "url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields?field={{context:fieldId}}", "meUrl": "/license/me/profile/{{context:entityType}}/{{context:entityId}}/reject-fields?field={{context:fieldId}}", "method": "DELETE", "format": "json", "description": "Remove flag from field"}}}