import { Input } from "@/components/ui/input";
import { ChangeEvent } from "react";

interface InputFieldProps {
  field: {
    id: string;
    [key: string]: any;
  };
  tempValue: string;
  handleChange: (value: string) => void;
  required?: boolean;
  setTempValue?: (value: string) => void;
  isDisplay?: boolean;
}

const PhoneInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay = true,
}) => {
  const formatPhoneNumber = (value: string) => {
    if (!value) return value;
    const phoneNumber = value.replace(/[^\d]/g, "");
    const phoneNumberLength = phoneNumber.length;
    if (phoneNumberLength < 4) return phoneNumber;
    if (phoneNumberLength < 7) return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/\D/g, "");
    if (setTempValue) setTempValue(rawValue);
    handleChange(rawValue);
  };

  return (
    <>
      {isDisplay ? (
        <span className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden">
          {formatPhoneNumber(tempValue) || ""}
        </span>
      ) : (
        <Input
          id={field.id}
          value={formatPhoneNumber(tempValue)}
          onChange={handleInputChange}
          required={required}
          className="w-full overflow-hidden text-ellipsis"
          placeholder={field.placeholder || "(*************"}
          maxLength={field.maxLength || 14} 
          type="tel"
        />
      )}
    </>
  );
};

export default PhoneInputField;
