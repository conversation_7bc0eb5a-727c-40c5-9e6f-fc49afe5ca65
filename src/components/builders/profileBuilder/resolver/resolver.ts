import computedField from "./functionResolver";

const FUNCTION_REGEX = /^(\w+)\(([^)]*)\)$/;
const DYNAMIC_REGEX = /\{\{\s*((?:.|\n)*?)\s*\}\}/;

export const resolver = (obj: Record<string, any>, path: string) => {
  const result = determineType(path, obj);
  if(result?.deleted) return null;
  return result;
};

export const updateField = (
  value: any,
  obj: Record<string, any>,
  newValue: any,
) => {
  const match = value.match(DYNAMIC_REGEX);
  if (!match) return value;
  const [, key] = match;
  if (newValue instanceof File) {
    updateNestedValues(obj, `${key}.fileName`, newValue?.name ?? "");
    updateNestedValues(obj, `${key}.contentType`, newValue?.type ?? "");
  }else{
    updateNestedValues(obj, key, newValue);
  }
};

/**
 * Pure helper that mutates a JS object in‑place, setting a nested key
 * (dot/bracket syntax) to newValue. Exactly the same as before, just
 * renamed here to avoid confusion with the React setter.
 */
const updateNestedValues = (
  obj: Record<string, any>,
  path: string,
  newValue: any,
): void => {
  const segments = path.split(".");
  let cursor: any = obj;

  for (let i = 0; i < segments.length; i++) {
    const isLast = i === segments.length - 1;
    const segment = segments[i];
    const bracketMatch = segment.match(/^(\w+)\[(.+)\]$/);

    if (bracketMatch) {
      const [, arrayKey, selector] = bracketMatch;
      const arr = cursor[arrayKey];
      if (!Array.isArray(arr)) {
        throw new Error(
          `Expected "${arrayKey}" to be an array in path "${path}"`,
        );
      }

      let nextItem: any;
      if (/^\d+$/.test(selector)) {
        // numeric index
        nextItem = arr[Number(selector)];
      } else {
        // filter by property e.g. type='Mailing' && status='Active'
        const conditions = selector.split('&&').map(cond => cond.trim());
        const filterMatches = conditions.map(cond => cond.match(/^(\w+)=['"](.+)['"]$/)).filter((match): match is RegExpMatchArray => match !== null);
        if (filterMatches.length !== conditions.length) {
          throw new Error(`Invalid filter syntax in path "${path}"`);
        }
        nextItem = arr.find((el: any) => 
          filterMatches.every(([, propName, propValue]) => 
            el[propName] === propValue
          )
        );
      }
      if (nextItem == null) {
        break;
      }

      if (isLast) {
        // replace the array element itself
        if (/^\d+$/.test(selector)) {
          arr[Number(selector)] = newValue;
        } else {
          const conditions = selector.split('&&').map(cond => cond.trim());
          const filterMatches = conditions.map(cond => cond.match(/^(\w+)=['"](.+)['"]$/)).filter((match): match is RegExpMatchArray => match !== null);
          if (filterMatches.length !== conditions.length) {
            console.error(`Invalid filter syntax in path "${path}"`);
          }
          const idx = arr.findIndex((el: any) => 
            filterMatches.every(([, propName, propValue]) => 
              el[propName] === propValue
            )
          );
          if (idx === -1) {
            console.error(`No matching element found in path "${path}"`);
          }
          arr[idx] = newValue;
        }
      } else {
        cursor = nextItem;
      }
    } else {
      // plain object key
      if (isLast) {
        cursor[segment] = newValue;
      } else {
        if (cursor[segment] == null) {
          cursor[segment] = {};
        }
        cursor = cursor[segment];
      }
    }
  }
};

/**
 * Retrieve a nested value from a DynamicContext given a "dot/bracket" path.
 *
 * Supports:
 *  • Plain object keys:      "main", "zip"
 *  • Numeric array indices:  "items[0]"
 *  • Array‐filter syntax:    "addresses[type='Mailing']"
 *
 * @param obj   The context object (or any nested object/array)
 * @param path  The lookup path, e.g. "main.zip" or "addresses[type='Mailing'].zip"
 * @returns     The value at that path, or `undefined` if any lookup step fails
 */
export const getNestedValue = (obj: Record<string, any>, path: string): any => {
  const segments = path.split(".");
  let cursor: any = obj;

  for (const segment of segments) {
    if (cursor == null) return undefined;

    // does this segment use bracket syntax? e.g. "foo[2]" or "addresses[type='Mailing']"
    const bracketMatch = segment.match(/^(\w+)\[(.+)\]$/);
    if (bracketMatch) {
      const [, arrayKey, selector] = bracketMatch;
      const arr = cursor?.[arrayKey];
      if (!Array.isArray(arr)) return undefined;

      // numeric index, e.g. "[0]"
      if (/^\d+$/.test(selector)) {
        cursor = arr?.[Number(selector)];
      }
      // property‐filter, e.g. "[type='Mailing']" or "[type='Mailing' && status='Active']"
      else {
        const conditions = selector.split('&&').map(cond => cond.trim());
        const filterMatches = conditions.map(cond => cond.match(/^(\w+)=['"](.+)['"]$/)).filter((match): match is RegExpMatchArray => match !== null);
        if (filterMatches.length !== conditions.length) return undefined;
        
        cursor = arr.find((el: any) => 
          filterMatches.every(([, propName, propValue]) => 
            el[propName] === propValue
          )
        );
      }
    }
    // plain object key, e.g. "main" or "zip"
    else {
      cursor = cursor?.[segment];
    }
  }
  
  return cursor;
};

const functionResolver = (value: any, obj: Record<string, any>) => {
  const match = value.match(FUNCTION_REGEX);
  if (!match) return value;
  const [, computed, params] = match;
  return computedField(
    computed,
    params.split(",").map((param: any) => {
      if (!param.match(FUNCTION_REGEX)) {
        // to avoid infinite loop
        return determineType(param, obj);
      }
      return param;
    }),
  );
};

const dynamicResolver = (value: any, obj: Record<string, any>) => {
  const match = value.match(DYNAMIC_REGEX);
  if (!match) return value;
  const resultArray = value.split("+").map((item: any) => {
  const itemMatch = item.match(DYNAMIC_REGEX);
  if (!itemMatch) return item;
  const [, key] = itemMatch;
  return getNestedValue(obj, key);
  });
  // Check if all elements are strings
  const allStrings = resultArray.every((el: any) => typeof el === "string" || typeof el === "number");

  return allStrings ? resultArray.join("") : resultArray?.[0];
};

const determineType = (value: any, obj: Record<string, any>) => {
  if (typeof value !== "string") return value;

  if (value.match(FUNCTION_REGEX)) {
    return functionResolver(value, obj);
  }

  if (value.match(DYNAMIC_REGEX)) {
    return dynamicResolver(value, obj);
  }

  return value;
};
