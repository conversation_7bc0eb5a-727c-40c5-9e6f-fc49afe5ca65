"use client";
import {
  addQuarters,
  addYears,
  subQuarters,
  subYears,
  addMonths,
  subMonths,
  addWeeks,
  subWeeks,
  addDays,
  subDays,
  format,
  startOfWeek,
  endOfWeek,
} from "date-fns";
import { FiChevronDown, FiChevronLeft, FiChevronRight } from "react-icons/fi";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { useDashboardContext } from "@/app/(app)/(realm)/(protected)/(app)/dashboard/useDashboardContext";
export const rangeDisplay = (range: string, date: Date) => {
  switch (range) {
    case "day":
      return format(date, "EEEE, MMMM d");
    case "week":
      return `${format(startOfWeek(date), "MMMM d")} - ${format(
        endOfWeek(date),
        "MMMM d",
      )}`;
    case "month":
      return format(date, "MMMM yyyy");
    case "year":
      return format(date, "yyyy");
    case "quarters":
      return format(date, "QQQQ yyyy");
    default:
      return "";
  }
};

const dateSub: {
  [key: string]: (date: Date) => Date;
} = {
  day: (date: Date) => subDays(date, 1),
  week: (date: Date) => subWeeks(date, 1),
  month: (date: Date) => subMonths(date, 1),
  quarters: (date: Date) => subQuarters(date, 1),
  year: (date: Date) => subYears(date, 1),
};

const dateAdd: {
  [key: string]: (date: Date) => Date;
} = {
  day: (date: Date) => addDays(date, 1),
  week: (date: Date) => addWeeks(date, 1),
  month: (date: Date) => addMonths(date, 1),
  quarters: (date: Date) => addQuarters(date, 1),
  year: (date: Date) => addYears(date, 1),
};

const subFunction = (func_date: Date, func_sortType: string) => {
  const action = dateSub[func_sortType];
  return action(func_date);
};

const addFunction = (func_date: Date, func_sortType: string) => {
  const action = dateAdd[func_sortType];
  return action(func_date);
};

type RangeBarProps = {
  date: Date;
  setDate: React.Dispatch<React.SetStateAction<Date>>;
  sortType: string;
  setSortType: React.Dispatch<React.SetStateAction<string>>;
};

const tooltipText = {
  month: {
    previous: "Previous Month",
    next: "Next Month",
  },
  day: {
    previous: "Previous Day",
    next: "Next Day",
  },
  week: {
    previous: "Previous Week",
    next: "Next Week",
  },
  year: {
    previous: "Previous Year",
    next: "Next Year",
  },
  quarters: {
    previous: "Previous Quarter",
    next: "Next Quarter",
  },
};

const RangeBar = () => {
  const { date, setDate, sortType, setSortType } = useDashboardContext();
  return (
    <TooltipProvider>
      <div className="flex items-center gap-4">
        <div className="flex flex-row">
          <Button onClick={() => setDate(new Date())} size="sm" variant="ghost">
            Today
          </Button>
          <div className="flex items-center justify-between gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={() => setDate(subFunction(date, sortType))}
                  variant="ghost"
                >
                  <FiChevronLeft />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="center">
                <p>
                  {tooltipText[sortType as keyof typeof tooltipText].previous}
                </p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={() => {
                    setDate(addFunction(date, sortType));
                  }}
                  variant="ghost"
                >
                  <FiChevronRight />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="center">
                <p>{tooltipText[sortType as keyof typeof tooltipText].next}</p>
              </TooltipContent>
            </Tooltip>

            <div>
              {/* <p>{format(date, formatTree[sortType])}</p> */}
              <p>{rangeDisplay(sortType, date)}</p>
            </div>
          </div>
        </div>
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="outline">
                <div className="flex items-center justify-between gap-2 capitalize">
                  {sortType}
                  <FiChevronDown />
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {sortTypesOptions.map((option, index) => (
                <DropdownMenuItem
                  key={option.value}
                  onSelect={() => setSortType(option.value)}
                >
                  {option.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </TooltipProvider>
  );
};

const sortTypesOptions: {
  label: string;
  value: "month" | "day" | "year" | "week" | "quarters";
}[] = [
  {
    label: "Day",
    value: "day",
  },
  {
    label: "Week",
    value: "week",
  },
  {
    label: "Month",
    value: "month",
  },
  // {
  //   label: "Quarters",
  //   value: "quarters",
  // },
  {
    label: "Year",
    value: "year",
  },
];

export default RangeBar;
