// import { useState } from "react";

// type TableSizeProps = {
//   table: any;
// };

// const TableSize = ({ table }: TableSizeProps) => {
//   const pageSize = table.getState().pagination.pageSize;

//   // Get total number of rows

//   return (
//     <div className="flex items-center gap-2">
//       <select
//         value={table.getState().pagination.pageSize}
//         onChange={(e) => {
//           table.setPageSize(Number(e.target.value));
//         }}
//       >
//         {[5, 10, 20, 30, 40, 50].map((size) => (
//           <option key={size} value={size} disabled={size > pageSize}>
//             Show {size}
//           </option>
//         ))}
//       </select>
//     </div>
//   );
// };

// export default TableSize;

import { useState } from "react";

type TableSizeProps = {
  table: any;
  totalRows: number;
};

const TableSize = ({ table, totalRows }: TableSizeProps) => {
  const pageSize = table.getState().pagination.pageSize;

  return (
    <select
      value={pageSize}
      onChange={(e) => {
        table.setPageSize(Number(e.target.value));
      }}
      className="font-semibold text-neutral-500 border-none outline-none"
    >
      {[5, 10, 20, 30, 40, 50].map((pageSizeOption) => (
        <option
          key={pageSizeOption}
          value={pageSizeOption}
          disabled={pageSizeOption > totalRows}
        >
          Show {pageSizeOption}
        </option>
      ))}
    </select>
  );
};

export default TableSize;
