import { cn } from "@/lib/utils";
import { Input } from "../ui/input";
import { Label as LabelDiv } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Control, Controller } from "react-hook-form";
import { Checkbox } from "../ui/checkbox";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import FileUpload from "../universal/input/SimpleFileUpload";
import ComboBoxComponent from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/profile/ComboBoxComponent";
import { formatPhoneNumber } from "../license/licenseHelper";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";

// options
import breeds from "@/fakeData/options/dogBreedOptions.json";
import colors from "@/fakeData/options/dogColorOptions.json";
import states from "@/fakeData/options/stateOptions.json";
import vaccineProducers from "@/fakeData/options/vaccineProducerOptions.json";
import vaccineBrands from "@/fakeData/options/vaccineBrandOptions.json";
import { Badge } from "../ui/badge";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp"

// Universal
const validateName = (value: string) =>
  !value ||
  /^[a-zA-Z0-9À-ÖØ-öø-ÿ'-.]+(?:[- '][a-zA-Z0-9À-ÖØ-öø-ÿ'-.]+)*$/i.test(value);

interface FieldDivProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const FieldDiv = ({ children, className, ...props }: FieldDivProps) => {
  return (
    <div className={cn("flex flex-col gap-2", className)} {...props}>
      {children}
    </div>
  );
};

const Label = ({
  children,
  htmlFor,
  flag = false,
  className,
}: {
  children: React.ReactNode;
  htmlFor?: string;
  flag?: boolean;
  className?: string;
}) => {
  return (
    <LabelDiv
      htmlFor={htmlFor}
      className={cn(
        "flex items-center gap-1",
        flag && "text-red-700",
        className,
      )}
    >
      {" "}
      {children}
      {flag && (
        <Badge variant={"destructive"} className="px-1 text-xs">
          Issue
        </Badge>
      )}
    </LabelDiv>
  );
};

const Error = ({ children }: { children: React.ReactNode }) => {
  return <p className="text-red-500">{children}</p>;
};

export const Comment = ({ register }: { register: any }) => {
  return (
    <FieldDiv>
      <Label htmlFor="comments">Comment (optional)</Label>
      <Textarea
        {...register("comment", { required: false })}
        placeholder="Enter any comments here"
      />
    </FieldDiv>
  );
};

// Dogs
export const DogName = ({
  errors,
  register,
  flag = false,
}: {
  errors: any;
  register: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="dogName" flag={flag}>
        Dog Name
      </Label>
      <Input
        autoComplete="off"
        {...register("dogName", { validate: validateName })}
      />
      {errors?.dogName && <Error>Invalid Dog Name</Error>}
    </FieldDiv>
  );
};

export const TagNumber = ({
  errors,
  control,
}: {
  errors: any;
  control: Control<any>;
}) => {
  const handleChange = (value: string, onChange: (value: string) => void) => {
    const cleanedValue = value.replace(/[^A-Z0-9]/gi, "").toUpperCase();
    onChange(cleanedValue);
  };

  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="tagNumber">
        Tag Number
      </Label>
      <Controller
        name="tagNumber"
        control={control}
        rules={{
          required: "Tag Number is required",
          minLength: {
            value: 4,
            message: "Tag Number must be 4 characters long",
          },
          maxLength: {
            value: 6,
            message: "Tag Number must be 6 characters long",
          },
        }}
        render={({ field: { value, onChange } }) => (
          <InputOTP 
            maxLength={6} 
            pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
            value={value}
            onChange={(value) => handleChange(value, onChange)}
          >
            <InputOTPGroup>
              {[...Array(6)].map((_, index) => (
                <InputOTPSlot key={index} index={index} />
              ))}
            </InputOTPGroup>
          </InputOTP>
        )}
      />
      {errors?.tagNumber && <Error>{errors.tagNumber.message}</Error>}
    </div>
  );
};

export const ExemptVaccine = ({
  control,
  flag = false,
}: {
  control: any;
  flag?: boolean;
}) => {
  return (
    <div className="my-4 flex flex-row items-center gap-2">
      <Controller
        name="vaccineDatesExempt"
        control={control}
        render={({ field: { onChange, value } }) => {
          return (
            <Checkbox
              id="vaccineDatesExempt"
              checked={value}
              onCheckedChange={onChange}
            />
          );
        }}
      />
      <Label
        className="cursor-pointer"
        htmlFor="vaccineDatesExempt"
        flag={flag}
      >
        Exempt from Rabies Vaccine
      </Label>
    </div>
  );
};

export const DogSex = ({
  errors,
  control,
  flag = false,
}: {
  errors: any;
  control: Control<any>;
  flag?: boolean;
}) => {
  console.log(control);
  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="dogSex" flag={flag}>
        Dog Sex
      </Label>
      <Controller
        name="dogSex"
        control={control}
        rules={{ required: "Dog sex is required" }}
        render={({ field: { onChange, value }, fieldState: { error } }) => {
          console.log(value);
          return (
            <>
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select a Sex" />
                </SelectTrigger>
                <SelectContent className="z-[9999]">
                  <SelectGroup>
                    <SelectItem value="Male">Male</SelectItem>
                    <SelectItem value="Female">Female</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              {error && <Error>Invalid Sex</Error>}
            </>
          );
        }}
      />
      {errors.dogSex && <Error>Invalid Dog Sex</Error>}
    </div>
  );
};

export const DogBreed = ({
  control,
  errors,
  flag = false,
}: {
  control: Control;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="dogBreed" flag={flag}>
        Dog Breed
      </Label>
      <Controller
        name="dogBreed"
        control={control}
        render={({ field: { onChange, value } }) => {
          console.log(value);
          return (
            <ComboBoxComponent
              name="dogBreed"
              options={breeds}
              placeholder="Select option..."
              selectedValue={value}
              onValueChange={(name, value) => onChange(value)}
            />
          );
        }}
      />
      {errors.dogBreed && <Error>Invalid Dog Breed</Error>}
    </div>
  );
};

export const DogPrimaryColor = ({
  control,
  errors,
  flag = false,
}: {
  control: Control;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="dogPrimaryColor" flag={flag}>
        Primary Color
      </Label>
      <Controller
        name="dogPrimaryColor"
        control={control}
        render={({ field: { onChange, value } }) => {
          console.log(value);
          return (
            <ComboBoxComponent
              name="dogPrimaryColor"
              options={colors}
              placeholder="Select option..."
              selectedValue={value}
              onValueChange={(name, value) => onChange(value)}
            />
          );
        }}
      />
      {errors.dogPrimaryColor && <Error>Invalid Color</Error>}
    </div>
  );
};

export const DogSecondaryColor = ({
  control,
  errors,
  flag = false,
}: {
  control: Control;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="dogSecondaryColor" flag={flag}>
        Secondary Color
      </Label>
      <Controller
        name="dogSecondaryColor"
        control={control}
        render={({ field: { onChange, value } }) => {
          console.log(value);
          return (
            <ComboBoxComponent
              name="dogSecondaryColor"
              options={colors}
              placeholder="Select option..."
              selectedValue={value}
              onValueChange={(name, value) => onChange(value)}
            />
          );
        }}
      />
      {errors.dogSecondaryColor && <Error>Invalid Color</Error>}
    </div>
  );
};

export const DoxBirthDate = ({
  register,
  errors,
  flag = false,
}: {
  register: any;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="dogBirthDate" flag={flag}>
        Date of Birth
      </Label>
      <Input autoComplete="off" type="date" {...register("dogBirthDate")} />
      {errors.dogBirthDate && <Error>Date of birth is required</Error>}
    </div>
  );
};

export const DogAlteration = ({
  dogSex,
  control,
  flag = false,
}: {
  dogSex: "male" | "female";
  control: Control<any>;
  flag?: boolean;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="dogSpayedOrNeutered" flag={flag}>
        {dogSex === "male" ? "Neutered?" : "Spayed?"}
      </Label>
      <Controller
        name="dogSpayedOrNeutered"
        control={control}
        rules={{ required: "Dog alteration status is required" }}
        render={({ field: { onChange, value } }) => (
          <Select
            onValueChange={onChange} // Make sure this triggers a state update in Select
            value={value} // The current value of the field
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={`${"Is Spayed or Neutered?"}`} />
            </SelectTrigger>
            <SelectContent className="z-[9999]">
              <SelectGroup>
                <SelectItem value="yes">Yes</SelectItem>
                <SelectItem value="no">No</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        )}
      />
    </div>
  );
};

export const DogBio = ({
  register,
  errors,
  flag = false,
}: {
  register: any;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="dogBio" flag={flag}>
        Bio (Info is shared if lost)
      </Label>
      <Textarea
        rows={8}
        {...register("dogBio", { required: false })}
        placeholder="Enter any information about your dog here."
      />
    </FieldDiv>
  );
};

export const ExemptLicense = ({ control }: { control: any }) => {
  return (
    <div className="mt-4 flex flex-row items-center gap-2">
      <Controller
        name="licenseExempt"
        control={control}
        render={({ field: { onChange, value } }) => {
          console.log(value);
          return (
            <Checkbox
              id="licenseExempt"
              checked={value}
              onCheckedChange={onChange}
            />
          );
        }}
      />
      <Label className="cursor-pointer" htmlFor="licenseExempt">
        Service Animal
      </Label>
    </div>
  );
};

// Dog Vaccines
export const RabiesVeterinary = ({
  register,
  errors,
  required = true,
  flag = false,
}: {
  register: any;
  errors: any;
  required?: boolean;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="veterinaryName" flag={flag}>
        Veterinary
      </Label>
      <Input
        autoComplete="off"
        {...register("veterinaryName", {
          required: {
            value: required,
            message: "Veterinary is required",
          },
        })}
      />
      {errors?.veterinaryName && <Error>Invalid Veterinary</Error>}
    </FieldDiv>
  );
};

export const RabiesTagNumber = ({
  register,
  errors,
  flag = false,
}: {
  register: any;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="rabiesTagNumber" flag={flag}>
        Rabies Tag Number
      </Label>
      <Input autoComplete="off" {...register("rabiesTagNumber")} />
      {errors?.rabiesTagNumber && <Error>Invalid Rabies Tag Number</Error>}
    </FieldDiv>
  );
};

export const RabiesProducer = ({
  control,
  errors,
  flag = false,
}: {
  control: Control;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="vaccineProducer" flag={flag}>
        Vaccine Producer
      </Label>
      <Controller
        name="vaccineProducer"
        control={control}
        render={({ field: { onChange, value } }) => {
          return (
            <ComboBoxComponent
              name="vaccineProducer"
              options={vaccineProducers}
              placeholder="Select option..."
              selectedValue={value}
              onValueChange={(name, value) => onChange(value)}
            />
          );
        }}
      />
      {errors?.vaccineBrand && <Error>Invalid Vaccine Brand</Error>}
    </FieldDiv>
  );
};

export const RabiesBrand = ({
  control,
  errors,
  flag = false,
}: {
  control: Control;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="vaccineBrand" flag={flag}>
        Vaccine Brand
      </Label>
      <Controller
        name="vaccineBrand"
        control={control}
        render={({ field: { onChange, value } }) => {
          return (
            <ComboBoxComponent
              name="vaccineBrand"
              options={vaccineBrands}
              placeholder="Select option..."
              selectedValue={value}
              onValueChange={(name, value) => onChange(value)}
            />
          );
        }}
      />
      {errors?.vaccineBrand && <Error>Invalid Vaccine Brand</Error>}
    </FieldDiv>
  );
};

export const RabiesAdministeredDate = ({
  register,
  errors,
  flag = false,
}: {
  register: any;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="vaccineAdministeredDate" flag={flag}>
        Vaccine Administered Date
      </Label>
      <Input
        autoComplete="off"
        type="date"
        {...register("vaccineAdministeredDate")}
      />
      {errors?.vaccineAdministeredDate && (
        <Error>Vaccine Administered Date is required</Error>
      )}
    </FieldDiv>
  );
};

export const RabiesDueDate = ({
  register,
  errors,
  flag = false,
}: {
  register: any;
  errors: any;
  flag?: boolean;
}) => {
  console.log(errors?.vaccineDueDate);
  return (
    <FieldDiv>
      <Label htmlFor="vaccineDueDate" flag={flag}>
        Vaccine Due Date
      </Label>
      <Input
        autoComplete="off"
        type="date"
        {...register("vaccineDueDate", {
          validate: (value: string) => {
            if (!value) {
              return "Vaccine Due Date is required";
            }
            const today = new Date();
            const dueDate = new Date(value);
            if (dueDate < today) {
              return "Vaccine Due Date must be in the future";
            }
            return true;
          },
        })}
      />
      {errors?.vaccineDueDate && (
        <Error>
          {errors?.vaccineDueDate?.message || "Vaccine Due Date is required"}
        </Error>
      )}
    </FieldDiv>
  );
};

export const RabiesLotNumber = ({
  register,
  errors,
  flag = false,
}: {
  register: any;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="vaccineLotNumber" flag={flag}>
        Vaccine Lot Number
      </Label>
      <Input autoComplete="off" {...register("vaccineLotNumber")} />
      {errors?.vaccineLotNumber && <Error>Invalid Vaccine Lot Number</Error>}
    </FieldDiv>
  );
};

export const RabiesLotExpirationDate = ({
  register,
  errors,
  flag = false,
}: {
  register: any;
  errors: any;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="vaccineLotExpirationDate" flag={flag}>
        Vaccine Lot Expiration Date
      </Label>
      <Input
        autoComplete="off"
        type="date"
        {...register("vaccineLotExpirationDate")}
      />
      {errors?.vaccineLotExpirationDate && (
        <Error>Invalid Vaccine Lot Expiration Date</Error>
      )}
    </FieldDiv>
  );
};

// Dog Documents
export const DogRabiesVaccinationExemptionDocument = ({
  control,
  required = true,
  flag = false,
}: {
  control: any;
  required: boolean;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="dogRabiesVaccinationExemptionDocument" flag={flag}>
        Vaccination Exemption Document{" "}
      </Label>
      <Controller
        name="dogRabiesVaccinationExemptionDocument"
        control={control}
        rules={{
          required: required,
        }}
        render={({ field: { onChange, value } }) => {
          console.log(value);
          return (
            <FileUpload
              input={{
                className: "",
                id: "dogRabiesVaccinationExemptionDocument",
                label: "",
              }}
              required={required}
              control={control}
            />
          );
        }}
      />
    </FieldDiv>
  );
};

export const ServiceDogLicenseExemptionDocument = ({
  control,
  flag,
  required = true,
}: {
  control: any;
  flag?: boolean;
  required?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="licenseExemptionDocument" flag={flag}>
        Service Dog License Exemption Document
      </Label>
      <Controller
        name="licenseExemptionDocument"
        control={control}
        render={({ field: { onChange, value } }) => {
          console.log(value);
          return (
            <FileUpload
              input={{
                className: "",
                id: "licenseExemptionDocument",
                label: "",
              }}
              required={required}
              control={control}
            />
          );
        }}
      />
    </FieldDiv>
  );
};

export const DogProofOfInsuranceDocument = ({
  control,
  required = true,
  flag,
}: {
  control: Control<any>;
  required: boolean;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="dogProofOfInsuranceDocument" flag={flag}>
        Insurance Records
      </Label>
      <Controller
        name="dogProofOfInsuranceDocument"
        control={control}
        rules={{
          required: required,
        }}
        render={({ field: { onChange, value } }) => {
          return (
            <FileUpload
              input={{
                className: "",
                id: "dogProofOfInsuranceDocument",
                label: "",
              }}
              required={required}
              control={control}
            />
          );
        }}
      />
    </FieldDiv>
  );
};

export const DogRabiesVaccinationDocument = ({
  control,
  required = true,
  flag,
}: {
  control: Control<any>;
  required?: boolean;
  flag?: boolean;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="dogRabiesVaccinationDocument" flag={flag}>
        Vaccination Records
      </Label>
      <Controller
        name="dogRabiesVaccinationDocument"
        control={control}
        rules={{
          required: required,
        }}
        render={({ field: { onChange, value } }) => {
          return (
            <FileUpload
              input={{
                className: "",
                id: "dogRabiesVaccinationDocument",
                label: "",
              }}
              required={required}
              control={control}
            />
          );
        }}
      />
    </FieldDiv>
  );
};

export const DogSpayedOrNeuteredDocument = ({
  control,
  required = false,
  flag,
}: {
  control: Control<any>;
  required?: boolean;
  flag?: boolean;
}) => {
  const variable = "dogSpayedOrNeuteredDocument";

  return (
    <FieldDiv>
      <Label htmlFor={variable} flag={flag}>
        Alteration Records
      </Label>
      <Controller
        name={variable}
        control={control}
        rules={{
          required: required,
        }}
        render={({ field: { onChange, value } }) => {
          return (
            <FileUpload
              input={{
                className: "",
                id: variable,
                label: "",
              }}
              required={required}
              control={control}
            />
          );
        }}
      />
    </FieldDiv>
  );
};

// Errors
export const ErrorMessage = ({ children }: { children: React.ReactNode }) => {
  return <p className="text-sm font-normal text-red-500">{children}</p>;
};

// Individuals
export const IndividualFirstName = ({
  register,
  errors,
}: {
  register: any;
  errors: any;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="firstName">First Name</Label>
      <Input
        autoComplete="off"
        {...register("firstName", { validate: validateName })}
      />
      {errors.firstName && <ErrorMessage>Invalid first name</ErrorMessage>}
    </FieldDiv>
  );
};

export const IndividualMiddleName = ({
  register,
  errors,
}: {
  register: any;
  errors: any;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="middleName">Middle Name</Label>
      <Input
        autoComplete="off"
        {...register("middleName", { validate: validateName, required: false })}
      />
      {errors.middleName && <ErrorMessage>Invalid middle name</ErrorMessage>}
    </FieldDiv>
  );
};

export const IndividualLastName = ({
  register,
  errors,
}: {
  register: any;
  errors: any;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="lastName">Last Name</Label>
      <Input
        autoComplete="off"
        {...register("lastName", { validate: validateName })}
      />
      {errors.lastName && <ErrorMessage>Invalid last name</ErrorMessage>}
    </FieldDiv>
  );
};

export const IndividualDateOfBirth = ({
  register,
  errors,
}: {
  register: any;
  errors: any;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="dateOfBirth">Date of Birth</Label>
      <Input autoComplete="off" type="date" {...register("dateOfBirth")} />
      {errors.dateOfBirth && (
        <ErrorMessage>Date of birth is required</ErrorMessage>
      )}
    </FieldDiv>
  );
};

export const StreetAddress = ({
  register,
  errors,
}: {
  register: any;
  errors: any;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="streetAddress">Street Address</Label>
      <Input autoComplete="off" {...register("streetAddress")} />
      {errors.streetAddress && (
        <ErrorMessage>Invalid Street Address</ErrorMessage>
      )}
    </FieldDiv>
  );
};

export const StreetAddress2 = ({
  register,
  errors,
}: {
  register: any;
  errors: any;
}) => {
  console.log("test");
  return (
    <FieldDiv>
      <Label htmlFor="streetAddress2">
        Apartment, Suite, Building (optional)
      </Label>
      <Input autoComplete="off" {...register("streetAddress2")} />
      {errors.streetAddress2 && (
        <ErrorMessage>Invalid Street Address</ErrorMessage>
      )}
    </FieldDiv>
  );
};

export const City = ({ register, errors }: { register: any; errors: any }) => {
  return (
    <FieldDiv>
      <Label htmlFor="city">City</Label>
      <Input autoComplete="off" {...register("city")} />
      {errors.city && <ErrorMessage>Invalid City</ErrorMessage>}
    </FieldDiv>
  );
};

interface State {
  label: string;
  value: string;
}

const optionClass =
  "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-slate-100 focus:text-slate-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:focus:bg-slate-800 dark:focus:text-slate-50";

export const State = ({ errors, register }: { errors: any; register: any }) => {
  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor="state">State</Label>
      <select
        {...register("state")}
        className={cn(
          "flex h-10 w-full items-center justify-between rounded-md border border-slate-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-slate-500 focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus:ring-slate-300",
        )}
      >
        <option value="" className={optionClass}>
          Select a State
        </option>
        {states.map((state: State) => (
          <option className={optionClass} key={state.value} value={state.value}>
            {state.label}
          </option>
        ))}
      </select>
      {errors.state && <Error>Invalid State</Error>}
    </div>
  );
};

export const ZipCode = ({
  register,
  errors,
}: {
  register: any;
  errors: any;
}) => {
  return (
    <FieldDiv>
      <Label htmlFor="zip">Zip</Label>
      <Input autoComplete="off" {...register("zip")} />
      {errors.zip && <ErrorMessage>Invalid Zip</ErrorMessage>}
    </FieldDiv>
  );
};

export const Emails = ({
  emails,
  register,
  errors,
}: {
  emails: any;
  register: any;
  errors: any;
}) => {
  return emails.map((email: any, index: number) => {
    console.log(email);
    return (
      <FieldDiv key={index}>
        <Label htmlFor="email">{email.group}</Label>
        <Input
          autoComplete="off"
          type="email"
          {...register(`${email.id}`)}
          defaultValue={email.value}
        />
        {errors[email.id] && <ErrorMessage>Email is required</ErrorMessage>}
      </FieldDiv>
    );
  });
};

export const Phones = ({
  phones,
  register,
  errors,
}: {
  phones: any;
  register: any;
  errors: any;
}) => {
  return phones.map((phone: any, index: number) => {
    const { onChange, ...rest } = register(`${phone.id}`);

    return (
      <FieldDiv key={index}>
        <Label htmlFor={`phone-${phone.id}`}>{phone.group}</Label>
        <Input
          autoComplete="off"
          type="phone"
          id={`${phone.id}`}
          defaultValue={formatPhoneNumber(phone.value)}
          onChange={(e) => {
            const formatted = formatPhoneNumber(e.target.value);
            e.target.value = formatted;
            onChange(e);
          }}
          {...rest}
        />
        {errors[phone.id] && <ErrorMessage>Phone is required</ErrorMessage>}
      </FieldDiv>
    );
  });
};
