/* eslint-disable @next/next/no-img-element */
import { AlertDialogFooter } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useState, useCallback, useEffect } from "react";
import { useFeeModalContext } from "./FeeModalContext";
import { useDropzone, FileRejection } from "react-dropzone";
import { Trash, Eye } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Document, Page, pdfjs } from "react-pdf";
import { toast } from "@/components/ui/use-toast";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface DocumentMap {
  [key: string]: File;
}

export default function FeeDocuments() {
  const { setShowDocuments, setFeesDocuments, feesDocuments } =
    useFeeModalContext();

  const [currentDocuments, setCurrentDocuments] = useState<DocumentMap>({});
  const [editFileNames, setEditFileNames] = useState<{ [key: string]: string }>(
    {},
  );
  const [selectedPdf, setSelectedPdf] = useState<string | null>(null);

  useEffect(() => {
    if (
      feesDocuments &&
      typeof feesDocuments === "object" &&
      !Array.isArray(feesDocuments)
    ) {
      setCurrentDocuments(feesDocuments);
      setEditFileNames(
        Object.keys(feesDocuments).reduce(
          (acc, key) => {
            acc[key] = key;
            return acc;
          },
          {} as { [key: string]: string },
        ),
      );
    }
  }, [feesDocuments]);

  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: FileRejection[]) => {
      if (fileRejections.length > 0) {
        toast({
          title: "Invalid File Type",
          description: "Only image files and PDFs are allowed.",
          variant: "destructive",
        });
        return;
      }

      setCurrentDocuments((prevDocs) => {
        const newDocs: DocumentMap = { ...prevDocs };
        acceptedFiles.forEach((file) => {
          const key = `${file.name}-${Date.now()}`;
          newDocs[key] = file;
        });
        return newDocs;
      });

      setEditFileNames((prevNames) => {
        const newNames = { ...prevNames };
        acceptedFiles.forEach((file) => {
          const key = `${file.name}-${Date.now()}`;
          newNames[key] = file.name;
        });
        return newNames;
      });
    },
    [],
  );

  const handleDelete = (key: string) => {
    setCurrentDocuments((prevDocs) => {
      const newDocs = { ...prevDocs };
      delete newDocs[key];
      return newDocs;
    });

    setEditFileNames((prevNames) => {
      const newNames = { ...prevNames };
      delete newNames[key];
      return newNames;
    });
  };

  const handleRenameChange = (key: string, newName: string) => {
    setEditFileNames((prevNames) => ({
      ...prevNames,
      [key]: newName,
    }));
  };
  
  const handleSubmit = () => {
    const updatedDocuments: DocumentMap = {};
  
    Object.keys(currentDocuments).forEach((key) => {
      const file = currentDocuments[key];
      const newName = editFileNames[key] || key;
  
      updatedDocuments[newName] = file; 
    });
  
    setFeesDocuments(updatedDocuments);
    setShowDocuments(false);
  };
  

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [],
      "application/pdf": [],
    },
  });

  return (
    <>
      <div className="rounded-md border bg-gray-50 p-4">
        <div
          {...getRootProps()}
          className="cursor-pointer border-2 border-dashed border-gray-300 p-4 text-center"
        >
          <input {...getInputProps()} />
          <p>
            Drag & drop some files here, or click to select files (Images & PDFs
            only)
          </p>
        </div>
      </div>

      <div className="mt-4">
        <h3 className="font-semibold">Current Documents</h3>
        <ul className="mt-2 rounded-md border p-2">
          {Object.keys(currentDocuments).length === 0 ? (
            <li className="p-2 text-center text-gray-500">No Files</li>
          ) : (
            Object.keys(currentDocuments).map((key) => (
              <li
                key={key}
                className="flex flex-col items-center justify-between border-b p-2"
              >
                <div className="flex w-full items-center gap-4">
                  <Input
                    type="text"
                    value={editFileNames[key] || currentDocuments[key].name}
                    onChange={(e) => handleRenameChange(key, e.target.value)}
                    className="w-full rounded-md border px-2"
                  />

                  {/* Preview Popover for Images */}
                  {currentDocuments[key].type.startsWith("image/") && (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" size="icon">
                          <Eye size={18} />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="p-2">
                        <img
                          src={URL.createObjectURL(currentDocuments[key])}
                          alt={currentDocuments[key].name}
                          className="max-h-60 max-w-full rounded-md"
                        />
                      </PopoverContent>
                    </Popover>
                  )}

                  {/* PDF Preview Button */}
                  {currentDocuments[key].type === "application/pdf" && (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() =>
                            setSelectedPdf(
                              URL.createObjectURL(currentDocuments[key]),
                            )
                          }
                        >
                          <Eye size={18} />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-3xl">
                        {selectedPdf && (
                          <Document file={selectedPdf}>
                            <Page pageNumber={1} />
                          </Document>
                        )}
                      </DialogContent>
                    </Dialog>
                  )}

                  {/* Delete Button */}
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={() => handleDelete(key)}
                  >
                    <Trash size={18} />
                  </Button>
                </div>
                <div className="text-left text-xs italic text-gray-600">
                  {currentDocuments[key].name}
                </div>
              </li>
            ))
          )}
        </ul>
      </div>

      <AlertDialogFooter>
        <Button
          variant="secondary"
          type="button"
          onClick={() => setShowDocuments(false)}
        >
          Cancel
        </Button>
        <Button onClick={handleSubmit} variant="primary" type="button">
          Add Documents
        </Button>
      </AlertDialogFooter>
    </>
  );
}
