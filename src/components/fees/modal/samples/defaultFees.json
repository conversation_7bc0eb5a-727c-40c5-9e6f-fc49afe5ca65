[{"groupName": "dogImpoundment", "label": "Dog Impoundment Fee", "description": "This is used to create a recurring fee for dog impoundment.", "associations": ["dog"], "fees": [{"key": "DOG-M-IMPOUNDMENT-BASE", "feeName": "Dog Impoundment Fee", "operation": "MANUAL", "amount": 80, "entityTypes": ["dog"]}, {"key": "DOG-M-IMPOUNDMENT-RECURRING", "feeName": "Dog Impoundment Recurring Fee", "operation": "MANUAL", "amount": 0, "recurringAmount": 80, "startDate": {"type": "relative", "date": "today", "offset": 0, "offsetBy": "days"}, "endDate": {"type": "relative", "date": "today", "offset": 5, "offsetBy": "days"}, "cronExpression": "0 0 0 * * *", "entityTypes": ["dog"]}]}, {"groupName": "customFees", "label": "Custom Fees", "description": "This is used to create a genertic fee.", "associations": ["individual", "dog", "license"], "fees": [{"key": "CUSTOM-FEE", "feeName": "Custom Fee", "operation": "MANUAL", "amount": 0, "entityTypes": ["individual", "dog", "license"]}]}]