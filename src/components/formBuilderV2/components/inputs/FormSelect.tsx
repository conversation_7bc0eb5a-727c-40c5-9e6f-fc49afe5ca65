import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import InputLabel from "./InputLabel";
import { useGetSettingsByOption } from "@/hooks/api/useAdmin";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

export default function FormSelect({
  input,
  required,
}: {
  input: any;
  required: boolean;
}) {
  const { updateContextValue, getContextValue } = useMachineContext();
  const { errors } = useFormContext();

  const { options, fetchError } = OptionsHandler({ input });
  const currentValue = getContextValue(input.id);

  return (
    <div className={cn("", input?.className)}>
      <div className={cn("flex flex-col gap-1")}>
        <InputLabel
          label={input?.label}
          id={input.id}
          required={required}
          information={input?.information}
        />
        <Select
          onValueChange={(value: string) => {
            updateContextValue(input.id, value);
          }}
          value={normalizeValue(currentValue) ?? null}
          disabled={input?.disabled}
        >
          <SelectTrigger
            className={cn(
              "w-full px-2 py-1 text-base font-normal",
              errors[input.id] && "border border-red-500",
            )}
          >
            <SelectValue>
              {currentValue
                ? options?.find(
                    (option: any) =>
                      normalizeValue(option.value) ===
                      normalizeValue(currentValue),
                  )?.label
                : input.placeholder}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <ScrollArea className="max-h-[300px] overflow-y-auto">
              {fetchError ? (
                <div className="text-red-500">{fetchError}</div>
              ) : (
                options?.map((option: any) => (
                  <SelectItem
                    key={option.value}
                    value={normalizeValue(option.value)}
                  >
                    {option.label}
                  </SelectItem>
                ))
              )}
            </ScrollArea>
          </SelectContent>
        </Select>
        {!required && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              updateContextValue(input.id, null);
            }}
          >
            Clear {input.label}
          </Button>
        )}
      </div>
      {errors[input.id] && (
        <p className="mt-1 text-sm text-red-500">
          {typeof errors[input.id] === 'string' 
            ? errors[input.id] 
            : 'Selection is Required'
          }
        </p>
      )}
    </div>
  );
}

export const normalizeValue = (value: any) => {
  return String(value).trim();
};

export const displayOptions = (
  context: any,
  options: any,
  getContextValue: any,
) => {
  if (!options) return null;

  console.log(options);

  return options.filter((option: any) => {
    if (!option.displayConditions) {
      return true;
    }

    const displayConditionsType =
      option?.displayConditions?.conditionsType ?? "and";

    const resolveContextPlaceholder = (field: string) => {
      const contextPlaceholderRegex = /{{context:\s*(\w+)\.(\w+)\s*}}/;
      const match = field.match(contextPlaceholderRegex);
      if (match) {
        const [_, contextKey, contextSubKey] = match;
        return getContextValue(`${contextKey}.${contextSubKey}`);
      }
      return field;
    };

    if (displayConditionsType === "or") {
      return option.displayConditions.conditions.some((condition: any) => {
        const contextValue = normalizeValue(
          resolveContextPlaceholder(condition.field),
        );
        const normalizedConditionValue = Array.isArray(condition.value)
          ? condition.value.map(normalizeValue)
          : normalizeValue(condition.value);

        if (Array.isArray(normalizedConditionValue)) {
          return normalizedConditionValue.includes(contextValue);
        } else {
          return normalizedConditionValue === contextValue;
        }
      });
    }

    return option.displayConditions.conditions.every((condition: any) => {
      const contextValue = normalizeValue(
        resolveContextPlaceholder(condition.field),
      );
      const normalizedConditionValue = Array.isArray(condition.value)
        ? condition.value.map(normalizeValue)
        : normalizeValue(condition.value);

      if (Array.isArray(normalizedConditionValue)) {
        return normalizedConditionValue.includes(contextValue);
      } else {
        return normalizedConditionValue === contextValue;
      }
    });
  });
};

export function OptionsHandler({ input }: { input: any }) {
  const { context, getContextValue } = useMachineContext();
  const [options, setOptions] = useState<
    { label: string; value: string; [key: string]: any }[]
  >([]);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const dynamicOptionsRegex = /{{settings:\s*(\w+)\.(\w+)\.(\w+)\s*}}/;
  const contextOptionsRegex = /{{context:\s*(\w+)\.(\w+)\s*}}/;

  const isSettingsDynamic =
    typeof input.options === "string" &&
    dynamicOptionsRegex.test(input.options);
  const isContextDynamic =
    typeof input.options === "string" &&
    contextOptionsRegex.test(input.options);

  let category, type, option;
  if (isSettingsDynamic) {
    const matches = input.options.match(dynamicOptionsRegex);
    category = matches?.[1];
    type = matches?.[2];
    option = matches?.[3];
  }

  const { data: fetchedOptions, error: fetchErrorObject } =
    useGetSettingsByOption(
      isSettingsDynamic ? category : undefined,
      isSettingsDynamic ? type : undefined,
      isSettingsDynamic ? option : undefined,
    );

  useEffect(() => {
    let initialOptions = [];

    if (isSettingsDynamic && fetchedOptions) {
      if (fetchErrorObject) {
        setFetchError("Failed to fetch options.");
      } else {
        initialOptions = Array.isArray(fetchedOptions)
          ? fetchedOptions
          : [];
      }
    } else if (isContextDynamic) {
      const contextMatch = input.options.match(contextOptionsRegex);
      const contextKey = contextMatch?.[1];
      const contextField = contextMatch?.[2];
      const contextValue = getContextValue(`${contextKey}.${contextField}`);

      if (Array.isArray(contextValue)) {
        initialOptions = contextValue;
      } else {
        setFetchError("Invalid context value for options.");
        initialOptions = [];
      }
    } else {
      initialOptions = Array.isArray(input.options) ? input.options : [];
    }

    
    // Map options to include `label` and `value` keys
    const labelKey = input.optionValueMap?.label || "label";
    const valueKey = input.optionValueMap?.value || "value";

    const mappedOptions = initialOptions.map((item: any) => ({
      label: item[labelKey],
      value: item[valueKey],
      ...item,
    }));

    // Sort options if `sortBy` is defined
    if (input.sortBy?.column) {
      const { column, order = "asc" } = input.sortBy;
      mappedOptions.sort((a: any, b: any) => {
        const aValue = a[column];
        const bValue = b[column];

        if (aValue < bValue) return order === "asc" ? -1 : 1;
        if (aValue > bValue) return order === "asc" ? 1 : -1;
        return 0;
      });
    }

    setOptions(mappedOptions);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    input.options,
    fetchedOptions,
    fetchErrorObject,
    isSettingsDynamic,
    isContextDynamic,
    getContextValue,
    input.sortBy,
    input.optionValueMap,
  ]);

  const filteredDisplayOptions = Array.isArray(options)
    ? displayOptions(context, options, getContextValue)
    : [];

  return { options: filteredDisplayOptions, fetchError };
}

