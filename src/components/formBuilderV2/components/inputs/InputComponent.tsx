import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { format, isValid, parse, parseISO } from "date-fns";
import InputLabel from "./InputLabel";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";
import { Fields } from "../../types/FormBuilderTypes3";

function formatInput(inputDate: string, inputType: string) {
  if (inputType !== "date") {
    return inputDate;
  }

  let parsedDate = parseISO(inputDate);
  if (!isValid(parsedDate)) {
    const formats = [
      // Date only
      "MM/dd/yyyy",
      "dd/MM/yyyy",
      "yyyy/MM/dd",
      "MM-dd-yyyy",
      "dd-MM-yyyy",
      "yyyy-MM-dd",
      "M/d/yyyy",
      "d/M/yyyy",
      "yyyy/M/d",
      "M-d-yyyy",
      "d-M-yyyy",
      "yyyy-M-d",
      "MM/dd/yy",
      "dd/MM/yy",
      "yy/MM/dd",
      "MM-dd-yy",
      "dd-MM-yy",
      "yy-MM-dd",
      "M/d/yy",
      "d/M/yy",
      "yy/M/d",
      "M-d-yy",
      "d-M-yy",
      "yy-M-d",
      "MMMM d, yyyy",
      "d MMMM, yyyy",
      "MMM d, yyyy",
      "d MMM, yyyy",
      "MMMM d, yy",
      "d MMMM, yy",
      "MMM d, yy",
      "d MMM, yy",
      "yyyy-MMM-dd",
      "yyyy-MMMM-dd",

      // Date and Time
      "MM/dd/yyyy HH:mm",
      "dd/MM/yyyy HH:mm",
      "yyyy/MM/dd HH:mm",
      "MM-dd-yyyy HH:mm",
      "dd-MM-yyyy HH:mm",
      "yyyy-MM-dd HH:mm",
      "M/d/yyyy H:m",
      "d/M/yyyy H:m",
      "yyyy/M/d H:m",
      "M-d-yyyy H:m",
      "d-M-yyyy H:m",
      "yyyy-M-d H:m",
      "MM/dd/yyyy HH:mm:ss",
      "dd/MM/yyyy HH:mm:ss",
      "yyyy/MM/dd HH:mm:ss",
      "MM-dd-yyyy HH:mm:ss",
      "dd-MM-yyyy HH:mm:ss",
      "yyyy-MM-dd HH:mm:ss",
      "M/d/yyyy H:m:s",
      "d/M/yyyy H:m:s",
      "yyyy/M/d H:m:s",
      "M-d-yyyy H:m:s",
      "d-M-yyyy H:m:s",
      "yyyy-M-d H:m:s",
      "MMMM d, yyyy HH:mm",
      "d MMMM, yyyy HH:mm",
      "MMM d, yyyy HH:mm",
      "d MMM, yyyy HH:mm",
      "MMMM d, yyyy HH:mm:ss",
      "d MMMM, yyyy HH:mm:ss",
      "MMM d, yyyy HH:mm:ss",
      "d MMM, yyyy HH:mm:ss",
      "yyyy-MMM-dd HH:mm",
      "yyyy-MMMM-dd HH:mm",
      "yyyy-MMM-dd HH:mm:ss",
      "yyyy-MMMM-dd HH:mm:ss",

      // ISO 8601 Date and Time
      "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
      "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
      "yyyy-MM-dd'T'HH:mm:ssXXX",
      "yyyy-MM-dd'T'HH:mm:ss'Z'",
    ];

    for (const format of formats) {
      parsedDate = parse(inputDate, format, new Date());
      if (isValid(parsedDate)) {
        break;
      }
    }
  }

  if (isValid(parsedDate)) {
    return format(parsedDate, "yyyy-MM-dd");
  }

  return "";
}

export default function InputComponent({
  input,
  required,
}: {
  input: Fields;
  required: boolean;
}) {
  const { updateContextValue, getContextValue } = useMachineContext();
  const { errors } = useFormContext();
  const value = getContextValue(input.id) || "";
  const [debouncedValue, setDebouncedValue] = useState(value);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatInput(e.target.value, input.type);
    setDebouncedValue(formattedValue);
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      if (getContextValue(input.id) !== debouncedValue) {
        updateContextValue(input.id, debouncedValue);
      }
    }, 300);

    return () => {
      clearTimeout(handler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedValue]);

  return (
    <div className={cn("", input?.className)}>
      <div className="flex flex-col gap-1">
        <InputLabel id={input.id} label={input.label} required={required} />
        <input
          type={input?.type}
          value={debouncedValue}
          onChange={handleChange}
          className={cn(
            `w-full rounded-md border border-slate-200 bg-white p-2 disabled:opacity-50`,
            errors[input.id] && "border-red-500",
            input?.disabled && "opacity-50"
          )}
          name={input?.id}
          placeholder={input?.placeholder}
          id={input?.id}
          disabled={input?.disabled}
        />
      </div>
      {errors[input.id] && (
        <p className="text-sm text-red-500">{errors[input.id]}</p>
      )}
    </div>
  );
}
