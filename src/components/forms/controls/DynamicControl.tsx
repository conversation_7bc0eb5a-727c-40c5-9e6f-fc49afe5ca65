import { RegisterOptions, useFormContext } from "react-hook-form";
import { DynamicInputData, SelectData } from "./dynamicControlTypes";
import { ErrorMessage } from "@hookform/error-message";
import CustomCreatableSelectSingle from "../formbuilder/components/form/CustomCreatableSelectSingle";

function isSelectData(data: DynamicInputData): data is SelectData {
  return data.type === "select";
}

interface DynamicControlProps {
  inputData: DynamicInputData;
  optionalPage?: boolean;
}

// Make first letter of each word uppercase
const toCamelCase = (word: string) => {
  return word.charAt(0).toUpperCase() + word.slice(1);
};

const camel2title = (camelCase: string) =>
  camelCase
    .replace(/([A-Z])/g, (match) => ` ${match}`)
    .replace(/^./, (match) => match.toUpperCase())
    .trim();

const formatPhoneNumber = (value: string) => {
  if (!value) return value;

  const phoneNumber = value.replace(/[^\d]/g, "");
  const phoneNumberLength = phoneNumber.length;

  if (phoneNumberLength < 4) return phoneNumber;

  if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  }

  return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(
    3,
    6
  )}-${phoneNumber.slice(6, 10)}`;
};

export const DynamicControl = ({
  inputData,
  optionalPage,
}: DynamicControlProps) => {
  const {
    type,
    fieldName,
    defaultValue,
    required,
    minValue,
    minLength,
    pattern,
    maxValue,
    maxLength,
    config = {},
  } = inputData;

  const {
    register,
    setValue,
    formState: { errors },
    watch,
  } = useFormContext();

  const tailwindClasses = `border border-gray-300 rounded px-2 py-1 w-full ${
    errors[fieldName] && "border-red-500"
  }`;

  // console.log(required)
  // console.log(errors)

  const validationOptions: RegisterOptions = {
    ...config,

    ...(optionalPage === false && {
      required: {
        value: required?.value === true,
        message:
          required?.message === ""
            ? `${camel2title(fieldName)} is required`
            : required?.message ?? `${camel2title(fieldName)} is required`,
      },
    }),
  };

  if (typeof minValue?.value === "number") {
    validationOptions.min = {
      value: minValue?.value,
      message: minValue?.message ?? "",
    };
  }

  if (typeof maxValue?.value === "number") {
    validationOptions.max = {
      value: maxValue?.value,
      message: maxValue?.message ?? "",
    };
  }

  if (typeof minLength?.value === "number") {
    validationOptions.minLength = {
      value: minLength?.value,
      message: minLength?.message ?? "",
    };
  }

  if (typeof maxLength?.value === "number") {
    validationOptions.maxLength = {
      value: maxLength?.value,
      message: maxLength?.message ?? "",
    };
  }

  if (typeof pattern?.value === "string") {
    validationOptions.pattern = {
      value: new RegExp(pattern?.value),
      message: pattern?.message ?? "",
    };
  }

  switch (type) {
    case "tel":
      const telValue = watch(fieldName);
      return (
        <>
          <input
            type="tel"
            {...register(fieldName, validationOptions)}
            defaultValue={defaultValue as string | number | undefined}
            className={tailwindClasses}
            onChange={(e) => {
              setValue(fieldName, formatPhoneNumber(e.target.value), {
                shouldValidate: true,
              });
            }}
          />
          <ErrorMessage
            errors={errors}
            name={fieldName}
            render={({ message }) => (
              <p className="text-red-500 text-xs mt-1">{message}</p>
            )}
          />
        </>
      );

    case "select": {
      const options = isSelectData(inputData) ? inputData.options : [];
      const selectValue = watch(fieldName);
      return (
        <>
          <select
            {...register(fieldName, validationOptions)}
            defaultValue={defaultValue as string | undefined}
            name={fieldName}
            id={fieldName}
            className={tailwindClasses}
            onChange={(e) => {
              setValue(fieldName, e.target.value, { shouldValidate: true });
            }}
          >
            {options.map((o, index) => (
              <option key={index} value={o.value}>
                {o.label}
              </option>
            ))}
          </select>

          {/* <CustomCreatableSelectSingle
            {...register(fieldName, validationOptions)}
            options={options}
            value={{
              value: defaultValue as string,
              label: defaultValue as string,
            }}
            onChange={(e: any) => {
              setValue(fieldName, e.value, { shouldValidate: true });
            }}
            lock={false}
          /> */}

          <ErrorMessage
            errors={errors}
            name={fieldName}
            render={({ message }) => (
              <p className="text-red-500 text-xs mt-1">{message}</p>
            )}
          />
        </>
      );
    }

    case "number":
      const numberValue = watch(fieldName);
      return (
        <>
          <input
            type="number"
            {...register(fieldName, validationOptions)}
            defaultValue={defaultValue as string | number | undefined}
            className={tailwindClasses}
            onChange={(e) => {
              setValue(fieldName, e.target.value, { shouldValidate: true });
            }}
          />
          <ErrorMessage
            errors={errors}
            name={fieldName}
            render={({ message }) => (
              <p className="text-red-500 text-xs mt-1">{message}</p>
            )}
          />
        </>
      );

    case "checkbox":
      const checkboxValue = watch(fieldName);
      return (
        <div className="py-0.5 mr-1">
          <input
            type="checkbox"
            {...register(fieldName, validationOptions)}
            defaultChecked={defaultValue === "true"}
            className={" "}
            onChange={(e) => {
              setValue(fieldName, e.target.checked, { shouldValidate: true });
            }}
          />
          <ErrorMessage
            errors={errors}
            name={fieldName}
            render={({ message }) => (
              <p className="text-red-500 text-xs mt-1">{message}</p>
            )}
          />
        </div>
      );

    case "date":
      const dateValue = watch(fieldName);

      // check if fieldName contains any variation of "dateofbirth"
      const containsDOB = [
        "dateofbirth",
        "dob",
        "vaccineAdministeredDate",
      ].some((str) => fieldName.toLowerCase().includes(str));
      const currentDate =
        containsDOB && dateValue === ""
          ? new Date().toISOString().split("T")[0]
          : undefined;

      return (
        <>
          <input
            type="date"
            {...register(fieldName, validationOptions)}
            defaultChecked={defaultValue as boolean}
            className={tailwindClasses}
            // max={currentDate}
            onChange={(e) => {
              setValue(fieldName, e.target.value, { shouldValidate: true });
            }}
          />
          <ErrorMessage
            errors={errors}
            name={fieldName}
            render={({ message }) => (
              <p className="text-red-500 text-xs mt-1">{message}</p>
            )}
          />
        </>
      );

    case "email":
      const emailValue = watch(fieldName);
      return (
        <>
          <input
            type="email"
            {...register(fieldName, validationOptions)}
            defaultValue={defaultValue as string | number | undefined}
            className={tailwindClasses}
            onChange={(e) => {
              setValue(fieldName, e.target.value, {
                shouldValidate: true,
              });
            }}
          />
          <ErrorMessage
            errors={errors}
            name={fieldName}
            render={({ message }) => (
              <p className="text-red-500 text-xs mt-1">{message}</p>
            )}
          />
        </>
      );

    default:
      // console.log(type);
      return (
        <>
          <input
            type="text"
            {...register(fieldName, validationOptions)}
            defaultValue={defaultValue as string | number | undefined}
            className={tailwindClasses}
            onChange={(e) => {
              setValue(fieldName, toCamelCase(e.target.value), {
                shouldValidate: true,
              });
            }}
          />
          <ErrorMessage
            errors={errors}
            name={fieldName}
            render={({ message }) => (
              <p className="text-red-500 text-xs mt-1">{message}</p>
            )}
          />
        </>
      );
  }
};
