"use client";

import { useAtom } from "jotai";
import { currentStepAtom } from "@/atoms/CreateFormAtoms";

type Step = {
  step: number;
  title: string;
};

interface ProgressBarProps {
  steps: Step[];
  currentStep: number;
}

const ProgressBar = ({ steps, currentStep }: ProgressBarProps) => {
  // Atoms
  // const [currentStep] = useAtom(currentStepAtom);
  const maxLength = steps.length;

  return (
    <div className="flex flex-col justify-center items-center shrink-0">
      <div className="bg-neutral-300 w-full h-1">
        <div
          className="
          bg-gradient-to-r from-blue-400 to-blue-600 h-full"
          style={{
            width: `${((Number(currentStep) || 0) / maxLength) * 100}%`,
          }}
        ></div>
      </div>
    </div>
  );
};

export default ProgressBar;
