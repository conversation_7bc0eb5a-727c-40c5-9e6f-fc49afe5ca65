import { useCallback, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import FileDisplay from "../fileDisplay/FileDisplay";
import FormInputs from "./FormInputs";
import NextBackButtons from "@/components/forms/formbuilder/components/NextBackButtons";
import ProgressTabs from "../ProgressTabs";
import { useFormBuilder } from "../../hooks/useFormBuilder";
import { useToast } from "@/components/ui/use-toast";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";

type FormViewProps = {
  form: any;
  page?: any;
};

const mapSections = (sections: any[]) =>
  sections?.map((section: { title: string; elements: any[] }) =>
    section.title !== null
      ? section.elements.map(mapElements)
      : section.elements.map(mapHiddenElements)
  );

const mapElements = (
  element: { type: string; defaultValue: any },
  index: number
) => (
  <div
    key={`element-${element.type}-${index}`}
    dangerouslySetInnerHTML={{
      __html: element.type === "html" ? element.defaultValue : "",
    }}
  />
);

const mapHiddenElements = (
  element: { type: string; defaultValue: any; fieldName: string },
  index: number
) => (
  <div
    key={`element-${element.fieldName}-${index}`}
    className={element.type === "conditionalCheckbox" ? "hidden" : ""}
    dangerouslySetInnerHTML={{
      __html: element.defaultValue,
    }}
  />
);

const FormView = ({ form }: FormViewProps) => {
  const {
    filesArray,
    divStyles,
    sectionHasFileDisplay,
    nextStep,
    previousStep,
    onSubmit,
    getPage,
    getApiLoading,
    getIsLastPage,
    getCurrentPage,
    getProgressBarSteps,
    changeCurrentPage,
    sectionHasFieldDisplay,
  } = useFormBuilder(form);

  const page = getPage();
  const optionalPage = page?.optional;

  const { handleSubmit, trigger, getValues, setError } = useFormContext();
  const currentPage = getCurrentPage();
  const tabSteps = getProgressBarSteps();

  const [, setToast] = useAtom(toastAtom);

  const handleSetCurrentPage = useCallback(
    (index: number) => {
      if (index > currentPage) {
        changeCurrentPage("next");
      } else if (index < currentPage) {
        changeCurrentPage("prev");
      }
    },
    [currentPage, changeCurrentPage]
  );

  const mappedSections = useMemo(
    () => mapSections(page?.sections),
    [page?.sections]
  );

  // Check if form has "searchBuilder" as a type of element
  const hasSearchBuilder = page?.sections?.some((section: any) =>
    section.elements.some((element: any) => element.type === "searchBuilder")
  );

  // Get the "searchBuilder" elements fieldNames
  const searchBuilderFieldNames = page?.sections
    ?.map((section: any) =>
      section.elements
        .filter((element: any) => element.type === "searchBuilder")
        .map((element: any) => element.fieldName)
    )
    .flat();

  // Function to check if there is a value in the "searchBuilder" elements fieldNames
  const hasSearchBuilderValue = (values: any) =>
    searchBuilderFieldNames.some((fieldName: string) => values[fieldName]);

  const style =
    sectionHasFileDisplay(page?.sections) ||
    sectionHasFieldDisplay(page?.sections)
      ? "flex flex-col"
      : "";

  return (
    <>
      <ProgressTabs
        currentStep={currentPage}
        steps={tabSteps}
        setCurrentStep={handleSetCurrentPage}
      />

      <div className="h-full overflow-auto w-full bg-white">
        <div className={divStyles()}>
          <div className={style}>
            {/* Page Title & Description */}
            <div className="text-center pb-10">
              <h1 className="text-3xl mt-2">{page?.title}</h1>
              <div className="text-center text-small italics text-neutral-600 mt-2">
                {mappedSections}
              </div>
            </div>

            {/* Page Sections */}
            <div className="w-full px-4">
              {page?.sections?.map((section: any, index: number) => (
                <FormInputs
                  optionalPage={optionalPage}
                  section={section}
                  key={`section-${section.title}-${index}`}
                />
              ))}
            </div>
          </div>

          {/* File Display */}
          {sectionHasFileDisplay(page?.sections) && filesArray.length > 0 && (
            <div
              className={
                sectionHasFileDisplay(page?.sections)
                  ? " w-full max-h-full"
                  : "hidden"
              }
            >
              <FileDisplay files={filesArray} />
            </div>
          )}
        </div>
      </div>

      {/* Loading Spinner */}
      {getApiLoading() && (
        <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 max-w-full w-auto rounded border border-black bg-white p-4 shadow-xl text-center">
          <h2 className="font-bold text-4xl">Loading...</h2>
          <div className="w-full text-center flex justify-center">
            <LoadingSpinner />
          </div>
          <p>Processing Request please wait</p>
        </div>
      )}

      {/* Buttons */}
      <NextBackButtons
        loading={getApiLoading()}
        nextText={currentPage === form?.pages?.length - 1 ? "Submit" : "Next"}
        progressBar
        currentStep={currentPage}
        steps={tabSteps}
        backButton={previousStep}
        nextButton={() => {
          if (hasSearchBuilder && !hasSearchBuilderValue(getValues())) {
            searchBuilderFieldNames.forEach((fieldName: string) => {
              setError(fieldName, {
                type: "manual",
                message: "Must select a resident to continue.",
              });
            });

            setToast({
              status: "info",
              label: "No Resident Selected",
              message: "Must select a resident to continue.",
              position: "top-right",
            });
          } else {
            getIsLastPage() ? handleSubmit(onSubmit)() : nextStep();
          }
        }}
      />
    </>
  );
};

export default FormView;
