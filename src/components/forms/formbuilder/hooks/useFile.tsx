import { useFormContext } from "react-hook-form";

type UseCustomFileProps = {
  fieldName: string;
  file: File | null;
};

const useCustomFile = ({ fieldName, file }: UseCustomFileProps) => {
  const { register, setValue, unregister } = useFormContext();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setValue(fieldName, selectedFile);
    }
  };

  const removeFile = () => {
    unregister(fieldName);
  };

  return {
    register: register(fieldName),
    handleFileChange,
    removeFile,
    file,
  };
};

export default useCustomFile;
