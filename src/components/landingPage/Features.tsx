import {
  BarChart4,
  Clock,
  Database,
  FileText,
  Shield,
  Users,
  CheckCircle,
  ArrowRight,
} from "lucide-react";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

export default function Features() {
  const [activeFeature, setActiveFeature] = useState(0);
  const { push } = useRouter();

  const features = [
    {
      icon: <FileText className="h-6 w-6 text-white" />,
      title: "Simple Applications",
      description:
        "Fill out applications online with easy-to-use forms that guide you through each step.",
      color: "bg-blue-600",
      stats: "Apply from anywhere, anytime",
      benefit: "No more paper forms or trips to city hall",
    },
    {
      icon: <Clock className="h-6 w-6 text-white" />,
      title: "Fast Results",
      description:
        "Get your permits and licenses processed quickly with automated workflows.",
      color: "bg-green-600",
      stats: "Results in days, not weeks",
      benefit: "Track your application status in real-time",
    },
    {
      icon: <Database className="h-6 w-6 text-white" />,
      title: "All Services in One Place",
      description:
        "Access all your local services through one convenient online platform.",
      color: "bg-purple-600",
      stats: "One account for everything",
      benefit: "Never lose track of your applications again",
    },
    {
      icon: <Users className="h-6 w-6 text-white" />,
      title: "24/7 Access",
      description:
        "Submit applications, check status, and manage your account whenever it's convenient for you.",
      color: "bg-amber-600",
      stats: "Available around the clock",
      benefit: "No more waiting for office hours",
    },
    {
      icon: <BarChart4 className="h-6 w-6 text-white" />,
      title: "Clear Status Updates",
      description:
        "Always know where your application stands with real-time notifications and updates.",
      color: "bg-red-600",
      stats: "Instant status notifications",
      benefit: "Stay informed every step of the way",
    },
    {
      icon: <Shield className="h-6 w-6 text-white" />,
      title: "Secure & Private",
      description:
        "Your personal information is protected with bank-level security and privacy standards.",
      color: "bg-indigo-600",
      stats: "Bank-level security",
      benefit: "Your data is safe and private",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
    },
  };

  return (
    <section
      className="bg-gradient-to-b from-white to-slate-50 py-16 md:py-32"
      id="features"
    >
      <div className="container mx-auto max-w-7xl px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mb-16 flex flex-col items-center text-center"
        >
          <span className="mb-4 rounded-full bg-blue-100 px-4 py-1 text-sm font-medium text-blue-800">
            POWERFUL FEATURES
          </span>
          <h2 className="bg-gradient-to-r from-blue-800 to-indigo-600 bg-clip-text text-3xl font-bold text-transparent md:text-5xl">
            Why Residents Love Us
          </h2>
          <p className="mx-auto mt-6 max-w-2xl text-xl text-gray-600">
            Discover how our platform makes getting local services easier, faster,
            and more convenient than ever before.
          </p>
        </motion.div>

        {/* Feature Showcase */}
        <div className="mb-16 grid grid-cols-1 gap-8 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="overflow-hidden rounded-xl bg-white shadow-xl"
          >
            <div className={`p-8 ${features[activeFeature].color}`}>
              <div className="mb-4 flex items-center">
                <div className="mr-4 rounded-lg bg-white bg-opacity-20 p-3">
                  {features[activeFeature].icon}
                </div>
                <h3 className="text-2xl font-bold text-white">
                  {features[activeFeature].title}
                </h3>
              </div>
              <p className="mb-4 text-lg text-white">
                {features[activeFeature].description}
              </p>
              <div className="mt-6 rounded-lg bg-white bg-opacity-10 p-4">
                <div className="mb-3 flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5 text-white" />
                  <p className="font-semibold text-white">
                    {features[activeFeature].stats}
                  </p>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5 text-white" />
                  <p className="font-semibold text-white">
                    {features[activeFeature].benefit}
                  </p>
                </div>
              </div>
            </div>
            {/* <div className="p-6">
              <button className="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-semibold flex items-center justify-center">
                See This Feature in Action
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
            </div> */}
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="flex flex-col justify-center"
          >
            <h3 className="mb-6 text-2xl font-bold">
              Choose a feature to explore
            </h3>
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="grid grid-cols-1 gap-4 md:grid-cols-2"
            >
              {features.map((feature, index) => (
                <motion.button
                  key={index}
                  variants={itemVariants}
                  onClick={() => setActiveFeature(index)}
                  className={`flex items-center rounded-lg border p-4 transition-all duration-300 ${
                    activeFeature === index
                      ? `${feature.color} border-transparent text-white`
                      : "border-gray-200 bg-white hover:border-gray-300"
                  }`}
                >
                  <div
                    className={`mr-3 rounded-lg p-2 ${
                      activeFeature === index
                        ? "bg-white bg-opacity-20"
                        : feature.color
                    }`}
                  >
                    {React.cloneElement(feature.icon, {
                      className: `h-5 w-5 ${activeFeature === index ? "text-white" : "text-white"}`,
                    })}
                  </div>
                  <span className="font-medium">{feature.title}</span>
                </motion.button>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Social Proof */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="rounded-xl border border-gray-100 bg-white p-8 shadow-lg"
        >
          <div className="flex flex-col items-center justify-between md:flex-row">
            <div className="mb-6 md:mb-0">
              <h3 className="mb-2 text-xl font-bold">
                Ready to make your local services easier?
              </h3>
              <p className="text-gray-600">
                Join thousands of residents who&apos;ve already simplified their
                local service experience
              </p>
            </div>
            <div className="flex flex-col gap-4 md:flex-row">
              <button
                onClick={() => push("/signup")}
                className="flex items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-3 font-semibold text-white"
              >
                Get Started Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
              <button 
                onClick={() => push("/login")}
                className="rounded-lg border border-gray-300 px-6 py-3 font-semibold text-gray-700"
              >
                Sign In
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
