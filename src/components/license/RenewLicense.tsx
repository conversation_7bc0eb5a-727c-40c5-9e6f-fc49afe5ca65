"use client";

import React from "react";
import Image from "next/image";
import Modal from "@/components/modal/Modal";
import { useRouter } from "next/navigation";
import { useParams, usePathname } from "next/navigation";

const Card = ({
  title,
  description,
  image,
  type = "default",
  disabled = false,
  link = null,
}: {
  title: string;
  description: string;
  image: string;
  type?: "normalDog" | "purebredDog" | "default";
  disabled?: boolean;
  link?: string | null;
}) => {
  const types = {
    normalDog: "bg-orange-400",
    purebredDog: "bg-orange-400",
    default: "bg-slate-400",
  };

  const router = useRouter();

  return (
    <button
      disabled={disabled}
      className={`${
        disabled && "opacity-50"
      } w-full rounded-lg shadow overflow-hidden`}
      onClick={() => {
        if (link) router.push(link);
      }}
    >
      <div className={`${types[type]} h-6 p-1 z-10`}></div>
      <div className="p-2 mt-4 text-left h-full">
        <div className="absolute h-10 w-10 rounded-full border-2 border-white flex items-center justify-center z-10 transform -translate-y-10 bg-orange-50 overflow-hidden">
          {image ? (
            <Image
              src={image}
              alt={title}
              fill
              style={{
                objectFit: "contain",
              }}
            />
          ) : (
            <div className="bg-neutral-600 w-full h-full"></div>
          )}
        </div>
        <h3 className="font-semibold line-clamp-1">{title}</h3>
        <p className="text-sm text-neutral-700 line-clamp-2">{description}</p>
      </div>
    </button>
  );
};

const RenewLicense = ({
  isOpen,
  onClose,
  entityId,
  formType,
}: {
  isOpen: boolean;
  onClose: () => void;
  entityId: string;
  formType: "full" | "partial";
}) => {
  const pathname = usePathname();

  const licenseTypes = {
    dog: {
      normal: encodeURIComponent("dogLicense"),
      purebred: encodeURIComponent("purebredDogLicense"),
      formType: encodeURIComponent(formType),
    },
  };

  

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Renew a License">
      <div className="grid grid-cols-3 gap-6">
        <Card
          title="Dog License"
          description="Max of 3 Dog Licenses per resident."
          image="/images/icons/dog.png"
          type="normalDog"
          link={`/license/dogLicenses/renew?entityId=${entityId}&entityType=${licenseTypes.dog.normal}&formType=${licenseTypes.dog.formType}&returnTo=${pathname}`}
        />
        <Card
          title="Purebred Dog License"
          description="Unlimited licenses with proof."
          image="/images/icons/dog.png"
          type="purebredDog"
          link={`/license/dogLicenses/renew?entityId=${entityId}&entityType=${licenseTypes.dog.purebred}&formType=${licenseTypes.dog.formType}&returnTo=${pathname}`}
        />
      </div>
    </Modal>
  );
};

export default RenewLicense;
