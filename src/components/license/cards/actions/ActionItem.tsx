import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/Tooltip";
import { IconType } from "react-icons";

type Action = {
  label: string;
  Icon: IconType;
  onClick: () => void;
  disabled?: boolean;
  loading?: boolean;
  error?: boolean;
};

export const ActionItem = ({
  Icon,
  label,
  onClick,
  disabled = true,
  error = false,
  loading = false,
}: Action) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger className="w-full">
          <button
            className={`
              w-full text-2xl flex items-center justify-center py-3 
              ${
                loading
                  ? "text-yellow-600 cursor-not-allowed bg-yellow-50/10 font-semibold"
                  : error
                  ? "text-red-200 bg-red-800 cursor-not-allowed"
                  : disabled
                  ? "text-neutral-300 cursor-not-allowed hover:bg-red-500/50 opacity-50"
                  : "text-neutral-300 hover:text-white hover:bg-white/10   transition-all border-neutral-400  hover:border-clerk-primary"
              }
            `}
            onClick={onClick}
            disabled={disabled}
          >
            {loading ? (
              <LoadingSpinner className="!my-0 w-6 h-6" />
            ) : (
              <Icon />
            )}{" "}
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
