import Button from "@/components/ui/buttons/Button";
import React from "react";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { usePathname } from "next/navigation";
import { useGetProfile } from "@/hooks/api/useProfiles";

const Title = ({ children }: { children: string }) => {
  return (
    <div className="mb-1 flex flex-col gap-2">
      <h3 className="text-xl font-bold text-neutral-800">{children}</h3>
    </div>
  );
};

const DogInformation = () => {
  const router = useRouter();
  const pathname = usePathname();
  const {
    entityId,
    entitytype,
    realm,
  }: {
    entityId: string;
    entitytype: string;
    realm: string;
  } = useParams();


  const { data, isLoading, isError, error } = useGetProfile(
    entitytype as string,
    entityId as string,
  );

  if (!entityId) return <div>loading...</div>;

  console.log(entityId);
  console.log(data);

  return (
    <div className="flex flex-col gap-10">
      <div>
        <Title>Dog Information:</Title>
        <p className="">Dog information goes here</p>
      </div>

      <div>
        <Title>Transfer of Ownership:</Title>
        <Button
          variant="danger"
          onClick={() => {
            router.push(`
              /entity/dog/transferOfOwnership?entityId=${entityId}&entityType=dog&returnTo=${pathname}
            `);
          }}
        >
          Start Transfer
        </Button>
      </div>
    </div>
  );
};

export default DogInformation;
