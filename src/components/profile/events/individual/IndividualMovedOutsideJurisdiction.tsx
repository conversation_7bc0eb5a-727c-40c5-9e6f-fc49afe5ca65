import EventDialog from "../EventDialog";

const IndividualMovedOutsideJurisdiction = ({
  event,
  entityType,
  entityId,
}: {
  event: any;
  entityType: string;
  entityId: string;
}) => {
  return (
    <EventDialog
      event={event}
      title="You are about to mark this individual as moved outside jurisdiction."
      description="Are you sure you want to do this?"
      entityId={entityId}
      entityType={entityType}
    />
  );
};

export default IndividualMovedOutsideJurisdiction;
