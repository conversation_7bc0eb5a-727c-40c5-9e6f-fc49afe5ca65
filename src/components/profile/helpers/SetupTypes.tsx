import { SelectHTMLAttributes } from "react";

export interface ProfileSectionProps {
  label: string;
  children: React.ReactNode;
  onSubmit: (data: any) => void;
  saving: boolean;
  hideEdit?: boolean;
  showHideMessage?: boolean;
}

export interface ProfileGridProps {
  children: React.ReactNode;
  cols: number;
}

export interface ProfileLabelProps {
  children: React.ReactNode;
  field: string | null;
  flagged?: boolean;
  noFlag?: boolean;
}

export interface ProfileGridItemProps {
  label: string;
  field?: string | null;
  children: React.ReactNode;
  span?: number;
  required?: boolean;

  // Flag Items
  flagged?: boolean;
  flaggedText?: string;
  noFlag?: boolean;
}

// Fields

export interface ProfileInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string; // Field name for the input
  type?:
    | "text"
    | "email"
    | "password"
    | "number"
    | "tel"
    | "url"
    | "search"
    | "date"; 
  placeholder?: string;
  required?: boolean;
  permissions?: string[] | null; 
  validate?: { pattern?: RegExp };
  errorMessage?: { required?: string; pattern?: string };
}

export interface ProfileDateInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string;
  formatPattern?: string;
  inputPattern?: string;
  required?: boolean;
  permissions?: string[] | null;
  validate?: {
    [key: string]: (value: string) => true | string;
  };
}

export interface PhoneInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string;
  permissions?: string[] | null;
}

export interface ProfileCustomSelectInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string;
  options: { value: string | boolean; label: string }[];
  required?: boolean;
  permissions?: string[] | null;
}

export interface ProfileSelectInputProps
  extends SelectHTMLAttributes<HTMLSelectElement> {
  name: string;
  required?: boolean;
  options: { label: string; value: string | boolean }[];
  permissions?: string[] | null;
}

export interface ProfileCheckboxInputProps {
  name: string;
  label: string;
  required?: boolean;
  permissions?: string[] | null;
}

export interface ProfileDocumentProps {
  name: string;
  accept: (".pdf" | ".jpeg" | ".png" | ".jpg")[];
  required: boolean;
  permissions?: string[] | null;
}

export interface ProfileOTPInputProps {
  name: string;
  required: boolean;
  length: number;
  permissions?: string[] | null;
  placeholder?: string;
}
