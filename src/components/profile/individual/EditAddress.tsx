import { useForm } from "react-hook-form";
import { Address } from "@/types/AddressType";
import {
  useUpdateIndividualAddress,
  useUpdateResidentAddress,
} from "@/hooks/api/useProfiles";
import { useEffect, useState } from "react";
import { useAtom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import {
  EditModal,
  EditModalFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import {
  City,
  Comment,
  State,
  StreetAddress,
  StreetAddress2,
  ZipCode,
} from "@/components/dialog/EditFields";
import { cn } from "@/lib/utils";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

type FormValues = {
  streetAddress?: string;
  streetAddress2?: string;
  city?: string;
  state?: string;
  zip?: string;
  comment?: string;
  participantAddressTypeId?: number | null;
  participantAddressId?: number | null;
  participantAddressType?: string;
};

const initializeAddress = (address: Address | undefined, type: string) => {
  return {
    participantAddressTypeId: address?.participantAddressTypeId ?? null,
    participantAddressId: address?.participantAddressId ?? null,
    participantAddressType: type,
    streetAddress: address?.streetAddress ?? "",
    streetAddress2: address?.streetAddress2 ?? "",
    city: address?.city ?? "",
    state: address?.state ?? "",
    zip: address?.zip ?? "",
  };
};

const EditAddress = ({
  address,
  entityId,
  refetch,
  updateAddress,
  setAddresses,
  setCurrentAddress,
  setIsOpen
}: {
  address: FormValues;
  entityId: string;
  refetch: any;
  updateAddress: any;
  setAddresses: any;
  setCurrentAddress: any;
  setIsOpen: any;
}) => {
  console.log(address);
  const [, setToast] = useAtom(toastAtom);
  const [initialValues, setInitialValues] = useState<FormValues>(address);

  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<FormValues>({
    defaultValues: address,
    mode: "onChange",
  });

  const handleCancel = () => {
    setIsOpen(false);
    reset();
  };

  const handleSave = (data: any) => {
    const formData = createFormData({
      ...data,
      addressType: data.participantAddressType,
    });

    const send = permitted
      ? { entityId: entityId, body: formData }
      : { body: formData };
    updateAddress.mutate(send, {
      onSuccess: (res: any) => {
        const results = res.addresses.find(
          (a: any) => a.participantAddressType === data.participantAddressType,
        );

        console.log(results);

        const newData = {
          participantAddressTypeId: results.addressType,
          participantAddressId: results.participantAddressId,
          participantAddressType: results.participantAddressType,
          streetAddress: results.streetAddress,
          streetAddress2: results.streetAddress2,
          city: results.city,
          state: results.state,
          zip: results.zip,
        };

        setInitialValues(newData);
        setAddresses((prev: FormValues[]) => {
          const updatedAddresses = prev.map((a: FormValues) => {
            if (a.participantAddressType === data.participantAddressType) {
              return { ...a, ...newData };
            }
            return a;
          });
          return updatedAddresses;
        });
        setCurrentAddress(newData);
        setToast({
          status: "success",
          label: "Address Updated",
          message: "Successfully updated address.",
        });
        refetch();
      },
      onError: (error: any) => {
        console.log(error);
        setToast({
          status: "error",
          label: "Error Updating Address",
          message:
            error?.response?.data?.message ||
            "An error occurred while updating the address.",
        });
      },
    });
  };

  useEffect(() => {
    reset({
      participantAddressTypeId: address.participantAddressTypeId,
      participantAddressId: address.participantAddressId,
      participantAddressType: address.participantAddressType,
      streetAddress: address.streetAddress,
      streetAddress2: address.streetAddress2 || "",
      city: address.city,
      state: address.state,
      zip: address.zip,
      comment: "",
    });
  }, [address, reset]);

  return (
    <form
      className="flex w-full flex-col gap-4"
      onSubmit={handleSubmit(handleSave)}
    >
      <div className="mb-2 border-b text-xl font-semibold text-neutral-800">
        {address?.participantAddressType}
      </div>
      <StreetAddress register={register} errors={errors} />
      <StreetAddress2 register={register} errors={errors} />
      <City register={register} errors={errors} />
      <State register={register} errors={errors} />
      <ZipCode register={register} errors={errors} />
      <Comment register={register} />
      <EditModalFooter
        handleClose={handleCancel}
        disabled={!isDirty}
        loading={updateAddress.isLoading}
      />
    </form>
  );
};

const AddressButton = ({
  address,
  setCurrentAddress,
  setCurrentType,
  currentType,
}: {
  address: Address;
  setCurrentAddress: any;
  setCurrentType: any;
  currentType: string;
}) => {

  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);

  return (
    <div
      key={address.participantAddressId}
      onClick={() => {
        console.log(address);
        setCurrentAddress(address);
        setCurrentType(address.participantAddressType);
      }}
      className={cn(
        "flex cursor-pointer flex-col rounded-sm p-2",
        address.participantAddressType === currentType
          ? "bg-blue-100"
          : "hover:bg-neutral-100",
      )}
    >
      <div className="mb-1 text-sm font-semibold text-neutral-800">
        {address.participantAddressType}:
      </div>
      <div className="text-sm">
        {address.streetAddress ? (
          <div>
            <div>{address.streetAddress}</div>
            {address.streetAddress2 && <div>{address.streetAddress2}</div>}
            <div>
              {address.city}, {address.state} {address.zip}
            </div>
          </div>
        ) : (
          <div className="text-neutral-600">No Address</div>
        )}
      </div>
    </div>
  );
};

export const EditAddresses = ({
  address,
  entityId,
  refetch,
}: {
  address: Address[];
  entityId: string;
  refetch: any;
}) => {
  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);
  const addressTypes: string[] = ["Home", "Mailing", "Work"];
  const convertedAddresses = addressTypes.map((type: string) => {
    const foundAddress = address?.find(
      (address: Address) => address.participantAddressType === type,
    );
    return initializeAddress(foundAddress, type);
  });

  const [addresses, setAddresses] = useState<FormValues[]>(convertedAddresses);
  const [currentType, setCurrentType] = useState<string>(
    convertedAddresses[0].participantAddressType,
  );
  const [currentAddress, setCurrentAddress] = useState<FormValues>(
    convertedAddresses[0],
  );
  const [isOpen, setIsOpen] = useState(false);

  const updateClerkAddress = useUpdateIndividualAddress();
  const updateResidentAddress = useUpdateResidentAddress();
  const updateAddress = permitted ? updateClerkAddress : updateResidentAddress;

  return (
    <EditModal
      title="Edit Address"
      handleClose={() => {
        setIsOpen(false);
        setCurrentAddress(addresses[0]);
        setCurrentType(addressTypes[0]);
      }}
      handleOpen={() => {
        setIsOpen(true);
      }}
      isOpen={isOpen}
    >
      {isOpen && (
        <div className="flex h-full w-full gap-4 ">
          <div className="flex h-full w-48 shrink-0 flex-col gap-4 border-r">
            <h3 className="text-base text-neutral-700">Choose Address</h3>
            <div className="mb-auto flex h-full flex-col gap-2 overflow-auto text-base">
              {addresses.map((a: any) => (
                <AddressButton
                  key={a.participantAddressId}
                  address={a}
                  setCurrentAddress={setCurrentAddress}
                  setCurrentType={setCurrentType}
                  currentType={currentType}
                />
              ))}
            </div>
          </div>

          <EditAddress
            setAddresses={setAddresses}
            setCurrentAddress={setCurrentAddress}
            address={currentAddress}
            entityId={entityId}
            refetch={refetch}
            updateAddress={updateAddress}
            setIsOpen={setIsOpen}
          />
        </div>
      )}
    </EditModal>
  );
};

export default EditAddresses;
