import { format } from "date-fns";
import { BiRefresh, BiTrash } from "react-icons/bi";
import { CgFileDocument } from "react-icons/cg";
import {
  downloadListAtom,
  removeFromList,
  downloadReport,
} from "./scripts/downloadCenterUtils";
import { useAtom } from "jotai";
import { toastAtom } from "../ui/toast/toast";

const status: {
  [key: string]: string;
} = {
  COMPLETED: "text-green-500",
  FAILED: "text-red-500",
  PENDING: "text-yellow-500",
};

const Title = ({ title }: { title: string }) => {
  return <div className="text-sm font-semibold">{title ?? "No Title"}</div>;
};

const SecondaryText = ({ children }: { children: React.ReactNode }) => {
  return <div className="text-xs flex gap-2 w-full">{children}</div>;
};

const RemoveButton = ({ reportId }: { reportId: string }) => {
  const [downloadList, setDownloadList] = useAtom(downloadListAtom);
  return (
    <button
      onClick={(e) => {
        e.stopPropagation();
        console.log("removing");
        const removedList = removeFromList(downloadList, reportId);
        setDownloadList(removedList);
      }}
      className="flex items-center gap-2 p-1 rounded-full hover:bg-red-100 group"
    >
      <BiTrash className="text-red-500 text-xl group-hover:text-red-600 group-hover:opacity-100 opacity-50" />
    </button>
  );
};

const cursorTable:{ [key:string]:string } = {
  "COMPLETED": "cursor-pointer",
  "PENDING": "cursor-wait",
  "FAILED": "cursor-not-allowed",
}

const ReportComponent = ({ report }: any) => {
  const [, setToast] = useAtom(toastAtom);
  const date = report.updatedAt
    ? format(new Date(report.updatedAt), "MM/dd - hh:mm a")
    : "No Date";

  return (
    <div
      key={report.reportId}
      className={`
        ${cursorTable[report.status] ?? "cursor-default"}
        flex gap-2 rounded hover:bg-blue-100  w-full p-1 items-center
      `}
      onClick={() => {
        if (report.status === "COMPLETED" && report?.documentId) {
          console.log(report);
          try {
            downloadReport(report?.documentId);
          } catch (e) {
            setToast({
              status: "error",
              label: "Error downloading report",
              message: "Please try again later or contact your administrator for assistance.",
            });
            console.log(e);
          }
        }
        console.log("clicked");
      }}
    >
      {report.status === "PENDING" ? (
        <BiRefresh className="text-2xl text-neutral-500 animate-spin" />
      ) : (
        <CgFileDocument className="text-2xl text-neutral-500" />
      )}
      <div className="flex flex-col w-full">
        <Title title={report.title} />
        <SecondaryText>
          <div
            className={`
              ${status[report.status] ?? "text-neutral-500"}
            `}
          >
            {report.status}
          </div>
          <div className="text-neutral-500">{date}</div>
        </SecondaryText>
      </div>
      <RemoveButton reportId={report.reportId} />
    </div>
  );
};

export default ReportComponent;
