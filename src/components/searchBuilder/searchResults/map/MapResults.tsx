"use client";
import { useState } from "react";
import GoogleMapWrapper from "./GoogleMapWrapper";
import GoogleMaps from "./GoogleMaps";
import MapResult from "./MapResult";

type MapResultsProps = {
  entityId: string;
  entityType: string;
  avatarUrl: string;
  primaryDisplay: string;
  latitude: string;
  thirdDisplay: any[];
  secondaryDisplay: string;
  longitude: string;
}

const MapResults = ({ results }: { results: MapResultsProps[] }) => {
  const [activeEntityId, setActiveEntityId] = useState<any>(
    results[0].entityId
  );

  return (
    <div className="
      flex flex-col w-full h-full relative overflow-hidden gap-4
      lg:flex-row
    ">
      <div
        className="
          order-2
          flex flex-col gap-4 bottom-0 h-[50%] w-full overflow-auto shrink-0
          lg:order-1
          lg:max-w-md
        "
      >
        {results && results.map((result) => (
          <MapResult
            key={result.entityId}
            result={result}
            activeEntityId={activeEntityId}
            setActiveEntityId={setActiveEntityId}
          />
        ))}
      </div>
      <div className="
        order-1 h-full w-full rounded overflow-hidden
        lg:order-2
      ">
        <GoogleMapWrapper>
          <GoogleMaps activeEntityId={activeEntityId} results={results} />
        </GoogleMapWrapper>
      </div>
    </div>
  );
};

export default MapResults;
