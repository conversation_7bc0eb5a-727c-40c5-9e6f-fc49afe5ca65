import { useEffect, useState } from "react";
import { useGetActiveTenant } from "@/hooks/api/useRealm";

const RECENT_ITEMS_SYNC_EVENT = 'recentItemsSync';

const saveToLocalStorage = (key: string, items: any) => {
  if (typeof window !== "undefined") {
    localStorage.setItem(key, JSON.stringify(items));
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent(RECENT_ITEMS_SYNC_EVENT, { detail: { key, items } }));
    }, 0);
  }
};

const loadFromLocalStorage = (key: string) => {
  if (typeof window !== "undefined") {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  }
  return [];
};

export type RecentItem = {
  displayName: string;
  entityId: string;
  link?: string;
  entityType: string;
  avatarUrl?: string | null;
  date: Date | string;
  pinned: boolean;
};

const isValidRecentItemArray = (items: any): items is RecentItem[] => {
  if (!Array.isArray(items)) {
    return false;
  }
  
  const isValid = items.every((item, index) => {
    const valid = typeof item.entityId === "string" &&
      typeof item.displayName === "string" &&
      typeof item.entityType === "string" &&
      (typeof item.date === "string" || item.date instanceof Date) &&
      typeof item.pinned === "boolean";
    
    return valid;
  });

  return isValid;
};

const useRecentItems = () => {
  const { data: activeTenant, isLoading } = useGetActiveTenant();

  const tenantKey = activeTenant && !isLoading
    ? `recentItems_${activeTenant.id}_recentItems`
    : "recentItems_default_recentItems";

  const [recentItems, setRecentItems] = useState<RecentItem[]>(() => {
    const loadedItems = loadFromLocalStorage(tenantKey);
    return isValidRecentItemArray(loadedItems) ? loadedItems.slice(0, 10) : [];
  });

  useEffect(() => {
    if (activeTenant && !isLoading) {
      const tenantLoadedItems = loadFromLocalStorage(tenantKey);
      setRecentItems(isValidRecentItemArray(tenantLoadedItems) ? tenantLoadedItems.slice(0, 10) : []);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTenant, tenantKey]);

  useEffect(() => {
    const handleSync = (event: CustomEvent) => {
      const { key, items } = event.detail;
      if (key === tenantKey && tenantKey && items) {
        setRecentItems(isValidRecentItemArray(items) ? items.slice(0, 10) : []);
      }
    };

    if (tenantKey) {
      window.addEventListener(RECENT_ITEMS_SYNC_EVENT, handleSync as EventListener);
    }
    
    return () => {
      if (tenantKey) {
        window.removeEventListener(RECENT_ITEMS_SYNC_EVENT, handleSync as EventListener);
      }
    };
  }, [tenantKey]);

  const addToRecent = (item: RecentItem) => {
    const newItems = recentItems.filter((i) => i.entityId !== item.entityId);
    const updatedItems = [item, ...newItems];
    const sortedItems = sortRecentItems(updatedItems);
    const limitedItems = sortedItems.slice(0, 10);

    saveToLocalStorage(tenantKey, limitedItems);
    setRecentItems(limitedItems);
  };

  const removeItem = (itemId: string) => {
    const newItems = recentItems.filter((item) => item.entityId !== itemId);
    saveToLocalStorage(tenantKey, newItems);
    setRecentItems(newItems);
  };

  const removeAll = () => {
    saveToLocalStorage(tenantKey, []);
    setRecentItems([]);
  };

  const handleBookmark = (itemId: string) => {
    const updatedItems = recentItems.map((item) => {
      if (item.entityId === itemId) {
        return {
          ...item,
          pinned: !item.pinned,
        };
      }
      return item;
    });

    const sortedItems = sortRecentItems(updatedItems);
    const limitedItems = sortedItems.slice(0, 10);

    saveToLocalStorage(tenantKey, limitedItems);
    setRecentItems(limitedItems);
  };

  const sortRecentItems = (items: RecentItem[]) =>
    items.sort(
      (a, b) =>
        Number(b.pinned) - Number(a.pinned) ||
        new Date(b.date).getTime() - new Date(a.date).getTime()
    );

  return {
    recentItems,
    addToRecent,
    removeItem,
    handleBookmark,
    removeAll,
  };
};

export default useRecentItems;
