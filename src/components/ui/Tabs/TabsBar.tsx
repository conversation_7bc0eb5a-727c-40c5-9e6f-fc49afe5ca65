"use client"
import React from 'react'
import { motion } from 'framer-motion'
import Button from '../buttons/Button';

type Options = {
  section: string;
  value: string;
}

type Variant = 'primary' | 'secondary' | 'warning' | 'danger' | 'success' | 'info' | 'light' | 'dark' | 'neutral'

const colors = {
  primary: 'bg-blue-300 text-blue-900',
  secondary: 'bg-gray-300 text-gray-700',
  warning: 'bg-yellow-300 text-yellow-900',
  danger: 'bg-red-300 text-red-900',
  success: 'bg-green-300 text-green-900',
  info: 'bg-blue-300 text-blue-900',
  light: 'bg-gray-300 text-gray-700',
  dark: 'bg-gray-300 text-gray-700',
  neutral: 'bg-gray-300 text-gray-700',
}


const TabsBar = ({
  options,
  activeRadio,
  setActiveRadio,
  variant = 'secondary',
  buttonLabel = null,
  buttonOnClick,
  buttonDisabled = false
}:{
  options: Options[];
  activeRadio: string | null;
  setActiveRadio: React.Dispatch<React.SetStateAction<string | null>>;
  variant?: Variant;
  buttonLabel?: string | null;
  buttonOnClick?: () => void;
  buttonDisabled?: boolean;
}) => {
  return (
    <div className='flex justify-between items-center gap-4 mb-6'>
      <motion.div className='flex border border-neutral-300 rounded overflow-hidden w-fit'>
        {options && options.map((option:any) => {
          return (
            <label key={option.value} 
              htmlFor={option.value} 
              className={`
                px-4 py-2 cursor-pointer text-sm 
                ${activeRadio === option.value 
                  ? `${colors[variant]} font-semibold` 
                  : 'bg-gray-50 text-gray-700 border-r'
                }
              `}
            >
            <input
              value={option.value}
              type="radio"
              name="searchType"
              id={option.value}
              onChange={() => {
                setActiveRadio(option.value);
              }}
              className="hidden"
              checked={activeRadio === option.value}
            />
              {option.section}
            </label>
          )
        })}
      </motion.div>
      {buttonLabel &&
        <div className='shrink-0'>
          <Button
            variant='primary'
            size='sm'
            onClick={buttonOnClick}
            disabled={buttonDisabled}
          >
            {buttonLabel}
          </Button>
        </div>
      }
    </div>
  )
}

export default TabsBar
