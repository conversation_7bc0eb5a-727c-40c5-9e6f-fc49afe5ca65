import React from "react";

const styles = {
  base: "font-semibold rounded cursor-pointer shrink-0 transition-all duration-200 ease-in-out",
  size: {
    xs: "text-xs py-1 px-2",
    sm: "text-sm py-2 px-3",
    md: "text-base py-2 px-4",
    lg: "text-lg py-2 px-4",
    xl: "text-xl py-2 px-4",
  },
  variant: {
    primary: "bg-clerk-primary text-white hover:bg-blue-700 active:bg-blue-900",
    secondary: "bg-gray-500 text-white hover:bg-gray-700 active:bg-gray-900",
    warning:
      "bg-yellow-500 text-white hover:bg-yellow-700 active:bg-yellow-900",
    danger: "bg-red-500 text-white hover:bg-red-700 active:bg-red-900",
    success: "bg-green-500 text-white hover:bg-green-700 active:bg-green-900",
  },
  outline: {
    primary:
      "text-clerk-primary border border-blue-500 hover:bg-blue-100 active:bg-blue-200",
    secondary:
      "text-gray-500 border border-gray-500 hover:bg-gray-100 active:bg-gray-200",
    warning:
      "text-yellow-500 border border-yellow-500 hover:bg-yellow-100 active:bg-yellow-200",
    danger:
      "text-red-500 border border-red-500 hover:bg-red-100 active:bg-red-200",
    success:
      "text-green-500 border border-green-500 hover:bg-green-100 active:bg-green-200",
  },
  fullWidth: "w-full",
  disabled: "opacity-50 cursor-not-allowed",
};

const Button = ({
  children,
  onClick,
  variant = "primary",
  disabled = false,
  size = "md",
  type = "button",
  outline = false,
  fullWidth = false,
  ariaLabel,
  loading = false,
  className = "",
}: {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: keyof typeof styles.variant;
  disabled?: boolean;
  size?: keyof typeof styles.size;
  type?: "button" | "submit" | "reset";
  outline?: boolean;
  fullWidth?: boolean;
  ariaLabel?: string;
  loading?: boolean;
  className?: string;
}) => {
  const sizeStyle = styles.size[size];
  const variantStyle = outline
    ? styles.outline[variant] || styles.outline.primary
    : styles.variant[variant] || styles.variant.primary;
  const disabledStyle = disabled || loading ? styles.disabled : "";
  const fullWidthStyle = fullWidth ? styles.fullWidth : "";

  return (
    <button
      className={`${styles.base} ${sizeStyle} ${variantStyle} ${disabledStyle} ${fullWidthStyle} ${className}`}
      onClick={onClick}
      disabled={disabled || loading}
      type={type}
      aria-label={ariaLabel}
      role="button"
    >
      {loading ? "Loading..." : children}
    </button>
  );
};

export default Button;
