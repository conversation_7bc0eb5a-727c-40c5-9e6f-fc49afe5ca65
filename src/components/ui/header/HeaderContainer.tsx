import React from "react";

const BGColors: {
  [key: string]: string;
} = {
  blue: "bg-gradient-to-br from-blue-500 to-blue-600 text-blue-100",
  green: "bg-gradient-to-br from-teal-700 to-emerald-600 text-teal-100",
  red: "bg-gradient-to-br from-red-500 to-red-600 text-red-100",
  yellow: "bg-gradient-to-br from-yellow-500 to-yellow-600 text-yellow-100",
  purple: "bg-gradient-to-br from-purple-500 to-purple-600 text-purple-100",
  pink: "bg-gradient-to-br from-pink-500 to-pink-600 text-pink-100",
  indigo: "bg-gradient-to-br from-indigo-500 to-indigo-600 text-indigo-100",
  teal: "bg-gradient-to-br from-teal-500 to-teal-600 text-teal-100",
  neutral: "bg-gradient-to-br from-neutral-500 to-neutral-600 text-neutral-100",
};

const HeaderContainer = ({
  color = "blue",
  label,
  description,
  children,
}: {
  color?: string;
  label: string;
  description?: string;
  children: React.ReactNode;
}) => {
  return (
    <div className={`${BGColors[color]}  p-6 shadow`}>
      <div className="mb-2">
        <h1 className="text-2xl font-semibold mb-2 text-white">{label}</h1>
        <h3 className="text-sm">{description}</h3>
      </div>
      {children}
    </div>
  );
};

export default HeaderContainer;
