"use client";

import { protectedNoRedirect, requests } from "@/utils/agent";
import { useMutation, useQuery } from "@tanstack/react-query";

export type PublicCodeLookUpResponse = {
  code: string;
  action: "register" | "lookup" | "renew" | "create" | null;
  entityType: "license" | "individual" | "tag" | "dog";
  entityId: string | null;
  realm: string;
  retry: 2;
};

// Get Code Details
export const useGetPublicCodeDetails = (code: string) => {
  return useQuery({
    queryKey: ["publicCode", code],
    queryFn: () =>
      protectedNoRedirect.get<PublicCodeLookUpResponse>(
        `/license/public/code-lookup/${code}`,
      ),
    enabled: !!code,
    retry: 1
  });
};

export type FoundDog = {
  dog: {
    uuid: string;
    dogName: string;
    dogSex: string;
    dogBreed: string;
    dogBio: string;
    dogBirthDate: string;
    dogPrimaryColor: string;
    dogSecondaryColor?: string | null;
    lost: boolean;
    avatar?: string | null;
    lastSeen: {
      location: {
        name: string;
        datetime: string;
        location?: string;
        latitude?: string;
        longitude?: string;
      };
      reportedBy?: {
        name: string;
        email: string;
        phone: string;
      };
    }[];
  };
  license: {
    licenseStatus: string;
  };
  contacts: FoundContact[];
  sightings: SightingData[];
};


export type FoundContact = {
  public?: false;
  name: string | null;
  email: string | null;
  phone: string | null;
  website?: {
    link: string;
    label: string;
  };
};

// Get Public Dog Details
export const useGetPublicProfileDetails = (
  entityType: string,
  entityId: string,
  realm: string
) => {
  return useQuery({
    queryKey: ["publicProfileEntity", entityType, entityId],
    queryFn: () =>
      requests.get<FoundDog>(
        `/license/public/profile/${entityType}/${entityId}/${realm}`,
      ),
    enabled: !!entityId && !!entityType,
  });
};

export interface SightingData {
  location: {
    name: string;
    datetime: string;
    location: string;
    latitude: string | null;
    longitude: string | null;
  };
  reportedBy: {
    name: string;
    email: string;
    phone: string;
  };
}

interface SightingRequest {
  entityId: string;
  entityType: string;
  data: SightingData;
}

// Create Sighting
export const useCreateSighting = () => {
  return useMutation(({ entityId, entityType, data }: SightingRequest) =>
    requests.post(`/license/public/${entityType}/report-sighting/${entityId}`, data)
  );
};