export const newDogLicenseForm = {
  name: "newDogLicenseForm",
  description: "Form used to create a new dog license",
  pages: [
    {
      title: "Dog Information",
      optional: false,
      sortOrder: 3,
      conditionallyDisplay: [],
      sections: [
        {
          title: "Dog Information",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Dog's Name",
              description: null,
              fieldName: "dogName",
              type: "text",
              defaultValue: "",
              sortOrder: 1,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 100,
                message: null,
              },
              maxValue: null,
              pattern: {
                value: "^[A-Za-z0-9\\s]*$",
                message: "Only letters, numbers, and spaces are allowed.",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "dogName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant",
                  columnName: "participant_type_group_id",
                  value: 7,
                },
              ],
            },
            {
              label: "Tag Number",
              description: null,
              fieldName: "tagNumber",
              type: "text",
              defaultValue: "",
              sortOrder: 2,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 100,
                message: "Tag number cannot be more than 100 characters.",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z0-9 ]*$",
                message: "Only letters, numbers, and spaces are allowed.",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "tagNumber",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Breed",
              description: null,
              fieldName: "dogBreed",
              type: "select",
              defaultValue: "",
              sortOrder: 3,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter the breed of the dog.",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 100,
                message: "Breed cannot be more than 100 characters.",
              },
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "*New Breed (No Desc.)",
                  value: "*New Breed (No Desc.)",
                  sortOrder: 2,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Abyssinian Sand Terrier",
                  value: "Abyssinian Sand Terrier",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Aberdeen Terrier",
                  value: "Aberdeen Terrier",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Affenpinscher",
                  value: "Affenpinscher",
                  sortOrder: 4,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Afghan Hound",
                  value: "Afghan Hound",
                  sortOrder: 5,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "African Wild Dog",
                  value: "African Wild Dog",
                  sortOrder: 6,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Aidi",
                  value: "Aidi",
                  sortOrder: 7,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ainu Dog",
                  value: "Ainu Dog",
                  sortOrder: 8,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Airedale Terrier",
                  value: "Airedale Terrier",
                  sortOrder: 9,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Akbash Dog",
                  value: "Akbash Dog",
                  sortOrder: 10,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Akita",
                  value: "Akita",
                  sortOrder: 11,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alano Español",
                  value: "Alano Español",
                  sortOrder: 12,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alapaha Blue Blood Bulldog",
                  value: "Alapaha Blue Blood Bulldog",
                  sortOrder: 13,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alaskan Husky",
                  value: "Alaskan Husky",
                  sortOrder: 14,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alaskan Klee Kai",
                  value: "Alaskan Klee Kai",
                  sortOrder: 15,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alaskan Malamute",
                  value: "Alaskan Malamute",
                  sortOrder: 16,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alopekis",
                  value: "Alopekis",
                  sortOrder: 17,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alpine Dachsbracke",
                  value: "Alpine Dachsbracke",
                  sortOrder: 18,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Alpine Spaniel",
                  value: "Alpine Spaniel",
                  sortOrder: 19,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Altman White English Bulldog",
                  value: "Altman White English Bulldog",
                  sortOrder: 20,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Akita",
                  value: "American Akita",
                  sortOrder: 21,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Bandogge Mastiff",
                  value: "American Bandogge Mastiff",
                  sortOrder: 22,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Blue Gascon Hound",
                  value: "American Blue Gascon Hound",
                  sortOrder: 23,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Bulldog",
                  value: "American Bulldog",
                  sortOrder: 24,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Bullnese",
                  value: "American Bullnese",
                  sortOrder: 25,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Bully",
                  value: "American Bully",
                  sortOrder: 26,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Cocker Spaniel",
                  value: "American Cocker Spaniel",
                  sortOrder: 27,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Crested Sand Terrier",
                  value: "American Crested Sand Terrier",
                  sortOrder: 28,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American English Coonhound",
                  value: "American English Coonhound",
                  sortOrder: 29,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Eskimo Dog",
                  value: "American Eskimo Dog",
                  sortOrder: 30,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Eskimo- minature",
                  value: "American Eskimo- minature",
                  sortOrder: 31,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Eskimo- toy",
                  value: "American Eskimo- toy",
                  sortOrder: 32,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Foxhound",
                  value: "American Foxhound",
                  sortOrder: 33,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Hairless Terrier",
                  value: "American Hairless Terrier",
                  sortOrder: 34,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Indian Dog",
                  value: "American Indian Dog",
                  sortOrder: 35,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Lo-Sze Pugg",
                  value: "American Lo-Sze Pugg",
                  sortOrder: 36,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Mastiff (Flying W)",
                  value: "American Mastiff (Flying W)",
                  sortOrder: 37,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Mastiff (Panja)",
                  value: "American Mastiff (Panja)",
                  sortOrder: 38,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Pit Bull Terrier",
                  value: "American Pit Bull Terrier",
                  sortOrder: 39,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Staffordshire Terrier",
                  value: "American Staffordshire Terrier",
                  sortOrder: 40,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Staghound",
                  value: "American Staghound",
                  sortOrder: 41,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "American Water Spaniel",
                  value: "American Water Spaniel",
                  sortOrder: 42,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Anatolian Mastiff",
                  value: "Anatolian Mastiff",
                  sortOrder: 43,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Anatolian Shepherd Dog",
                  value: "Anatolian Shepherd Dog",
                  sortOrder: 44,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Andalusian Mouse-Hunting Dog",
                  value: "Andalusian Mouse-Hunting Dog",
                  sortOrder: 45,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Anglo-Francais de Moyen Venerie",
                  value: "Anglo-Francais de Moyen Venerie",
                  sortOrder: 46,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Anglo-Francais de Petite Venerie",
                  value: "Anglo-Francais de Petite Venerie",
                  sortOrder: 47,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Anglo-Français, Grand",
                  value: "Anglo-Français, Grand",
                  sortOrder: 48,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Antebellum Bulldog",
                  value: "Antebellum Bulldog",
                  sortOrder: 49,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Appenzeller Sennenhunde",
                  value: "Appenzeller Sennenhunde",
                  sortOrder: 50,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Argentine Dogo",
                  value: "Argentine Dogo",
                  sortOrder: 51,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ariege Pointer",
                  value: "Ariege Pointer",
                  sortOrder: 52,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ariégeois",
                  value: "Ariégeois",
                  sortOrder: 53,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Armant",
                  value: "Armant",
                  sortOrder: 54,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Armenian Gampr dog",
                  value: "Armenian Gampr dog",
                  sortOrder: 55,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Artois Hound",
                  value: "Artois Hound",
                  sortOrder: 56,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Arubian Cunucu Dog",
                  value: "Arubian Cunucu Dog",
                  sortOrder: 57,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Aryan Molossus",
                  value: "Aryan Molossus",
                  sortOrder: 58,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Bandog",
                  value: "Australian Bandog",
                  sortOrder: 59,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Bulldog",
                  value: "Australian Bulldog",
                  sortOrder: 60,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Cattle Dog",
                  value: "Australian Cattle Dog",
                  sortOrder: 61,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Greyhound",
                  value: "Australian Greyhound",
                  sortOrder: 62,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Kelpie",
                  value: "Australian Kelpie",
                  sortOrder: 63,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Shepherd",
                  value: "Australian Shepherd",
                  sortOrder: 64,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Shepherd- Minature",
                  value: "Australian Shepherd- Minature",
                  sortOrder: 65,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Shepherd- Miniature",
                  value: "Australian Shepherd- Miniature",
                  sortOrder: 66,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Silky Terrier",
                  value: "Australian Silky Terrier",
                  sortOrder: 67,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Stumpy Tail Cattle Dog",
                  value: "Australian Stumpy Tail Cattle Dog",
                  sortOrder: 68,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Australian Terrier",
                  value: "Australian Terrier",
                  sortOrder: 69,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Austrian Black and Tan Hound",
                  value: "Austrian Black and Tan Hound",
                  sortOrder: 70,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Austrian Brandlbracke",
                  value: "Austrian Brandlbracke",
                  sortOrder: 71,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Austrian Pinscher",
                  value: "Austrian Pinscher",
                  sortOrder: 72,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Azawakh",
                  value: "Azawakh",
                  sortOrder: 73,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bakharwal Dog",
                  value: "Bakharwal Dog",
                  sortOrder: 74,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Balkan Hound",
                  value: "Balkan Hound",
                  sortOrder: 75,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Banjara Greyhound",
                  value: "Banjara Greyhound",
                  sortOrder: 76,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Barbet",
                  value: "Barbet",
                  sortOrder: 77,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Basenji",
                  value: "Basenji",
                  sortOrder: 78,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Basque Shepherd Dog",
                  value: "Basque Shepherd Dog",
                  sortOrder: 79,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Basset Artesien Normand",
                  value: "Basset Artesien Normand",
                  sortOrder: 80,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Basset Bleu de Gascogne",
                  value: "Basset Bleu de Gascogne",
                  sortOrder: 81,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Basset Fauve de Bretagne",
                  value: "Basset Fauve de Bretagne",
                  sortOrder: 82,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Basset Griffon Vendéen",
                  value: "Basset Griffon Vendéen",
                  sortOrder: 83,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Basset Hound",
                  value: "Basset Hound",
                  sortOrder: 84,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bavarian Mountain Hound",
                  value: "Bavarian Mountain Hound",
                  sortOrder: 85,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Beagle",
                  value: "Beagle",
                  sortOrder: 86,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Beagle Mix",
                  value: "Beagle Mix",
                  sortOrder: 87,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Beagle-Harrier",
                  value: "Beagle-Harrier",
                  sortOrder: 88,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bearded Collie",
                  value: "Bearded Collie",
                  sortOrder: 89,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Beauceron",
                  value: "Beauceron",
                  sortOrder: 90,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bedlington Terrier",
                  value: "Bedlington Terrier",
                  sortOrder: 91,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bedouin Shepherd Dog",
                  value: "Bedouin Shepherd Dog",
                  sortOrder: 92,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Griffon",
                  value: "Belgian Griffon",
                  sortOrder: 93,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Mastiff",
                  value: "Belgian Mastiff",
                  sortOrder: 94,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Sheepdog",
                  value: "Belgian Sheepdog",
                  sortOrder: 95,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Shepherd Dog (Groenendael)",
                  value: "Belgian Shepherd Dog (Groenendael)",
                  sortOrder: 96,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Shepherd Dog (Laekenois)",
                  value: "Belgian Shepherd Dog (Laekenois)",
                  sortOrder: 97,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Shepherd Dog (Malinois)",
                  value: "Belgian Shepherd Dog (Malinois)",
                  sortOrder: 98,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Shepherd Dog (Tervueren)",
                  value: "Belgian Shepherd Dog (Tervueren)",
                  sortOrder: 99,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgian Shorthaired Pointer",
                  value: "Belgian Shorthaired Pointer",
                  sortOrder: 100,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Belgrade Terrier",
                  value: "Belgrade Terrier",
                  sortOrder: 101,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Benchlegged Feist",
                  value: "Benchlegged Feist",
                  sortOrder: 102,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bergamasco",
                  value: "Bergamasco",
                  sortOrder: 103,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Berger Blanc Suisse",
                  value: "Berger Blanc Suisse",
                  sortOrder: 104,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Berger De Pyrenees,Sm.Muzzled",
                  value: "Berger De Pyrenees,Sm.Muzzled",
                  sortOrder: 105,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Berger Picard",
                  value: "Berger Picard",
                  sortOrder: 106,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Berner Laufhund",
                  value: "Berner Laufhund",
                  sortOrder: 107,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Berner Neiderlaufhund",
                  value: "Berner Neiderlaufhund",
                  sortOrder: 108,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bernese Mountain Dog",
                  value: "Bernese Mountain Dog",
                  sortOrder: 109,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bichon Avanese",
                  value: "Bichon Avanese",
                  sortOrder: 110,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bichon Frise",
                  value: "Bichon Frise",
                  sortOrder: 111,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Billy",
                  value: "Billy",
                  sortOrder: 112,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bisben",
                  value: "Bisben",
                  sortOrder: 113,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black and Tan Coonhound",
                  value: "Black and Tan Coonhound",
                  sortOrder: 114,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black and Tan Virginia Foxhound",
                  value: "Black and Tan Virginia Foxhound",
                  sortOrder: 115,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black Forest Hound",
                  value: "Black Forest Hound",
                  sortOrder: 116,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black Mouth Cur",
                  value: "Black Mouth Cur",
                  sortOrder: 117,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black Norwegian Elkhound",
                  value: "Black Norwegian Elkhound",
                  sortOrder: 118,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black Russian Terrier",
                  value: "Black Russian Terrier",
                  sortOrder: 119,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Blackmouth Cur",
                  value: "Blackmouth Cur",
                  sortOrder: 120,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bleu de Gascogne, Grand",
                  value: "Bleu de Gascogne, Grand",
                  sortOrder: 121,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bleu de Gascogne, Petit",
                  value: "Bleu de Gascogne, Petit",
                  sortOrder: 122,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bloodhound",
                  value: "Bloodhound",
                  sortOrder: 123,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Blue Lacy Game Dog",
                  value: "Blue Lacy Game Dog",
                  sortOrder: 124,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Blue Paul Terrier",
                  value: "Blue Paul Terrier",
                  sortOrder: 125,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Blue Picardy Spaniel",
                  value: "Blue Picardy Spaniel",
                  sortOrder: 126,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bluetick Coonhound",
                  value: "Bluetick Coonhound",
                  sortOrder: 127,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Boerboel",
                  value: "Boerboel",
                  sortOrder: 128,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bohemian Shepherd",
                  value: "Bohemian Shepherd",
                  sortOrder: 129,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bolognese",
                  value: "Bolognese",
                  sortOrder: 130,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Border Collie",
                  value: "Border Collie",
                  sortOrder: 131,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Border Terrier",
                  value: "Border Terrier",
                  sortOrder: 132,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Borzoi",
                  value: "Borzoi",
                  sortOrder: 133,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bosnian Roughhaired Hound",
                  value: "Bosnian Roughhaired Hound",
                  sortOrder: 134,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Boston Terrier",
                  value: "Boston Terrier",
                  sortOrder: 135,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bouvier des Ardennes",
                  value: "Bouvier des Ardennes",
                  sortOrder: 136,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bouvier des Flandres",
                  value: "Bouvier des Flandres",
                  sortOrder: 137,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Boxer",
                  value: "Boxer",
                  sortOrder: 138,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Boxer Mix",
                  value: "Boxer Mix",
                  sortOrder: 139,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Boykin Spaniel",
                  value: "Boykin Spaniel",
                  sortOrder: 140,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bracco Italiano",
                  value: "Bracco Italiano",
                  sortOrder: 141,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Braque d'Auvergne",
                  value: "Braque d'Auvergne",
                  sortOrder: 142,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Braque du Bourbonnais",
                  value: "Braque du Bourbonnais",
                  sortOrder: 143,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Braque Dupuy",
                  value: "Braque Dupuy",
                  sortOrder: 144,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Braque Francais De grande Taille",
                  value: "Braque Francais De grande Taille",
                  sortOrder: 145,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Braque Francais De Petite Talle",
                  value: "Braque Francais De Petite Talle",
                  sortOrder: 146,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Braque Saint-Germain",
                  value: "Braque Saint-Germain",
                  sortOrder: 147,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Brazilian Terrier",
                  value: "Brazilian Terrier",
                  sortOrder: 148,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Briard",
                  value: "Briard",
                  sortOrder: 149,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Briquet Griffon Vendeen",
                  value: "Briquet Griffon Vendeen",
                  sortOrder: 150,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Brittany",
                  value: "Brittany",
                  sortOrder: 151,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Broholmer",
                  value: "Broholmer",
                  sortOrder: 152,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bruno Jura Laufhund",
                  value: "Bruno Jura Laufhund",
                  sortOrder: 153,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Brussels Griffon",
                  value: "Brussels Griffon",
                  sortOrder: 154,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bulgarian Shepherd Dog",
                  value: "Bulgarian Shepherd Dog",
                  sortOrder: 155,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bull Terrier",
                  value: "Bull Terrier",
                  sortOrder: 156,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bull Terrier (Miniature)",
                  value: "Bull Terrier (Miniature)",
                  sortOrder: 157,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bulldog",
                  value: "Bulldog",
                  sortOrder: 158,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bulldog Campeiro",
                  value: "Bulldog Campeiro",
                  sortOrder: 159,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bullenbeisser",
                  value: "Bullenbeisser",
                  sortOrder: 160,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bullmastiff",
                  value: "Bullmastiff",
                  sortOrder: 161,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Bully Kutta",
                  value: "Bully Kutta",
                  sortOrder: 162,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cairn Terrier",
                  value: "Cairn Terrier",
                  sortOrder: 163,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cajun Squirrel Dog",
                  value: "Cajun Squirrel Dog",
                  sortOrder: 164,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Canaan Dog",
                  value: "Canaan Dog",
                  sortOrder: 165,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Canadian Eskimo Dog",
                  value: "Canadian Eskimo Dog",
                  sortOrder: 166,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cane Corso",
                  value: "Cane Corso",
                  sortOrder: 167,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cane de pastore Maremmano-Abruzzese",
                  value: "Cane de pastore Maremmano-Abruzzese",
                  sortOrder: 168,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Canis Panther",
                  value: "Canis Panther",
                  sortOrder: 169,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Canoe Dog",
                  value: "Canoe Dog",
                  sortOrder: 170,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cao da Serra de Aires",
                  value: "Cao da Serra de Aires",
                  sortOrder: 171,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cao de Castro Laboreiro",
                  value: "Cao de Castro Laboreiro",
                  sortOrder: 172,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cao Fila de Sao Miguel",
                  value: "Cao Fila de Sao Miguel",
                  sortOrder: 173,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Caravan Hound",
                  value: "Caravan Hound",
                  sortOrder: 174,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Carlin Pinscher",
                  value: "Carlin Pinscher",
                  sortOrder: 175,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Carolina Dog",
                  value: "Carolina Dog",
                  sortOrder: 176,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Carpathian Shepherd Dog",
                  value: "Carpathian Shepherd Dog",
                  sortOrder: 177,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Catahoula Bulldog",
                  value: "Catahoula Bulldog",
                  sortOrder: 178,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Catahoula Cur",
                  value: "Catahoula Cur",
                  sortOrder: 179,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Catahoula Leopard Dog",
                  value: "Catahoula Leopard Dog",
                  sortOrder: 180,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Catalan Sheepdog",
                  value: "Catalan Sheepdog",
                  sortOrder: 181,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Caucasian Mountain Dog",
                  value: "Caucasian Mountain Dog",
                  sortOrder: 182,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Caucasian Ovcharka",
                  value: "Caucasian Ovcharka",
                  sortOrder: 183,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cavalier King Charles Spaniel",
                  value: "Cavalier King Charles Spaniel",
                  sortOrder: 184,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Central Asia Shepherd Dog",
                  value: "Central Asia Shepherd Dog",
                  sortOrder: 185,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cesky Fousek",
                  value: "Cesky Fousek",
                  sortOrder: 186,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cesky Terrier",
                  value: "Cesky Terrier",
                  sortOrder: 187,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cevennes Shepherd",
                  value: "Cevennes Shepherd",
                  sortOrder: 188,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chart Polski",
                  value: "Chart Polski",
                  sortOrder: 189,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chesapeake Bay Retriever",
                  value: "Chesapeake Bay Retriever",
                  sortOrder: 190,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chien-gris",
                  value: "Chien-gris",
                  sortOrder: 191,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chiens Francaises Blanc et Noir",
                  value: "Chiens Francaises Blanc et Noir",
                  sortOrder: 192,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chiens Francaises Blanc et Orange",
                  value: "Chiens Francaises Blanc et Orange",
                  sortOrder: 193,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chiens Francaises Tricolore",
                  value: "Chiens Francaises Tricolore",
                  sortOrder: 194,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chihuahua",
                  value: "Chihuahua",
                  sortOrder: 195,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chihuahua, Longhaired",
                  value: "Chihuahua, Longhaired",
                  sortOrder: 196,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chilean Fox Terrier",
                  value: "Chilean Fox Terrier",
                  sortOrder: 197,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chinese Chongqing Dog",
                  value: "Chinese Chongqing Dog",
                  sortOrder: 198,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chinese Crested Dog",
                  value: "Chinese Crested Dog",
                  sortOrder: 199,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chinese Foo Dog",
                  value: "Chinese Foo Dog",
                  sortOrder: 200,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chinese Imperial Ch'in",
                  value: "Chinese Imperial Ch'in",
                  sortOrder: 201,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chinese Shar-Pei",
                  value: "Chinese Shar-Pei",
                  sortOrder: 202,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chinese Temple Dog",
                  value: "Chinese Temple Dog",
                  sortOrder: 203,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chinook",
                  value: "Chinook",
                  sortOrder: 204,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chippiparai",
                  value: "Chippiparai",
                  sortOrder: 205,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chortaj",
                  value: "Chortaj",
                  sortOrder: 206,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chow Chow",
                  value: "Chow Chow",
                  sortOrder: 207,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chow Mix",
                  value: "Chow Mix",
                  sortOrder: 208,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cierny Sery",
                  value: "Cierny Sery",
                  sortOrder: 209,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cimarron Urguayo",
                  value: "Cimarron Urguayo",
                  sortOrder: 210,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ciobanesc Romanesc Carpatin",
                  value: "Ciobanesc Romanesc Carpatin",
                  sortOrder: 211,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ciobanesc Romanesc Mirotic",
                  value: "Ciobanesc Romanesc Mirotic",
                  sortOrder: 212,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cirneco dell'Etna",
                  value: "Cirneco dell'Etna",
                  sortOrder: 213,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Clumber Spaniel",
                  value: "Clumber Spaniel",
                  sortOrder: 214,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cockapoo",
                  value: "Cockapoo",
                  sortOrder: 215,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cocker Spaniel",
                  value: "Cocker Spaniel",
                  sortOrder: 216,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Collie",
                  value: "Collie",
                  sortOrder: 217,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Collie Mix",
                  value: "Collie Mix",
                  sortOrder: 218,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Collie, Rough",
                  value: "Collie, Rough",
                  sortOrder: 219,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Collie, Smooth",
                  value: "Collie, Smooth",
                  sortOrder: 220,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Combai",
                  value: "Combai",
                  sortOrder: 221,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Continental Toy Spaniel,Papillon",
                  value: "Continental Toy Spaniel,Papillon",
                  sortOrder: 222,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Continental Toy Spaniel,Phalene",
                  value: "Continental Toy Spaniel,Phalene",
                  sortOrder: 223,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Coonhound",
                  value: "Coonhound",
                  sortOrder: 224,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cordoba Fighting Dog",
                  value: "Cordoba Fighting Dog",
                  sortOrder: 225,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Coton de Tulear",
                  value: "Coton de Tulear",
                  sortOrder: 226,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cretan Hound",
                  value: "Cretan Hound",
                  sortOrder: 227,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Croatian Sheepdog",
                  value: "Croatian Sheepdog",
                  sortOrder: 228,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cumberland Sheepdog",
                  value: "Cumberland Sheepdog",
                  sortOrder: 229,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cur",
                  value: "Cur",
                  sortOrder: 230,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Curly-coated Retriever",
                  value: "Curly-coated Retriever",
                  sortOrder: 231,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cursinu",
                  value: "Cursinu",
                  sortOrder: 232,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Cypro Kukur",
                  value: "Cypro Kukur",
                  sortOrder: 233,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Czechoslovakian Vlcak",
                  value: "Czechoslovakian Vlcak",
                  sortOrder: 234,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Czeslovakian Wolfdog",
                  value: "Czeslovakian Wolfdog",
                  sortOrder: 235,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dachshund",
                  value: "Dachshund",
                  sortOrder: 236,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dachshund- minature",
                  value: "Dachshund- minature",
                  sortOrder: 237,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dalmatian",
                  value: "Dalmatian",
                  sortOrder: 238,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dandie Dinmont Terrier",
                  value: "Dandie Dinmont Terrier",
                  sortOrder: 239,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Danish Broholmer",
                  value: "Danish Broholmer",
                  sortOrder: 240,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Danish/Swedish Farm Dog",
                  value: "Danish/Swedish Farm Dog",
                  sortOrder: 241,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Deutsche Bracke",
                  value: "Deutsche Bracke",
                  sortOrder: 242,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Deutscher Wachtelhund",
                  value: "Deutscher Wachtelhund",
                  sortOrder: 243,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dingo",
                  value: "Dingo",
                  sortOrder: 244,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dobermann Pinscher",
                  value: "Dobermann Pinscher",
                  sortOrder: 245,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dogo Argentino",
                  value: "Dogo Argentino",
                  sortOrder: 246,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dogo Cubano",
                  value: "Dogo Cubano",
                  sortOrder: 247,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dogo Guatemalteco",
                  value: "Dogo Guatemalteco",
                  sortOrder: 248,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dogo Sardesco",
                  value: "Dogo Sardesco",
                  sortOrder: 249,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dogue Brasileiro",
                  value: "Dogue Brasileiro",
                  sortOrder: 250,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dogue de Bordeaux",
                  value: "Dogue de Bordeaux",
                  sortOrder: 251,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dorset Olde Tyme Bulldogge",
                  value: "Dorset Olde Tyme Bulldogge",
                  sortOrder: 252,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Drentse Patrijshond",
                  value: "Drentse Patrijshond",
                  sortOrder: 253,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Drever",
                  value: "Drever",
                  sortOrder: 254,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dunker",
                  value: "Dunker",
                  sortOrder: 255,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dutch Shepherd Dog",
                  value: "Dutch Shepherd Dog",
                  sortOrder: 256,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Dutch Smoushond",
                  value: "Dutch Smoushond",
                  sortOrder: 257,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "East European Shepherd",
                  value: "East European Shepherd",
                  sortOrder: 258,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "East Siberian Laika",
                  value: "East Siberian Laika",
                  sortOrder: 259,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Elo",
                  value: "Elo",
                  sortOrder: 260,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Cocker Spaniel",
                  value: "English Cocker Spaniel",
                  sortOrder: 261,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Coonhound",
                  value: "English Coonhound",
                  sortOrder: 262,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Foxhound",
                  value: "English Foxhound",
                  sortOrder: 263,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Mastiff",
                  value: "English Mastiff",
                  sortOrder: 264,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Pointer",
                  value: "English Pointer",
                  sortOrder: 265,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Setter",
                  value: "English Setter",
                  sortOrder: 266,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Shepherd",
                  value: "English Shepherd",
                  sortOrder: 267,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Springer Spaniel",
                  value: "English Springer Spaniel",
                  sortOrder: 268,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Toy Spaniel",
                  value: "English Toy Spaniel",
                  sortOrder: 269,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Toy Terrier (Black & Tan)",
                  value: "English Toy Terrier (Black & Tan)",
                  sortOrder: 270,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English Water Spaniel",
                  value: "English Water Spaniel",
                  sortOrder: 271,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "English White Terrier",
                  value: "English White Terrier",
                  sortOrder: 272,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Entlebucher Mountain Dog",
                  value: "Entlebucher Mountain Dog",
                  sortOrder: 273,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Épagneul Bleu de Picardie",
                  value: "Épagneul Bleu de Picardie",
                  sortOrder: 274,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Epagneul Breton",
                  value: "Epagneul Breton",
                  sortOrder: 275,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Epagneul Picard",
                  value: "Epagneul Picard",
                  sortOrder: 276,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Epagneul Pont-audemer",
                  value: "Epagneul Pont-audemer",
                  sortOrder: 277,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Eskimo dog",
                  value: "Eskimo dog",
                  sortOrder: 278,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Estonian Hound",
                  value: "Estonian Hound",
                  sortOrder: 279,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Estrela Mountain Dog",
                  value: "Estrela Mountain Dog",
                  sortOrder: 280,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Eurasier",
                  value: "Eurasier",
                  sortOrder: 281,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Feist",
                  value: "Feist",
                  sortOrder: 282,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Field Spaniel",
                  value: "Field Spaniel",
                  sortOrder: 283,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Fila Brasileiro",
                  value: "Fila Brasileiro",
                  sortOrder: 284,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Finnish Hound",
                  value: "Finnish Hound",
                  sortOrder: 285,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Finnish Lapphund",
                  value: "Finnish Lapphund",
                  sortOrder: 286,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Finnish Spitz",
                  value: "Finnish Spitz",
                  sortOrder: 287,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Flat Coated Retriever",
                  value: "Flat Coated Retriever",
                  sortOrder: 288,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Formosan Mountain Dog",
                  value: "Formosan Mountain Dog",
                  sortOrder: 289,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Fox Hound",
                  value: "Fox Hound",
                  sortOrder: 290,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Fox Terrier",
                  value: "Fox Terrier",
                  sortOrder: 291,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Francais Blanc et Noir",
                  value: "Francais Blanc et Noir",
                  sortOrder: 292,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Francais Blanc et Orange",
                  value: "Francais Blanc et Orange",
                  sortOrder: 293,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Francais Tricolore",
                  value: "Francais Tricolore",
                  sortOrder: 294,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "French Brittany",
                  value: "French Brittany",
                  sortOrder: 295,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "French Bulldog",
                  value: "French Bulldog",
                  sortOrder: 296,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "French Spaniel",
                  value: "French Spaniel",
                  sortOrder: 297,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "French Wire-Haired Korthals Pointing Griffon",
                  value: "French Wire-Haired Korthals Pointing Griffon",
                  sortOrder: 298,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Galgo Espanol",
                  value: "Galgo Espanol",
                  sortOrder: 299,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gascon Saintongeois",
                  value: "Gascon Saintongeois",
                  sortOrder: 300,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Georgian Shepherd",
                  value: "Georgian Shepherd",
                  sortOrder: 301,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Longhaired Pointer",
                  value: "German Longhaired Pointer",
                  sortOrder: 302,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Pinscher",
                  value: "German Pinscher",
                  sortOrder: 303,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Rough-haired Pointer",
                  value: "German Rough-haired Pointer",
                  sortOrder: 304,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Sheeppoodle",
                  value: "German Sheeppoodle",
                  sortOrder: 305,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Shepherd Dog",
                  value: "German Shepherd Dog",
                  sortOrder: 306,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Shorthaired Pointer",
                  value: "German Shorthaired Pointer",
                  sortOrder: 307,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Spaniel",
                  value: "German Spaniel",
                  sortOrder: 308,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Spitz (Giant)",
                  value: "German Spitz (Giant)",
                  sortOrder: 309,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Spitz (Small)",
                  value: "German Spitz (Small)",
                  sortOrder: 310,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Spitz (Standard)",
                  value: "German Spitz (Standard)",
                  sortOrder: 311,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Spitz (Toy)",
                  value: "German Spitz (Toy)",
                  sortOrder: 312,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Wachtelhund",
                  value: "German Wachtelhund",
                  sortOrder: 313,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Wirehaired Pointer",
                  value: "German Wirehaired Pointer",
                  sortOrder: 314,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "German Wolfspitz",
                  value: "German Wolfspitz",
                  sortOrder: 315,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Giant Schnauzer",
                  value: "Giant Schnauzer",
                  sortOrder: 316,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Glen of Imaal Terrier",
                  value: "Glen of Imaal Terrier",
                  sortOrder: 317,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Golden Retriever",
                  value: "Golden Retriever",
                  sortOrder: 318,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Goldendoodle",
                  value: "Goldendoodle",
                  sortOrder: 319,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gordon Setter",
                  value: "Gordon Setter",
                  sortOrder: 320,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gran Mastin de Borinquen",
                  value: "Gran Mastin de Borinquen",
                  sortOrder: 321,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grand Anglo-Francais Tricolor Hound",
                  value: "Grand Anglo-Francais Tricolor Hound",
                  sortOrder: 322,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grand Anglo-Francais White and Black Hound",
                  value: "Grand Anglo-Francais White and Black Hound",
                  sortOrder: 323,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grand Anglo-Francais White and Orange Hound",
                  value: "Grand Anglo-Francais White and Orange Hound",
                  sortOrder: 324,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grand Basset Griffon Vendeen",
                  value: "Grand Basset Griffon Vendeen",
                  sortOrder: 325,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grand Bleu de Gascogne",
                  value: "Grand Bleu de Gascogne",
                  sortOrder: 326,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grand Gascon Saintongeois",
                  value: "Grand Gascon Saintongeois",
                  sortOrder: 327,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grand Griffon Vendeen",
                  value: "Grand Griffon Vendeen",
                  sortOrder: 328,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Great Anglo-Francais Tricolor Hound",
                  value: "Great Anglo-Francais Tricolor Hound",
                  sortOrder: 329,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Great Anglo-Francais White and Black Hound",
                  value: "Great Anglo-Francais White and Black Hound",
                  sortOrder: 330,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Great Anglo-Francais White and Orange Hound",
                  value: "Great Anglo-Francais White and Orange Hound",
                  sortOrder: 331,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Great Dane",
                  value: "Great Dane",
                  sortOrder: 332,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Great Pyrenees",
                  value: "Great Pyrenees",
                  sortOrder: 333,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Great Spitz",
                  value: "Great Spitz",
                  sortOrder: 334,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Greater Swiss Mountain Dog",
                  value: "Greater Swiss Mountain Dog",
                  sortOrder: 335,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Greek Harehound",
                  value: "Greek Harehound",
                  sortOrder: 336,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Greek Hound",
                  value: "Greek Hound",
                  sortOrder: 337,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Greek Shepherd",
                  value: "Greek Shepherd",
                  sortOrder: 338,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Greenland Dog",
                  value: "Greenland Dog",
                  sortOrder: 339,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Grey Blue Merle",
                  value: "Grey Blue Merle",
                  sortOrder: 340,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Greyhound",
                  value: "Greyhound",
                  sortOrder: 341,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Griffon Bleu de Gascogne",
                  value: "Griffon Bleu de Gascogne",
                  sortOrder: 342,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Griffon Bleu De Gascogne (petit)",
                  value: "Griffon Bleu De Gascogne (petit)",
                  sortOrder: 343,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Griffon Bruxellois",
                  value: "Griffon Bruxellois",
                  sortOrder: 344,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Griffon Fauve de Bretagne",
                  value: "Griffon Fauve de Bretagne",
                  sortOrder: 345,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Griffon Nivernais",
                  value: "Griffon Nivernais",
                  sortOrder: 346,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Guatemalan Bull Terrier",
                  value: "Guatemalan Bull Terrier",
                  sortOrder: 347,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gull Dong",
                  value: "Gull Dong",
                  sortOrder: 348,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gull Terr",
                  value: "Gull Terr",
                  sortOrder: 349,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hairless Dog",
                  value: "Hairless Dog",
                  sortOrder: 350,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hairless Khala",
                  value: "Hairless Khala",
                  sortOrder: 351,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Haldenstovare",
                  value: "Haldenstovare",
                  sortOrder: 352,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hamiltonstovare",
                  value: "Hamiltonstovare",
                  sortOrder: 353,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hanoverian Hound",
                  value: "Hanoverian Hound",
                  sortOrder: 354,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hare Indian Dog",
                  value: "Hare Indian Dog",
                  sortOrder: 355,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Harlequin Pinscher",
                  value: "Harlequin Pinscher",
                  sortOrder: 356,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Harrier",
                  value: "Harrier",
                  sortOrder: 357,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Havanese",
                  value: "Havanese",
                  sortOrder: 358,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hawaiian Poi Dog",
                  value: "Hawaiian Poi Dog",
                  sortOrder: 359,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hertha Pointer",
                  value: "Hertha Pointer",
                  sortOrder: 360,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Himalayan Sheepdog",
                  value: "Himalayan Sheepdog",
                  sortOrder: 361,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hokkaido",
                  value: "Hokkaido",
                  sortOrder: 362,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hortaya Borzaya",
                  value: "Hortaya Borzaya",
                  sortOrder: 363,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hound",
                  value: "Hound",
                  sortOrder: 364,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hound Mix",
                  value: "Hound Mix",
                  sortOrder: 365,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hovawart",
                  value: "Hovawart",
                  sortOrder: 366,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hungarian Hound",
                  value: "Hungarian Hound",
                  sortOrder: 367,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hungarian Wirehaired Vizsla",
                  value: "Hungarian Wirehaired Vizsla",
                  sortOrder: 368,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hunters Creek Feist",
                  value: "Hunters Creek Feist",
                  sortOrder: 369,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Husky Mix",
                  value: "Husky Mix",
                  sortOrder: 370,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Hygenhund",
                  value: "Hygenhund",
                  sortOrder: 371,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ibizan Hound",
                  value: "Ibizan Hound",
                  sortOrder: 372,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Icelandic Sheepdog",
                  value: "Icelandic Sheepdog",
                  sortOrder: 373,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Inca Hairless Dog",
                  value: "Inca Hairless Dog",
                  sortOrder: 374,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Indian Spitz",
                  value: "Indian Spitz",
                  sortOrder: 375,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Irish Red and White Setter",
                  value: "Irish Red and White Setter",
                  sortOrder: 376,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Irish Setter",
                  value: "Irish Setter",
                  sortOrder: 377,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Irish Staffordshire Bull Terrier",
                  value: "Irish Staffordshire Bull Terrier",
                  sortOrder: 378,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Irish Terrier",
                  value: "Irish Terrier",
                  sortOrder: 379,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Irish Water Spaniel",
                  value: "Irish Water Spaniel",
                  sortOrder: 380,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Irish Wolfhound",
                  value: "Irish Wolfhound",
                  sortOrder: 381,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Istrian Hound (smoothhaired)",
                  value: "Istrian Hound (smoothhaired)",
                  sortOrder: 382,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Istrian Hound,Wirehaired",
                  value: "Istrian Hound,Wirehaired",
                  sortOrder: 383,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Istrian Sheepdog",
                  value: "Istrian Sheepdog",
                  sortOrder: 384,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Istrian Shorthaired Hound",
                  value: "Istrian Shorthaired Hound",
                  sortOrder: 385,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Italian Greyhound",
                  value: "Italian Greyhound",
                  sortOrder: 386,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Italian Spinone",
                  value: "Italian Spinone",
                  sortOrder: 387,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Jack Russell Terrier",
                  value: "Jack Russell Terrier",
                  sortOrder: 388,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Jagdterrier",
                  value: "Jagdterrier",
                  sortOrder: 389,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Jamthund",
                  value: "Jamthund",
                  sortOrder: 390,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Japanese Chin",
                  value: "Japanese Chin",
                  sortOrder: 391,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Japanese Spitz",
                  value: "Japanese Spitz",
                  sortOrder: 392,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Japanese Terrier",
                  value: "Japanese Terrier",
                  sortOrder: 393,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Jindo",
                  value: "Jindo",
                  sortOrder: 394,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Jonangi",
                  value: "Jonangi",
                  sortOrder: 395,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Jura Neiderlaufhund",
                  value: "Jura Neiderlaufhund",
                  sortOrder: 396,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kai Ken",
                  value: "Kai Ken",
                  sortOrder: 397,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kaikadi",
                  value: "Kaikadi",
                  sortOrder: 398,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kangal Dog",
                  value: "Kangal Dog",
                  sortOrder: 399,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kanni",
                  value: "Kanni",
                  sortOrder: 400,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Karakachan Dog",
                  value: "Karakachan Dog",
                  sortOrder: 401,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Karelian Bear Dog",
                  value: "Karelian Bear Dog",
                  sortOrder: 402,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Karelo-finnish Laika",
                  value: "Karelo-finnish Laika",
                  sortOrder: 403,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Karst Shepherd",
                  value: "Karst Shepherd",
                  sortOrder: 404,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Keeshond",
                  value: "Keeshond",
                  sortOrder: 405,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kemmer Feist",
                  value: "Kemmer Feist",
                  sortOrder: 406,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kerry Beagle",
                  value: "Kerry Beagle",
                  sortOrder: 407,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kerry Blue Terrier",
                  value: "Kerry Blue Terrier",
                  sortOrder: 408,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "King Charles Spaniel",
                  value: "King Charles Spaniel",
                  sortOrder: 409,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "King Shepherd",
                  value: "King Shepherd",
                  sortOrder: 410,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kintamani",
                  value: "Kintamani",
                  sortOrder: 411,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kishu",
                  value: "Kishu",
                  sortOrder: 412,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kishu Ken",
                  value: "Kishu Ken",
                  sortOrder: 413,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Komondor",
                  value: "Komondor",
                  sortOrder: 414,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kooikerhondje",
                  value: "Kooikerhondje",
                  sortOrder: 415,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Koolie",
                  value: "Koolie",
                  sortOrder: 416,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Korean Jindo Dog",
                  value: "Korean Jindo Dog",
                  sortOrder: 417,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Korean Mastiff",
                  value: "Korean Mastiff",
                  sortOrder: 418,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Krasey Ovcar",
                  value: "Krasey Ovcar",
                  sortOrder: 419,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kromfohrlander",
                  value: "Kromfohrlander",
                  sortOrder: 420,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kugsha Dog",
                  value: "Kugsha Dog",
                  sortOrder: 421,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kunming Dog",
                  value: "Kunming Dog",
                  sortOrder: 422,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kuvasz",
                  value: "Kuvasz",
                  sortOrder: 423,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Kyi-Leo Dog",
                  value: "Kyi-Leo Dog",
                  sortOrder: 424,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Labradoodle",
                  value: "Labradoodle",
                  sortOrder: 425,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Labrador Husky",
                  value: "Labrador Husky",
                  sortOrder: 426,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Labrador Mix",
                  value: "Labrador Mix",
                  sortOrder: 427,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Labrador Retriever",
                  value: "Labrador Retriever",
                  sortOrder: 428,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lagotto Romagnolo",
                  value: "Lagotto Romagnolo",
                  sortOrder: 429,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lakeland Terrier",
                  value: "Lakeland Terrier",
                  sortOrder: 430,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lancashire Heeler",
                  value: "Lancashire Heeler",
                  sortOrder: 431,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Landseer (Continental-European type)",
                  value: "Landseer (Continental-European type)",
                  sortOrder: 432,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lapinporokoira",
                  value: "Lapinporokoira",
                  sortOrder: 433,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lapponian Herder",
                  value: "Lapponian Herder",
                  sortOrder: 434,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Larson Lakeview Bulldogge",
                  value: "Larson Lakeview Bulldogge",
                  sortOrder: 435,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Latvian Hound",
                  value: "Latvian Hound",
                  sortOrder: 436,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Leonberger",
                  value: "Leonberger",
                  sortOrder: 437,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Leopard Cur",
                  value: "Leopard Cur",
                  sortOrder: 438,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Levesque",
                  value: "Levesque",
                  sortOrder: 439,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lhasa Apso",
                  value: "Lhasa Apso",
                  sortOrder: 440,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lithuanian Hound",
                  value: "Lithuanian Hound",
                  sortOrder: 441,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Llewellin Setter",
                  value: "Llewellin Setter",
                  sortOrder: 442,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Long Hair Mix",
                  value: "Long Hair Mix",
                  sortOrder: 443,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lowchen",
                  value: "Lowchen",
                  sortOrder: 444,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Lundehund",
                  value: "Lundehund",
                  sortOrder: 445,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Luzerner Laufhund",
                  value: "Luzerner Laufhund",
                  sortOrder: 446,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Luzerner Neiderlaufhund",
                  value: "Luzerner Neiderlaufhund",
                  sortOrder: 447,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mackenzie River husky",
                  value: "Mackenzie River husky",
                  sortOrder: 448,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Magyar Agar",
                  value: "Magyar Agar",
                  sortOrder: 449,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mahratta Greyhound",
                  value: "Mahratta Greyhound",
                  sortOrder: 450,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Majestic Tree Hound",
                  value: "Majestic Tree Hound",
                  sortOrder: 451,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Maltese",
                  value: "Maltese",
                  sortOrder: 452,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Manchester Terrier",
                  value: "Manchester Terrier",
                  sortOrder: 453,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Maremma Sheepdog",
                  value: "Maremma Sheepdog",
                  sortOrder: 454,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Markiesje",
                  value: "Markiesje",
                  sortOrder: 455,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mastiff",
                  value: "Mastiff",
                  sortOrder: 456,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "McNab",
                  value: "McNab",
                  sortOrder: 457,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mexican Hairless Dog",
                  value: "Mexican Hairless Dog",
                  sortOrder: 458,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mi-Ki",
                  value: "Mi-Ki",
                  sortOrder: 459,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mi-Ki (IMR)",
                  value: "Mi-Ki (IMR)",
                  sortOrder: 460,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Miniature Australian Shepherd",
                  value: "Miniature Australian Shepherd",
                  sortOrder: 461,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Miniature Bull Terrier",
                  value: "Miniature Bull Terrier",
                  sortOrder: 462,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Miniature Fox Terrier",
                  value: "Miniature Fox Terrier",
                  sortOrder: 463,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Miniature Pinscher",
                  value: "Miniature Pinscher",
                  sortOrder: 464,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Miniature Schnauzer",
                  value: "Miniature Schnauzer",
                  sortOrder: 465,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mioritic",
                  value: "Mioritic",
                  sortOrder: 466,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Molossus",
                  value: "Molossus",
                  sortOrder: 467,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Montenegrin Mountain Hound",
                  value: "Montenegrin Mountain Hound",
                  sortOrder: 468,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Moscow Longhaired Toy Terrier",
                  value: "Moscow Longhaired Toy Terrier",
                  sortOrder: 469,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Moscow Vodolaz",
                  value: "Moscow Vodolaz",
                  sortOrder: 470,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Moscow Watchdog",
                  value: "Moscow Watchdog",
                  sortOrder: 471,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Moscow Water Dog",
                  value: "Moscow Water Dog",
                  sortOrder: 472,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mountain Cur",
                  value: "Mountain Cur",
                  sortOrder: 473,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mountain Feist",
                  value: "Mountain Feist",
                  sortOrder: 474,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mountain View Cur",
                  value: "Mountain View Cur",
                  sortOrder: 475,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mucuchies",
                  value: "Mucuchies",
                  sortOrder: 476,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mudhol Hound",
                  value: "Mudhol Hound",
                  sortOrder: 477,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Mudi",
                  value: "Mudi",
                  sortOrder: 478,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Munsterlander (Large)",
                  value: "Munsterlander (Large)",
                  sortOrder: 479,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Münsterländer, Small",
                  value: "Münsterländer, Small",
                  sortOrder: 480,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Murray River Curly Coated Retriever",
                  value: "Murray River Curly Coated Retriever",
                  sortOrder: 481,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Native American Indian Dog",
                  value: "Native American Indian Dog",
                  sortOrder: 482,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Neapolitan Mastiff",
                  value: "Neapolitan Mastiff",
                  sortOrder: 483,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Nebolish Mastiff",
                  value: "Nebolish Mastiff",
                  sortOrder: 484,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Nenets Herding Laika",
                  value: "Nenets Herding Laika",
                  sortOrder: 485,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "New Guinea Singing Dog",
                  value: "New Guinea Singing Dog",
                  sortOrder: 486,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "New Zealand Huntaway",
                  value: "New Zealand Huntaway",
                  sortOrder: 487,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Newfoundland",
                  value: "Newfoundland",
                  sortOrder: 488,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Norfolk Spaniel",
                  value: "Norfolk Spaniel",
                  sortOrder: 489,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Norfolk Terrier",
                  value: "Norfolk Terrier",
                  sortOrder: 490,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Norrbottenspets",
                  value: "Norrbottenspets",
                  sortOrder: 491,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "North Country Beagle",
                  value: "North Country Beagle",
                  sortOrder: 492,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Northeasterly Hauling Laika",
                  value: "Northeasterly Hauling Laika",
                  sortOrder: 493,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Northern Inuit Dog",
                  value: "Northern Inuit Dog",
                  sortOrder: 494,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Norwegian Buhund",
                  value: "Norwegian Buhund",
                  sortOrder: 495,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Norwegian Elkhound",
                  value: "Norwegian Elkhound",
                  sortOrder: 496,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Norwegian Lundehund",
                  value: "Norwegian Lundehund",
                  sortOrder: 497,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Norwich Terrier",
                  value: "Norwich Terrier",
                  sortOrder: 498,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Nova Scotia Duck Tolling Retriever",
                  value: "Nova Scotia Duck Tolling Retriever",
                  sortOrder: 499,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Old Croatian Sighthound",
                  value: "Old Croatian Sighthound",
                  sortOrder: 500,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Old Danish Bird Dog",
                  value: "Old Danish Bird Dog",
                  sortOrder: 501,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Old Danish Pointer",
                  value: "Old Danish Pointer",
                  sortOrder: 502,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Old English Sheepdog",
                  value: "Old English Sheepdog",
                  sortOrder: 503,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Old English Terrier",
                  value: "Old English Terrier",
                  sortOrder: 504,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Old German Shepherd Dog",
                  value: "Old German Shepherd Dog",
                  sortOrder: 505,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Olde Boston Bulldogge",
                  value: "Olde Boston Bulldogge",
                  sortOrder: 506,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Olde English Bulldogge",
                  value: "Olde English Bulldogge",
                  sortOrder: 507,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Old-Time Farm Shepherd",
                  value: "Old-Time Farm Shepherd",
                  sortOrder: 508,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ori Pei",
                  value: "Ori Pei",
                  sortOrder: 509,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Osterreichischer Kurzhaariger Pinscher",
                  value: "Osterreichischer Kurzhaariger Pinscher",
                  sortOrder: 510,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Other",
                  value: "Other",
                  sortOrder: 511,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Other Mixed Breed",
                  value: "Other Mixed Breed",
                  sortOrder: 512,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Other Purebred",
                  value: "Other Purebred",
                  sortOrder: 513,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Otterhound",
                  value: "Otterhound",
                  sortOrder: 514,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Owczarek Podhalanski",
                  value: "Owczarek Podhalanski",
                  sortOrder: 515,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pachon Navarro",
                  value: "Pachon Navarro",
                  sortOrder: 516,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Paisley Terrier",
                  value: "Paisley Terrier",
                  sortOrder: 517,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Papillon",
                  value: "Papillon",
                  sortOrder: 518,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Parson Russell Terrier",
                  value: "Parson Russell Terrier",
                  sortOrder: 519,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Patterdale Terrier",
                  value: "Patterdale Terrier",
                  sortOrder: 520,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pekingese",
                  value: "Pekingese",
                  sortOrder: 521,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pembroke Welsh Corgi",
                  value: "Pembroke Welsh Corgi",
                  sortOrder: 522,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Penciltail Feist",
                  value: "Penciltail Feist",
                  sortOrder: 523,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Perdiguero De Burgos",
                  value: "Perdiguero De Burgos",
                  sortOrder: 524,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Perdiguero Navarro",
                  value: "Perdiguero Navarro",
                  sortOrder: 525,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Perro de Pastor Mallorquin",
                  value: "Perro de Pastor Mallorquin",
                  sortOrder: 526,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Perro de Presa Canario",
                  value: "Perro de Presa Canario",
                  sortOrder: 527,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Perro de Presa Mallorquin",
                  value: "Perro de Presa Mallorquin",
                  sortOrder: 528,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Peruvian Hairless Dog",
                  value: "Peruvian Hairless Dog",
                  sortOrder: 529,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Peruvian Inca Orchid",
                  value: "Peruvian Inca Orchid",
                  sortOrder: 530,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Petit Basset Griffon Vendeen",
                  value: "Petit Basset Griffon Vendeen",
                  sortOrder: 531,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Petit Bleu de Gascogne",
                  value: "Petit Bleu de Gascogne",
                  sortOrder: 532,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Petit Brabancon",
                  value: "Petit Brabancon",
                  sortOrder: 533,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Petit Gascon Saintongeois",
                  value: "Petit Gascon Saintongeois",
                  sortOrder: 534,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Phalène",
                  value: "Phalène",
                  sortOrder: 535,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pharaoh Hound",
                  value: "Pharaoh Hound",
                  sortOrder: 536,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Phu Quoc ridgeback dog",
                  value: "Phu Quoc ridgeback dog",
                  sortOrder: 537,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Picardy Spaniel",
                  value: "Picardy Spaniel",
                  sortOrder: 538,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pit Bull Mix",
                  value: "Pit Bull Mix",
                  sortOrder: 539,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pitbull",
                  value: "Pitbull",
                  sortOrder: 540,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Plott Hound",
                  value: "Plott Hound",
                  sortOrder: 541,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Podenco Canario",
                  value: "Podenco Canario",
                  sortOrder: 542,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Podenco Ibicenco",
                  value: "Podenco Ibicenco",
                  sortOrder: 543,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Podengo Portugueso Grande",
                  value: "Podengo Portugueso Grande",
                  sortOrder: 544,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Podengo Portugueso Pequeno",
                  value: "Podengo Portugueso Pequeno",
                  sortOrder: 545,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pointer",
                  value: "Pointer",
                  sortOrder: 546,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pointer Mix",
                  value: "Pointer Mix",
                  sortOrder: 547,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Poitevin",
                  value: "Poitevin",
                  sortOrder: 548,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Polish Greyhound",
                  value: "Polish Greyhound",
                  sortOrder: 549,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Polish Hound",
                  value: "Polish Hound",
                  sortOrder: 550,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Polish Hunting Dog",
                  value: "Polish Hunting Dog",
                  sortOrder: 551,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Polish Lowland Sheepdog",
                  value: "Polish Lowland Sheepdog",
                  sortOrder: 552,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Polish Tatra Sheepdog",
                  value: "Polish Tatra Sheepdog",
                  sortOrder: 553,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pomeranian",
                  value: "Pomeranian",
                  sortOrder: 554,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pont-Audemer Spaniel",
                  value: "Pont-Audemer Spaniel",
                  sortOrder: 555,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Poodle",
                  value: "Poodle",
                  sortOrder: 556,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Poodle Mix",
                  value: "Poodle Mix",
                  sortOrder: 557,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Poodle, minature",
                  value: "Poodle, minature",
                  sortOrder: 558,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Poodle, toy",
                  value: "Poodle, toy",
                  sortOrder: 559,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Porcelaine",
                  value: "Porcelaine",
                  sortOrder: 560,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Portuguese Podengo",
                  value: "Portuguese Podengo",
                  sortOrder: 561,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Portuguese Pointer",
                  value: "Portuguese Pointer",
                  sortOrder: 562,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Portuguese Water Dog",
                  value: "Portuguese Water Dog",
                  sortOrder: 563,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Posavac Hound",
                  value: "Posavac Hound",
                  sortOrder: 564,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Potsdam Greyhound",
                  value: "Potsdam Greyhound",
                  sortOrder: 565,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Prazsky Krysavik",
                  value: "Prazsky Krysavik",
                  sortOrder: 566,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Puapua Lena Lena",
                  value: "Puapua Lena Lena",
                  sortOrder: 567,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pudelpointer",
                  value: "Pudelpointer",
                  sortOrder: 568,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pug",
                  value: "Pug",
                  sortOrder: 569,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Puggle",
                  value: "Puggle",
                  sortOrder: 570,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Puli",
                  value: "Puli",
                  sortOrder: 571,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pumi",
                  value: "Pumi",
                  sortOrder: 572,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pungsan Dog",
                  value: "Pungsan Dog",
                  sortOrder: 573,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pyrenean Mastiff",
                  value: "Pyrenean Mastiff",
                  sortOrder: 574,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Pyrenean Shepherd",
                  value: "Pyrenean Shepherd",
                  sortOrder: 575,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rafeiro do Alentejo",
                  value: "Rafeiro do Alentejo",
                  sortOrder: 576,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rajapalyam",
                  value: "Rajapalyam",
                  sortOrder: 577,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rampur Greyhound",
                  value: "Rampur Greyhound",
                  sortOrder: 578,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rastreador Brasileiro",
                  value: "Rastreador Brasileiro",
                  sortOrder: 579,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rat Terrier",
                  value: "Rat Terrier",
                  sortOrder: 580,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rat Terrier-miniature",
                  value: "Rat Terrier-miniature",
                  sortOrder: 581,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rat Terrier-toy",
                  value: "Rat Terrier-toy",
                  sortOrder: 582,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ratonero Bodeguero Andaluz",
                  value: "Ratonero Bodeguero Andaluz",
                  sortOrder: 583,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ratonero Valenciano",
                  value: "Ratonero Valenciano",
                  sortOrder: 584,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Redbone Coonhound",
                  value: "Redbone Coonhound",
                  sortOrder: 585,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rhodesian Ridgeback",
                  value: "Rhodesian Ridgeback",
                  sortOrder: 586,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Romagna Waterdog",
                  value: "Romagna Waterdog",
                  sortOrder: 587,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rottweiler",
                  value: "Rottweiler",
                  sortOrder: 588,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rottweiler Mix",
                  value: "Rottweiler Mix",
                  sortOrder: 589,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rough Collie",
                  value: "Rough Collie",
                  sortOrder: 590,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Running Walker Foxhound",
                  value: "Running Walker Foxhound",
                  sortOrder: 591,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Russian Black Terrier",
                  value: "Russian Black Terrier",
                  sortOrder: 592,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Russian Harlequin Hound",
                  value: "Russian Harlequin Hound",
                  sortOrder: 593,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Russian Hound",
                  value: "Russian Hound",
                  sortOrder: 594,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Russian Spaniel",
                  value: "Russian Spaniel",
                  sortOrder: 595,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Russian Toy",
                  value: "Russian Toy",
                  sortOrder: 596,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Russian Tsvetnaya Bolonka",
                  value: "Russian Tsvetnaya Bolonka",
                  sortOrder: 597,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Russo-European Laika",
                  value: "Russo-European Laika",
                  sortOrder: 598,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Saarlooswolfhond",
                  value: "Saarlooswolfhond",
                  sortOrder: 599,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sabueso Espanol",
                  value: "Sabueso Espanol",
                  sortOrder: 600,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sabueso Espanol De Monte",
                  value: "Sabueso Espanol De Monte",
                  sortOrder: 601,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sabueso Espanol Lebrero",
                  value: "Sabueso Espanol Lebrero",
                  sortOrder: 602,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sage Ashayeri",
                  value: "Sage Ashayeri",
                  sortOrder: 603,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sage Koochee",
                  value: "Sage Koochee",
                  sortOrder: 604,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sage Mazandarani",
                  value: "Sage Mazandarani",
                  sortOrder: 605,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Saint Bernard",
                  value: "Saint Bernard",
                  sortOrder: 606,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Saint-Usuge Spaniel",
                  value: "Saint-Usuge Spaniel",
                  sortOrder: 607,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sakhalin Husky",
                  value: "Sakhalin Husky",
                  sortOrder: 608,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Saluki",
                  value: "Saluki",
                  sortOrder: 609,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Samoyed",
                  value: "Samoyed",
                  sortOrder: 610,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sanshu Dog",
                  value: "Sanshu Dog",
                  sortOrder: 611,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sapsali",
                  value: "Sapsali",
                  sortOrder: 612,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sarplaninac",
                  value: "Sarplaninac",
                  sortOrder: 613,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Schapendoes",
                  value: "Schapendoes",
                  sortOrder: 614,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Schillerstovare",
                  value: "Schillerstovare",
                  sortOrder: 615,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Schipperke",
                  value: "Schipperke",
                  sortOrder: 616,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Schnauzer, Standard",
                  value: "Schnauzer, Standard",
                  sortOrder: 617,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Schweizer Laufhund",
                  value: "Schweizer Laufhund",
                  sortOrder: 618,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Schweizer Niederlaufhund",
                  value: "Schweizer Niederlaufhund",
                  sortOrder: 619,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Scotch Collie",
                  value: "Scotch Collie",
                  sortOrder: 620,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Scottish Deerhound",
                  value: "Scottish Deerhound",
                  sortOrder: 621,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Scottish Terrier",
                  value: "Scottish Terrier",
                  sortOrder: 622,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sealydale Terrier",
                  value: "Sealydale Terrier",
                  sortOrder: 623,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sealyham Terrier",
                  value: "Sealyham Terrier",
                  sortOrder: 624,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Segugio Italiano A Pelo Forte",
                  value: "Segugio Italiano A Pelo Forte",
                  sortOrder: 625,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Segugio Italiano A Pelo Raso",
                  value: "Segugio Italiano A Pelo Raso",
                  sortOrder: 626,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Seppala Siberian Sleddog",
                  value: "Seppala Siberian Sleddog",
                  sortOrder: 627,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Serbian Hound",
                  value: "Serbian Hound",
                  sortOrder: 628,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Serbian Tricolour Hound",
                  value: "Serbian Tricolour Hound",
                  sortOrder: 629,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Setter Mix",
                  value: "Setter Mix",
                  sortOrder: 630,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shar Pei",
                  value: "Shar Pei",
                  sortOrder: 631,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shar Pei-miniature",
                  value: "Shar Pei-miniature",
                  sortOrder: 632,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sheepdog",
                  value: "Sheepdog",
                  sortOrder: 633,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shepherd Mix",
                  value: "Shepherd Mix",
                  sortOrder: 634,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shetland Sheepdog",
                  value: "Shetland Sheepdog",
                  sortOrder: 635,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shiba Inu",
                  value: "Shiba Inu",
                  sortOrder: 636,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shih Tzu",
                  value: "Shih Tzu",
                  sortOrder: 637,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shika Inus",
                  value: "Shika Inus",
                  sortOrder: 638,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shikoku",
                  value: "Shikoku",
                  sortOrder: 639,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Shiloh Shepherd",
                  value: "Shiloh Shepherd",
                  sortOrder: 640,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Siberian Husky",
                  value: "Siberian Husky",
                  sortOrder: 641,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Siberian Laikas",
                  value: "Siberian Laikas",
                  sortOrder: 642,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Silken Windhound",
                  value: "Silken Windhound",
                  sortOrder: 643,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Silky Terrier",
                  value: "Silky Terrier",
                  sortOrder: 644,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Simaku",
                  value: "Simaku",
                  sortOrder: 645,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sinhala Hound",
                  value: "Sinhala Hound",
                  sortOrder: 646,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Skye Terrier",
                  value: "Skye Terrier",
                  sortOrder: 647,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sloughi",
                  value: "Sloughi",
                  sortOrder: 648,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Slovak Cuvac",
                  value: "Slovak Cuvac",
                  sortOrder: 649,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Slovakian Hound",
                  value: "Slovakian Hound",
                  sortOrder: 650,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Slovakian Rough-haired Pointer",
                  value: "Slovakian Rough-haired Pointer",
                  sortOrder: 651,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Slovensky Cuvac",
                  value: "Slovensky Cuvac",
                  sortOrder: 652,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Slovensky Hrubosrsty Stavac (Ohar)",
                  value: "Slovensky Hrubosrsty Stavac (Ohar)",
                  sortOrder: 653,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Slovenský Kopov",
                  value: "Slovenský Kopov",
                  sortOrder: 654,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Smalandsstovare",
                  value: "Smalandsstovare",
                  sortOrder: 655,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Small Greek Domestic Dog",
                  value: "Small Greek Domestic Dog",
                  sortOrder: 656,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Small Munsterlander",
                  value: "Small Munsterlander",
                  sortOrder: 657,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Small Spitz",
                  value: "Small Spitz",
                  sortOrder: 658,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Smooth Collie",
                  value: "Smooth Collie",
                  sortOrder: 659,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Smooth Fox Terrier",
                  value: "Smooth Fox Terrier",
                  sortOrder: 660,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Smooth Hair Mix",
                  value: "Smooth Hair Mix",
                  sortOrder: 661,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Soft Coated Wheaten Terrier",
                  value: "Soft Coated Wheaten Terrier",
                  sortOrder: 662,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Soft-coated Griffon",
                  value: "Soft-coated Griffon",
                  sortOrder: 663,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "South Russian Ovtcharka",
                  value: "South Russian Ovtcharka",
                  sortOrder: 664,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "South Russian Steppe Hound",
                  value: "South Russian Steppe Hound",
                  sortOrder: 665,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Southern Hound",
                  value: "Southern Hound",
                  sortOrder: 666,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spaniel",
                  value: "Spaniel",
                  sortOrder: 667,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spaniel Mix",
                  value: "Spaniel Mix",
                  sortOrder: 668,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spanish Bulldog",
                  value: "Spanish Bulldog",
                  sortOrder: 669,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spanish Mastiff",
                  value: "Spanish Mastiff",
                  sortOrder: 670,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spanish Water Dog",
                  value: "Spanish Water Dog",
                  sortOrder: 671,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spinone Italiano",
                  value: "Spinone Italiano",
                  sortOrder: 672,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spitz",
                  value: "Spitz",
                  sortOrder: 673,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sporting Lucas Terrier",
                  value: "Sporting Lucas Terrier",
                  sortOrder: 674,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Springer Spaniel",
                  value: "Springer Spaniel",
                  sortOrder: 675,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "St. Bernard",
                  value: "St. Bernard",
                  sortOrder: 676,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "St. Hubert Jura Laufhund",
                  value: "St. Hubert Jura Laufhund",
                  sortOrder: 677,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "St. John's Water Dog",
                  value: "St. John's Water Dog",
                  sortOrder: 678,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Stabyhoun",
                  value: "Stabyhoun",
                  sortOrder: 679,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Staffordshire Bull Terrier",
                  value: "Staffordshire Bull Terrier",
                  sortOrder: 680,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Standard Schnauzer",
                  value: "Standard Schnauzer",
                  sortOrder: 681,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Stellufstover",
                  value: "Stellufstover",
                  sortOrder: 682,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Stephens Stock Mountain Cur",
                  value: "Stephens Stock Mountain Cur",
                  sortOrder: 683,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Stumpy Tail Cattle Dog",
                  value: "Stumpy Tail Cattle Dog",
                  sortOrder: 684,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Styrian Coarse-haired Hound",
                  value: "Styrian Coarse-haired Hound",
                  sortOrder: 685,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Styrian Roughhaired Mnt.Hound",
                  value: "Styrian Roughhaired Mnt.Hound",
                  sortOrder: 686,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sussex Spaniel",
                  value: "Sussex Spaniel",
                  sortOrder: 687,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Swedish Lapphund",
                  value: "Swedish Lapphund",
                  sortOrder: 688,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Swedish Vallhund",
                  value: "Swedish Vallhund",
                  sortOrder: 689,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Swiss Shorthaired Pinscher",
                  value: "Swiss Shorthaired Pinscher",
                  sortOrder: 690,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tahltan Bear Dog",
                  value: "Tahltan Bear Dog",
                  sortOrder: 691,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Taigan",
                  value: "Taigan",
                  sortOrder: 692,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Talbot",
                  value: "Talbot",
                  sortOrder: 693,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tamaskan Dog",
                  value: "Tamaskan Dog",
                  sortOrder: 694,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tasy",
                  value: "Tasy",
                  sortOrder: 695,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tchiorny Terrier",
                  value: "Tchiorny Terrier",
                  sortOrder: 696,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Teddy Roosevelt Terrier",
                  value: "Teddy Roosevelt Terrier",
                  sortOrder: 697,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Telomian",
                  value: "Telomian",
                  sortOrder: 698,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tennessee Treeing Brindle",
                  value: "Tennessee Treeing Brindle",
                  sortOrder: 699,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tenterfield Terrier",
                  value: "Tenterfield Terrier",
                  sortOrder: 700,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Terrier",
                  value: "Terrier",
                  sortOrder: 701,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Terrier Mix",
                  value: "Terrier Mix",
                  sortOrder: 702,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Thai Bangkaew Dog",
                  value: "Thai Bangkaew Dog",
                  sortOrder: 703,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Thai Ridgeback",
                  value: "Thai Ridgeback",
                  sortOrder: 704,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tibetan Mastiff",
                  value: "Tibetan Mastiff",
                  sortOrder: 705,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tibetan Spaniel",
                  value: "Tibetan Spaniel",
                  sortOrder: 706,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tibetan Terrier",
                  value: "Tibetan Terrier",
                  sortOrder: 707,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Titan Terrier",
                  value: "Titan Terrier",
                  sortOrder: 708,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tornjak",
                  value: "Tornjak",
                  sortOrder: 709,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tosa",
                  value: "Tosa",
                  sortOrder: 710,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Toy Bulldog",
                  value: "Toy Bulldog",
                  sortOrder: 711,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Toy Fox Terrier",
                  value: "Toy Fox Terrier",
                  sortOrder: 712,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Toy Manchester Terrier",
                  value: "Toy Manchester Terrier",
                  sortOrder: 713,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Toy Mix",
                  value: "Toy Mix",
                  sortOrder: 714,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Toy Trawler Spaniel",
                  value: "Toy Trawler Spaniel",
                  sortOrder: 715,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Transylvanian Hound (short)",
                  value: "Transylvanian Hound (short)",
                  sortOrder: 716,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Transylvanian Hound,Tall",
                  value: "Transylvanian Hound,Tall",
                  sortOrder: 717,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Treeing Cur",
                  value: "Treeing Cur",
                  sortOrder: 718,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Treeing Feist",
                  value: "Treeing Feist",
                  sortOrder: 719,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Treeing Tennessee Brindle",
                  value: "Treeing Tennessee Brindle",
                  sortOrder: 720,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Treeing Walker Coonhound",
                  value: "Treeing Walker Coonhound",
                  sortOrder: 721,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Trigg Hound",
                  value: "Trigg Hound",
                  sortOrder: 722,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tweed Water Spaniel",
                  value: "Tweed Water Spaniel",
                  sortOrder: 723,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tyrolean Hound",
                  value: "Tyrolean Hound",
                  sortOrder: 724,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Unknown",
                  value: "Unknown",
                  sortOrder: 725,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Utonagan",
                  value: "Utonagan",
                  sortOrder: 726,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Valley Bulldog",
                  value: "Valley Bulldog",
                  sortOrder: 727,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Victorian Bulldog",
                  value: "Victorian Bulldog",
                  sortOrder: 728,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Villano de Las Encartaciones",
                  value: "Villano de Las Encartaciones",
                  sortOrder: 729,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Vizsla",
                  value: "Vizsla",
                  sortOrder: 730,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Volpino Italiano",
                  value: "Volpino Italiano",
                  sortOrder: 731,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Vucciriscu",
                  value: "Vucciriscu",
                  sortOrder: 732,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Weimaraner",
                  value: "Weimaraner",
                  sortOrder: 733,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Welsh Corgi",
                  value: "Welsh Corgi",
                  sortOrder: 734,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Welsh Corgi (Cardigan)",
                  value: "Welsh Corgi (Cardigan)",
                  sortOrder: 735,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Welsh Corgi (Pembroke)",
                  value: "Welsh Corgi (Pembroke)",
                  sortOrder: 736,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Welsh Hound",
                  value: "Welsh Hound",
                  sortOrder: 737,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Welsh Sheepdog",
                  value: "Welsh Sheepdog",
                  sortOrder: 738,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Welsh Springer Spaniel",
                  value: "Welsh Springer Spaniel",
                  sortOrder: 739,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Welsh Terrier",
                  value: "Welsh Terrier",
                  sortOrder: 740,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "West Highland White Terrier",
                  value: "West Highland White Terrier",
                  sortOrder: 741,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "West Russian Coursing Hound",
                  value: "West Russian Coursing Hound",
                  sortOrder: 742,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "West Siberian Laika",
                  value: "West Siberian Laika",
                  sortOrder: 743,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Westphalian Dachsbracke",
                  value: "Westphalian Dachsbracke",
                  sortOrder: 744,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Wetterhoun",
                  value: "Wetterhoun",
                  sortOrder: 745,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Whippet",
                  value: "Whippet",
                  sortOrder: 746,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Whippet (long Haired)",
                  value: "Whippet (long Haired)",
                  sortOrder: 747,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "White English Bulldog",
                  value: "White English Bulldog",
                  sortOrder: 748,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "White Shepard Dog",
                  value: "White Shepard Dog",
                  sortOrder: 749,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Wire Fox Terrier",
                  value: "Wire Fox Terrier",
                  sortOrder: 750,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Wirehaired Pointing Griffon",
                  value: "Wirehaired Pointing Griffon",
                  sortOrder: 751,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Wirehaired Vizsla",
                  value: "Wirehaired Vizsla",
                  sortOrder: 752,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Xoloitzcuintli",
                  value: "Xoloitzcuintli",
                  sortOrder: 753,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Xoloitzcuintli, Minature",
                  value: "Xoloitzcuintli, Minature",
                  sortOrder: 755,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Xoloitzcuintli, Standard",
                  value: "Xoloitzcuintli, Standard",
                  sortOrder: 756,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Yankee Bull Terrier",
                  value: "Yankee Bull Terrier",
                  sortOrder: 757,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Yankee Terrier",
                  value: "Yankee Terrier",
                  sortOrder: 758,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Yorkshire Terrier",
                  value: "Yorkshire Terrier",
                  sortOrder: 759,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Yugoslavian Mountain Hound",
                  value: "Yugoslavian Mountain Hound",
                  sortOrder: 760,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Yugoslavian Tricolour Hound",
                  value: "Yugoslavian Tricolour Hound",
                  sortOrder: 761,
                  description: "",
                  group: "",
                  default: false,
                },
              ],
              columnOrCustomFieldName: "dogBreed",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Birth Date",
              description: null,
              fieldName: "dogBirthDate",
              type: "date",
              defaultValue: "",
              sortOrder: 4,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter the dog's birth date.",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "dogBirthDate",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Sex",
              description: null,
              fieldName: "dogSex",
              type: "select",
              defaultValue: null,
              sortOrder: 5,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Male",
                  value: "male",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Female",
                  value: "female",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "dogSex",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Spayed or Neutered?",
              description: null,
              fieldName: "dogSpayedOrNeutered",
              type: "select",
              defaultValue: "",
              sortOrder: 6,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Yes",
                  value: "yes",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "No",
                  value: "no",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "dogSpayedOrNeutered",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Primary Color",
              description: null,
              fieldName: "dogPrimaryColor",
              type: "select",
              defaultValue: "",
              sortOrder: 7,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Apricot",
                  value: "Apricot",
                  sortOrder: 2,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black",
                  value: "Black",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Blonde",
                  value: "Blonde",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Brindle",
                  value: "Brindle",
                  sortOrder: 4,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Brown (Liver)",
                  value: "Brown (Liver)",
                  sortOrder: 5,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Buff",
                  value: "Buff",
                  sortOrder: 6,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chocolate",
                  value: "Chocolate",
                  sortOrder: 7,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gold",
                  value: "Gold",
                  sortOrder: 8,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gray (Blue Merle)",
                  value: "Gray (Blue Merle)",
                  sortOrder: 9,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Orange",
                  value: "Orange",
                  sortOrder: 10,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Merle",
                  value: "Merle",
                  sortOrder: 11,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Red",
                  value: "Red",
                  sortOrder: 11,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rust",
                  value: "Rust",
                  sortOrder: 12,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sable",
                  value: "Sable",
                  sortOrder: 13,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Salt and Pepper",
                  value: "Salt and Pepper",
                  sortOrder: 14,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Silver",
                  value: "Silver",
                  sortOrder: 15,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spotted (Patched)",
                  value: "Spotted (Patched)",
                  sortOrder: 16,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tan (Beige)",
                  value: "Tan (Beige)",
                  sortOrder: 17,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ticked",
                  value: "Ticked",
                  sortOrder: 18,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tri-Color",
                  value: "Tri-Color",
                  sortOrder: 19,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "White",
                  value: "White",
                  sortOrder: 20,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Yellow",
                  value: "Yellow",
                  sortOrder: 21,
                  description: "",
                  group: "",
                  default: false,
                },
              ],
              columnOrCustomFieldName: "dogPrimaryColor",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Secondary Color",
              description: null,
              fieldName: "dogSecondaryColor",
              type: "select",
              defaultValue: "",
              sortOrder: 8,
              size: "md",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Apricot",
                  value: "Apricot",
                  sortOrder: 2,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Blonde",
                  value: "Blonde",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Black",
                  value: "Black",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Brindle",
                  value: "Brindle",
                  sortOrder: 4,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Brown (Liver)",
                  value: "Brown (Liver)",
                  sortOrder: 5,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Buff",
                  value: "Buff",
                  sortOrder: 6,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Chocolate",
                  value: "Chocolate",
                  sortOrder: 7,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gold",
                  value: "Gold",
                  sortOrder: 8,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Gray (Blue Merle)",
                  value: "Gray (Blue Merle)",
                  sortOrder: 9,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Orange",
                  value: "Orange",
                  sortOrder: 10,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Red",
                  value: "Red",
                  sortOrder: 11,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Merle",
                  value: "Merle",
                  sortOrder: 11,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Rust",
                  value: "Rust",
                  sortOrder: 12,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Sable",
                  value: "Sable",
                  sortOrder: 13,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Salt and Pepper",
                  value: "Salt and Pepper",
                  sortOrder: 14,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Silver",
                  value: "Silver",
                  sortOrder: 15,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Spotted (Patched)",
                  value: "Spotted (Patched)",
                  sortOrder: 16,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tan (Beige)",
                  value: "Tan (Beige)",
                  sortOrder: 17,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Ticked",
                  value: "Ticked",
                  sortOrder: 18,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Tri-Color",
                  value: "Tri-Color",
                  sortOrder: 19,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "White",
                  value: "White",
                  sortOrder: 20,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Yellow",
                  value: "Yellow",
                  sortOrder: 21,
                  description: "",
                  group: "",
                  default: false,
                },
              ],
              columnOrCustomFieldName: "dogSecondaryColor",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Microchip Number",
              description: null,
              fieldName: "microchipNumber",
              type: "text",
              defaultValue: "",
              sortOrder: 9,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "microchipNumber",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Service Dog (Exempt from License Fee)",
              description: null,
              fieldName: "licenseExempt",
              type: "conditionalCheckbox",
              defaultValue: "false",
              sortOrder: 10,
              size: "full",
              info: null,
              google: true,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [
                {
                  fieldName: "exemptionDocuments",
                  condition: "isNotEmpty",
                },
              ],
              options: [],
              columnOrCustomFieldName: "licenseExempt",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Optional Details",
          sortOrder: 2,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Bio",
              description: null,
              fieldName: "dogBio",
              type: "text",
              defaultValue: "",
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "dogBio",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Markings",
              description: null,
              fieldName: "dogMarkings",
              type: "text",
              defaultValue: "",
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "dogMarkings",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Cat Friendly?",
              description: null,
              fieldName: "catFriendly",
              type: "select",
              defaultValue: null,
              sortOrder: 3,
              size: "md",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Yes",
                  value: "yes",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "No",
                  value: "no",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "catFriendly",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Dog Friendly?",
              description: null,
              fieldName: "dogFriendly",
              type: "select",
              defaultValue: null,
              sortOrder: 4,
              size: "md",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Yes",
                  value: "yes",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "No",
                  value: "no",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "dogFriendly",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Child Friendly?",
              description: null,
              fieldName: "childFriendly",
              type: "select",
              defaultValue: null,
              sortOrder: 5,
              size: "md",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Yes",
                  value: "yes",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "No",
                  value: "no",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "childFriendly",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: null,
          sortOrder: 5,
          conditionallyDisplay: [],
          elements: [
            {
              label: null,
              description: null,
              fieldName: "vaccineRecord",
              type: "fileDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: null,
              description: null,
              fieldName: "purebredCert",
              type: "fileDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [
        {
          api: "/coordinator/license/multiFormBuilder/newDogLicenseForm/draft/{entityId}",
          method: "PATCH",
          requestSlug: [
            {
              key: "{entityId}",
              valueLocation: "form",
              valueName: "entityId",
            },
          ],
          queryString: [],
          body: {
            type: "form-data",
            sendAllFormDataField: true,
            fields: [],
          },
          response: {
            success: {
              code: 204,
              waitUntilComplete: false,
              setAllToForm: false,
              navigate: null,
              fields: [],
            },
            error: {
              code: [400, 500],
              message: "Some error message here for toast",
            },
          },
        },
      ],
      onFormSubmit: [],
    },
    {
      title: "Health Information",
      optional: false,
      sortOrder: 4,
      conditionallyDisplay: [],
      sections: [
        {
          title: "Dog Health Information",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Vet / Hospital Name",
              description: null,
              fieldName: "veterinaryName",
              type: "text",
              defaultValue: null,
              sortOrder: 1,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter a valid name.",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: {
                value: "^[A-Za-z\\s'-.,]*$",
                message: "Please enter a valid name.",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "veterinaryName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant",
                  columnName: "participant_type_group_id",
                  value: 7,
                },
              ],
            },
            {
              label: "Veterinarian Name",
              description: null,
              fieldName: "veterinarianName",
              type: "text",
              defaultValue: null,
              sortOrder: 2,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: {
                value: "^[A-Za-z\\s'-.,]*$",
                message: "Please enter a valid name.",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "veterinarianName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Exempt from Vaccine",
              description: null,
              fieldName: "vaccineDatesExempt",
              type: "conditionalCheckbox",
              defaultValue: "false",
              sortOrder: 3,
              size: "full",
              info: null,
              google: true,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineDatesExempt",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Rabies Tag Number",
              description: null,
              fieldName: "rabiesTagNumber",
              type: "text",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z0-9\\s]*$",
                message: "Please enter a valid rabies tag number.",
              },
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "rabiesTagNumber",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Name",
              description: null,
              fieldName: "vaccineName",
              type: "select",
              defaultValue: "rabies",
              sortOrder: 5,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Rabies",
                  value: "rabies",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "vaccineName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Producer",
              description: null,
              fieldName: "vaccineProducer",
              type: "select",
              defaultValue: "",
              sortOrder: 6,
              size: "md",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Boehringer Ingelheim",
                  value: "Boehringer Ingelheim",
                  sortOrder: 2,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Elanco",
                  value: "Elanco",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Merial",
                  value: "Merial",
                  sortOrder: 4,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Merck Animal Health",
                  value: "Merck Animal Health",
                  sortOrder: 5,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Virbac",
                  value: "Virbac",
                  sortOrder: 6,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Zoetis",
                  value: "Zoetis",
                  sortOrder: 7,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Other",
                  value: "Other",
                  sortOrder: 8,
                  description: "",
                  group: "",
                  default: false,
                },
              ],
              columnOrCustomFieldName: "vaccineProducer",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Brand",
              description: null,
              fieldName: "vaccineBrand",
              type: "select",
              defaultValue: null,
              sortOrder: 7,
              size: "md",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 1",
                  value: "IMRAB 1",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 1 (TF)",
                  value: "IMRAB 1 (TF)",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 3",
                  value: "IMRAB 3",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 3 (TF)",
                  value: "IMRAB 3 (TF)",
                  sortOrder: 5,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "PUREVAX 1",
                  value: "PUREVAX 1",
                  sortOrder: 6,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "PUREVAX 3",
                  value: "PUREVAX 3",
                  sortOrder: 7,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "NOBIVAC 1",
                  value: "NOBIVAC 1",
                  sortOrder: 8,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "NOBIVAC 3 CA",
                  value: "NOBIVAC 3 CA",
                  sortOrder: 9,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "NOBIVAC 3",
                  value: "NOBIVAC 3",
                  sortOrder: 10,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "RABVAC 1",
                  value: "RABVAC 1",
                  sortOrder: 11,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "RABVAC 3",
                  value: "RABVAC 3",
                  sortOrder: 12,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "DEFENSOR 1",
                  value: "DEFENSOR 1",
                  sortOrder: 13,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "DEFENSOR 3",
                  value: "DEFENSOR 3",
                  sortOrder: 14,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "VANGUARD 1",
                  value: "VANGUARD 1",
                  sortOrder: 15,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "VANGUARD 3",
                  value: "VANGUARD 3",
                  sortOrder: 16,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Killed",
                  value: "Killed",
                  sortOrder: 17,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Killed Virus",
                  value: "Killed Virus",
                  sortOrder: 18,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "vaccineBrand",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Administered Date",
              description: null,
              fieldName: "vaccineAdministeredDate",
              type: "date",
              defaultValue: null,
              sortOrder: 8,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter a valid date.",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineAdministeredDate",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Expiration Date",
              description: null,
              fieldName: "vaccineDueDate",
              type: "date",
              defaultValue: null,
              sortOrder: 9,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter a valid date.",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineDueDate",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Number",
              description: null,
              fieldName: "vaccineLotNumber",
              type: "text",
              defaultValue: "",
              sortOrder: 10,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineLotNumber",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Expiration",
              description: null,
              fieldName: "vaccineLotExpirationDate",
              type: "date",
              defaultValue: null,
              sortOrder: 11,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineLotExpirationDate",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: null,
          sortOrder: 5,
          conditionallyDisplay: [],
          elements: [
            {
              label: null,
              description: null,
              fieldName: "serviceDogCert",
              type: "fileDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [
        {
          api: "/coordinator/license/multiFormBuilder/newDogLicenseForm/draft/{entityId}",
          method: "PATCH",
          requestSlug: [
            {
              key: "{entityId}",
              valueLocation: "form",
              valueName: "entityId",
            },
          ],
          queryString: [],
          body: {
            type: "form-data",
            sendAllFormDataField: true,
            fields: [],
          },
          response: {
            success: {
              code: 204,
              waitUntilComplete: false,
              setAllToForm: false,
              navigate: null,
              fields: [],
            },
            error: {
              code: [400, 500],
              message: "Some error message here for toast",
            },
          },
        },
      ],
      onFormSubmit: [],
    },
    {
      title: "License Duration",
      optional: false,
      sortOrder: 5,
      conditionallyDisplay: [],
      sections: [
        {
          title: null,
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: null,
              description: null,
              fieldName: "durationHeader",
              type: "html",
              defaultValue:
                "<h3>Please select the duration of the license.</h3>",
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: null,
              description: null,
              fieldName: "durationFooter",
              type: "html",
              defaultValue:
                "<p>Attention: Maximum of 3 year per dog license unless dogs are purebred.</p>",
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "License Duration",
              description: null,
              fieldName: "licenseDuration",
              type: "select",
              defaultValue: "",
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "1 Year",
                  value: "1",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "2 Years",
                  value: "2",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "3 Years",
                  value: "3",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "licenseDuration",
              tableName: "license",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "license_type",
                  columnName: "license_type_id",
                  value: 7,
                },
              ],
            },
          ],
        },
      ],
      onPageNext: [
        {
          api: "/coordinator/license/multiFormBuilder/newDogLicenseForm/draft/{entityId}",
          method: "PATCH",
          requestSlug: [
            {
              key: "{entityId}",
              valueLocation: "form",
              valueName: "entityId",
            },
          ],
          queryString: [],
          body: {
            type: "form-data",
            sendAllFormDataField: true,
            fields: [],
          },
          response: {
            success: {
              code: 204,
              waitUntilComplete: false,
              setAllToForm: false,
              navigate: null,
              fields: [],
            },
            error: {
              code: [400, 500],
              message: "Some error message here for toast",
            },
          },
        },
      ],
      onFormSubmit: [],
    },
    {
      title: "Confirm Information",
      optional: false,
      sortOrder: 6,
      conditionallyDisplay: [],
      sections: [
        {
          title: "Dog Information",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Tag Number",
              description: null,
              fieldName: "tagNumber",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Dog's Name",
              description: null,
              fieldName: "dogName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Breed",
              description: null,
              fieldName: "dogBreed",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Birth Date",
              description: null,
              fieldName: "dogBirthDate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Sex",
              description: null,
              fieldName: "dogSex",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 5,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Spayed or Neutered?",
              description: null,
              fieldName: "dogSpayedOrNeutered",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 6,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Primary Color",
              description: null,
              fieldName: "dogPrimaryColor",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 7,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Secondary Color",
              description: null,
              fieldName: "dogSecondaryColor",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 8,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Microchip Number",
              description: null,
              fieldName: "microchipNumber",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 9,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Optional Details",
          sortOrder: 2,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Bio",
              description: null,
              fieldName: "dogBio",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Markings",
              description: null,
              fieldName: "dogMarkings",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Cat Friendly?",
              description: null,
              fieldName: "catFriendly",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Dog Friendly?",
              description: null,
              fieldName: "dogFriendly",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Child Friendly?",
              description: null,
              fieldName: "childFriendly",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 5,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Health Information",
          sortOrder: 3,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Vet / Hospital Name",
              description: null,
              fieldName: "veterinaryName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Veterinarian Name",
              description: null,
              fieldName: "veterinarianName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Rabies Tag Number",
              description: null,
              fieldName: "rabiesTagNumber",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Name",
              description: null,
              fieldName: "vaccineName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Producer",
              description: null,
              fieldName: "vaccineProducer",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 5,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Administered Date",
              description: null,
              fieldName: "vaccineAdministeredDate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 6,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Brand",
              description: null,
              fieldName: "vaccineBrand",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 6,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Expiration Date",
              description: null,
              fieldName: "vaccineDueDate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 7,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Number",
              description: null,
              fieldName: "vaccineLotNumber",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 8,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Expiration",
              description: null,
              fieldName: "vaccineLotExpirationDate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 9,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [],
      onFormSubmit: [
        {
          api: "/license/license/final/{entityId}",
          method: "POST",
          requestSlug: [
            {
              key: "{entityId}",
              valueLocation: "form",
              valueName: "entityId",
            },
          ],
          queryString: [],
          body: null,
          response: {
            success: {
              code: 204,
              waitUntilComplete: true,
              setAllToForm: false,
              navigate: "addToCart,cart",
              fields: [],
            },
            error: {
              code: [400, 500],
              message: "Fail to create license",
            },
          },
        },
      ],
    },
  ],
  requiredFields: [
    {
      key: "licenseType",
      valueLocation: "slug",
      valueName: "entityType",
    },
    {
      key: "participantId",
      valueLocation: "queryString",
      valueName: "entityId",
    },
  ],
};
