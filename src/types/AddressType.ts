export interface Address {
  entityId: string;
  events: any[];
  houseNumber: string;
  streetName: string;
  streetAddress: string;
  streetAddress2: string | null;
  town: string | null;
  city: string;
  state: string;
  zip: string;
  fullAddress: string;
  latitude: number;
  longitude: number;
  participantAddressId: number | null;
  participantAddressTypeId: number | null;
  participantAddressType: string;
}