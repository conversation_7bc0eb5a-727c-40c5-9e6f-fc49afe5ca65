package com.scube.support.controller;

import com.scube.support.dto.ChatDto;
import com.scube.support.dto.ChatMessage;
import com.scube.support.dto.MessageDto;
import com.scube.support.enums.ChatStatus;
import com.scube.support.model.Chat;
import com.scube.support.service.ChatService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.james.mime4j.dom.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.UUID;

@AllArgsConstructor
@RestController
@Slf4j
@Validated
public class ChatController {

    private final ChatService chatService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @MessageMapping("/private-message") // /app/private-message
    public void sendPrivateMessage(@Payload ChatMessage message,
                                   Principal principal,
                                   SimpMessageHeaderAccessor headerAccessor) {
        String recipient = message.getTo(); // The recipient's email or identifier
        System.out.println("Message sent by: " + principal.getName() + " to: " + recipient);

        // Send the message to the correct user's queue
        // The recipient's queue will be /user/{recipient}/queue/messages
        messagingTemplate.convertAndSendToUser(
                recipient, // The recipient's username or email
                "/queue/messages", // The specific queue for this user
                message // The message object
        );
    }

    @GetMapping("/chats")
    public ResponseEntity<Page<ChatDto>> getAllChats(
            @RequestParam(required = true) UUID CurrentUserUuid,
            @RequestParam(required = false) ChatStatus status,
            @PageableDefault(size = 10) Pageable pageable){
        Page<ChatDto> chats = chatService.getAllChats(CurrentUserUuid, status, pageable);
        return ResponseEntity.ok(chats);
    }

    @PutMapping("/resolveChat/{chatUuid}")
    public ResponseEntity<Void> resolveChat(@PathVariable UUID chatUuid) {
        chatService.resolveChat(chatUuid);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/messages/{chatUuid}")
    public ResponseEntity<Page<MessageDto>> getConversation(
            @PathVariable UUID chatUuid,
            @RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize){
        Page<MessageDto> chats = chatService.getConversation(chatUuid, pageNumber, pageSize);
        return ResponseEntity.ok(chats);
    }

    @PostMapping("/joinConversation")
    public ResponseEntity<Void> joinConversation(
            @RequestParam(required = true) UUID CurrentUserUuid,
            @RequestParam(required = true) UUID chatUuid
    ) {
        chatService.joinConversation(chatUuid, CurrentUserUuid);
        return ResponseEntity.ok().build();
    }

}
