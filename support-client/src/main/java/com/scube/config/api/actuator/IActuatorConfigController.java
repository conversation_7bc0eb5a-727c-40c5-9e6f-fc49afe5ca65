package com.scube.config.api.actuator;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.AddToServiceClient;
import com.scube.client.annotation.GenerateHttpExchangeProxy;
import com.scube.client.annotation.HttpExchangeWebClient;
import org.springframework.web.service.annotation.GetExchange;

import java.util.Map;

@HttpExchangeWebClient(ServiceUrlConstant.CONFIG_SERVICE)
@AddToServiceClient(ServiceUrlConstant.CONFIG_SERVICE)
@GenerateHttpExchangeProxy
public interface IActuatorConfigController {
    @GetExchange("/actuator/health")
    ActuatorHealth health();

    @GetExchange("/actuator/health/**")
    Object healthPath();

    @GetExchange("/actuator")
    Map<String, Map<String, Link>> links();
}